import { useConfigStore } from "../stores/config.js";

/**
 *
 * @param {string} client
 * @param {string} endpoint
 * @param {Object} replaceMap
 * @returns {{endpoint: *, baseURL}}
 */
export const getEndpointFromStore = (client, endpoint, replaceMap = {}) => {
  const configStore = useConfigStore();
  return {
    endpoint: configStore.getClientEndpoint(client, endpoint, replaceMap),
    baseURL: configStore.getClientConfig(client).host,
  };
}

/**
 * Legacy function for backward compatibility with rootGetters
 * @deprecated Use getEndpointFromStore instead
 */
export const getEndpointFromStoreVuex = (rootGetters, client, endpoint) => {
  return {
    endpoint: rootGetters["config/getClientEndpoint"](client, endpoint),
    baseURL: rootGetters["config/getClientConfig"](client).host,
  };
}
