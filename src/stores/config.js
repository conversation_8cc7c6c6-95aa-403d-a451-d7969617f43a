import { computed, ref } from "vue";
import URLFormatter from "@/services/URLFormatter";
import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";
import { useUtils } from "../composables/useUtils.js";

export const useConfigStore = defineStore("Config", () => {
  const configRef = ref(null);
  const sidebarNavsRef = ref(null);
  const featuresRef = ref({});
  const isLoadingRef = ref(true);

  const { printConsole } = useUtils();

  const config = computed(() => configRef.value);
  const sidebarNavs = computed(() => sidebarNavsRef.value);
  const features = computed(() => featuresRef.value);

  const sidebarNavsFiltered = computed(() => {
    if (!sidebarNavsRef.value || import.meta.server) {
      return [];
    }

    const filtered = sidebarNavsRef.value
      .map((item) => ({
        ...item,
        isInSidebar: featuresRef.value[item.uniqueId] || false,
      }))
      .filter(({ isInSidebar }) => isInSidebar);

    return filtered;
  });

  const setConfigAsync = async (conf, navs) => {
    configRef.value = conf || null;
    sidebarNavsRef.value = navs || null;
    isLoadingRef.value = false;
  };

  const setFeatures = (featuresData) => {
    if (featuresData) {
      featuresRef.value = featuresData;
    }
  };

  const getConfig = () => {
    return configRef.value;
  };

  const getFeatures = () => {
    return featuresRef.value;
  };

  const getClientConfig = (clientKey) => {
    try {
      return configRef.value?.clients[clientKey] || {};
    } catch (err) {
      printConsole("Missing client config for key: " + clientKey);
      return {};
    }
  };

  const getClientEndpoint = (clientKey, endpointKey, replaceMap = {}) => {
    try {
      const template = configRef.value?.clients[clientKey]?.endpoints[endpointKey];
      return URLFormatter.urlFormat(template, replaceMap);
    } catch (e) {
      printConsole("Missing client's endpoint config for key: " + clientKey);
      return "";
    }
  };

  const getConfigEndpointHost = (clientKey, endpointKey) => {
    const client = configRef.value?.clients[clientKey];
    return {
      endpoint: URLFormatter.urlFormat(client?.endpoints?.[endpointKey]),
      baseURL: client?.host,
    };
  };

  const getICAMeal = (clientKey, endpointKey, gtin) => {
    try {
      const template = configRef.value?.clients[clientKey]?.endpoints[endpointKey];
      return URLFormatter.urlFormat(template, { gtin });
    } catch (e) {
      printConsole("Missing client's endpoint config for key: " + clientKey);
      return "";
    }
  };

  const getAppSocialLinks = (app, socialMedia) => {
    try {
      return configRef.value?.appSocialLinks?.[app]?.[socialMedia] || "";
    } catch (e) {
      printConsole("Missing app social links config for: " + app);
      return "";
    }
  };

  const getAppDownloadLinks = (app, store) => {
    try {
      return configRef.value?.appDownloadLinks?.[app]?.[store] || "";
    } catch (e) {
      printConsole("Missing app download links config for: " + app);
      return "";
    }
  };

  const getWebsiteLinks = (id) => {
    try {
      return configRef.value?.websiteLinks?.[id] || "";
    } catch (e) {
      printConsole("Missing website links config for: " + id);
      return "";
    }
  };

  const getFRWebsiteLinks = (id) => {
    try {
      return configRef.value?.frWebsiteLinks?.[id] || "";
    } catch (e) {
      printConsole("Missing FR website links config for: " + id);
      return "";
    }
  };

  const getSharableLinks = (socialMedia, replaceMap = {}) => {
    try {
      const template = configRef.value?.sharableLinks?.[socialMedia];
      return URLFormatter.urlFormat(template, replaceMap);
    } catch (e) {
      printConsole("Missing sharable links config for: " + socialMedia);
      return "";
    }
  };



  const fetchFeaturesAsync = async ({ isHotRefresh = false } = {}) => {
    if (Object.keys(featuresRef.value).length && !isHotRefresh) {
      return;
    }

    // Use Pinia userData store instead of Vuex
    const { useUserDataStore } = await import('./userData.js');
    const userDataStore = useUserDataStore();
    const project = userDataStore.getProject;

    if (!project?.id) {
      return;
    }

    try {
      const response = await useSimpleCustomFetch('', {}, 'flite', 'getFeatureConfig');

      if (response) {
        setFeatures(response);
      }
    } catch (error) {
      console.error('[IQ][useConfigStore] Error fetching features:', error);
      throw error;
    }
  };

  return {
    configRef,
    sidebarNavsRef,
    featuresRef,
    isLoadingRef,

    config,
    sidebarNavs,
    features,
    sidebarNavsFiltered,

    setConfigAsync,
    setFeatures,
    getConfig,
    getFeatures,
    getClientConfig,
    getClientEndpoint,
    getConfigEndpointHost,
    getICAMeal,
    getAppSocialLinks,
    getAppDownloadLinks,
    getWebsiteLinks,
    getFRWebsiteLinks,
    getSharableLinks,
    fetchFeaturesAsync,
  };
})
