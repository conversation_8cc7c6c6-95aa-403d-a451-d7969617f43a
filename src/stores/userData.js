import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useSimpleCustomFetch } from '../composables/useCustomFetch.js';
import { useConfig } from '../composables/useConfig.js';
import { useConfigStore } from './config.js';
import { STORAGE_KEY } from '../сonstants/storage-key.js';
import {
  getItemFromLocalStorage,
  setItemToLocalStorage,
  removeItemFromLocalStorage
} from '../services/local-storage-service.js';

export const useUserDataStore = defineStore('userData', () => {
  // State
  const project = ref(null);
  const projectList = ref([]);
  const langs = ref(null);
  const projectPermissions = ref(null);
  const projectUsers = ref([]);

  // Computed getters
  const getProject = computed(() => project.value);
  const getProjectList = computed(() => projectList.value);
  const getProjectById = computed(() => (id) => {
    return projectList.value?.find(project => project.id === id);
  });
  
  const getDefaultLang = computed(() => {
    return langs.value?.defaultLang ||
      getItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS)?.defaultLang ||
      (project.value?.id === "roche_main" ? "fr-FR" : "en-US");
  });
  
  const getAvailableLangs = computed(() => {
    return langs.value?.availableLangs ||
      getItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS)?.availableLangs ||
      null;
  });
  
  const getProjectPermissions = computed(() => {
    return projectPermissions.value?.permissions;
  });
  
  const getProjectUsers = computed(() => projectUsers.value);

  // Actions
  const setProject = (projectData) => {
    project.value = projectData;
    setItemToLocalStorage(STORAGE_KEY.USER_DATA_PROJECT, {
      id: projectData.id,
      displayName: projectData.displayName
    });
  };

  const setProjectList = (list) => {
    projectList.value = list;
  };

  const setLangs = (langsData) => {
    langs.value = langsData;
    setItemToLocalStorage(STORAGE_KEY.USER_DATA_LANGS, langsData);
  };

  const setProjectPermissions = (permissionsData) => {
    projectPermissions.value = permissionsData;
  };

  const setDefaultLang = (lang) => {
    if (!langs.value) {
      langs.value = {};
    }
    langs.value.defaultLang = lang;
    localStorage.setItem(
      STORAGE_KEY.USER_DATA_LANGS,
      JSON.stringify({ defaultLang: lang })
    );
  };

  const setProjectUsers = (users) => {
    projectUsers.value = users;
  };

  const setPermissions = (permissions) => {
    projectPermissions.value = permissions;
  };

  const clear = () => {
    project.value = null;
    projectList.value = [];
    langs.value = null;
    projectPermissions.value = null;
    projectUsers.value = [];
    removeItemFromLocalStorage(STORAGE_KEY.USER_DATA_PROJECT);
    removeItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS);
  };

  const updateLanguageOnProjectChange = () => {
    const newLang = project.value?.id === "roche_main" ? "fr-FR" : "en-US";
    setDefaultLang(newLang);
  };

  const postProjectAsync = async (payload) => {
    try {
      await useSimpleCustomFetch('', {
        method: 'POST',
        body: payload
      }, 'flite', 'postProject');
    } catch (error) {
      console.error('[IQ][useUserDataStore] Error in postProjectAsync:', error);
      throw error;
    }
  };

  const fetchLangsAsync = async ({ isHotRefresh } = {}) => {
    if (langs.value && !isHotRefresh) {
      return;
    }

    if (!project.value?.id) {
      return;
    }

    try {
      const { $axios } = useNuxtApp();
      const configStore = useConfigStore();

      const endpoint = configStore.getClientEndpoint("flite", "getLangConfig");
      const baseUrl = configStore.getClientConfig("flite").host;

      const response = await $axios.get(endpoint, {
        baseURL: baseUrl,
      });

      if (response?.data) {
        setLangs(response.data);
      }
    } catch (error) {
      console.error('[IQ][useUserDataStore] Error fetching languages:', error);
      throw error;
    }
  };

  const fetchProjectsAsync = async ({ isAdmin, isHotRefresh }) => {
    if (project.value && !isHotRefresh) {
      return;
    }

    const setUndefinedProject = () => {
      setProject({
        id: undefined,
        displayName: undefined,
      });
    };

    try {
      const { $axios } = useNuxtApp();
      const { config } = useConfig();
      const configStore = useConfigStore();

      const endpoint = configStore.getClientEndpoint("flite", "getProjectsList");
      const baseUrl = configStore.getClientConfig("flite").host;

      const response = await $axios.get(endpoint, {
        baseURL: baseUrl,
        headers: {
          [config.value.HEADERS.X_INNIT_PROJECT_ID]: config.value.DEFAULT_PROJECT_ID,
        },
        timeout: 120000
      });

      if (response?.data?.length) {
        setProjectList(response.data);

        const cachedProject = getItemFromLocalStorage(STORAGE_KEY.USER_DATA_PROJECT);
        const foundProject = response.data.find(p => p.id === cachedProject?.id);

        if (foundProject) {
          setProject(foundProject);
        } else {
          setProject(response.data[0]);
        }
      } else {
        setUndefinedProject();
      }
    } catch (error) {
      console.error('[IQ][useUserDataStore] Error fetching projects:', error);
      setUndefinedProject();
      throw error;
    }
  };

  const fetchUserPermissionsAsync = async ({ isHotRefresh }) => {
    if (projectPermissions.value && !isHotRefresh) {
      return;
    }

    try {
      const { $axios, $auth } = useNuxtApp();
      const configStore = useConfigStore();

      const endpoint = configStore.getClientEndpoint("flite", "postControl");
      const baseUrl = configStore.getClientConfig("flite").host;

      const response = await $axios.get(endpoint, {
        baseURL: baseUrl,
        params: {
          userId: $auth?.user?.value?.sub
        },
      });

      if (response?.data) {
        setProjectPermissions(response.data);
      }
    } catch (error) {
      console.error('[IQ][useUserDataStore] Error fetching user permissions:', error);
      throw error;
    }
  };

  return {
    // State
    project,
    projectList,
    langs,
    projectPermissions,
    projectUsers,

    // Computed
    getProject,
    getProjectList,
    getProjectById,
    getDefaultLang,
    getAvailableLangs,
    getProjectPermissions,
    getProjectUsers,

    // Actions
    setProject,
    setProjectList,
    setLangs,
    setProjectPermissions,
    setDefaultLang,
    setProjectUsers,
    setPermissions,
    clear,
    updateLanguageOnProjectChange,
    postProjectAsync,
    fetchLangsAsync,
    fetchProjectsAsync,
    fetchUserPermissionsAsync,
  };
});
