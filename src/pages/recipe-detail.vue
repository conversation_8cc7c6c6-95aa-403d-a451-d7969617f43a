<template>
  <client-only>
      <content-wrapper :is-body-loading="isDataLoading" wrapper-classes="padding-zero">
        <div
          v-show="
            !displayInstructionsPage && !isShowShopPreview && !isDataLoading
          "
          class="recipe-container"
        >
        <div class="recipe-header-section">
            <div class="button-section-container">
              <div class="back-button-section">
                <button
                  type="button"
                  class="btn-reset back-button"
                  v-if="!$route.query.fromPage"
                  @click="backButton(), toggleDropdownOff()"
                  data-test-id="recipe-detail-back-button"
                >
                  <img
                    alt="back"
                    class="back-master-list-arrow-image"
                    src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                  />
                  <span v-if="isBatchGenerator" class="back-to-recipes text-title-2">
                    {{ $t('BATCH_GENERATOR.BATCH_GENERATOR_TEXT') }}
                  </span>
                  <span v-else-if="isPageViewOverview" class="back-to-recipes text-title-2">
                    {{ $t('SWITCH_PAGES.BACK_TO_OVERVIEW') }}
                  </span>
                  <span v-else class="back-to-recipes text-title-2">
                    {{ $t('SWITCH_PAGES.BACK_TO_RECIPE_MASTER_LIST') }}
                  </span>
                </button>
              </div>
              <div class="button-section">
                <button
                  type="button"
                  @click="backButton(), toggleDropdownOff()"
                  @keydown="preventEnterAndSpaceKeyPress($event)"
                  class="btn-green-outline"
                >
                  {{ $t('BUTTONS.CANCEL_BUTTON') }}
                </button>
                <template v-if="!isShowSaveButton">
                  <button type="button"
                    v-if="!hasShowPublishButton"
                    @click="displaySavePopupAsync"
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                    :class="
                      recipeData?.state === 'publishing'
                        ? 'disabled-button btn-green'
                        : isCampaignModified &&
                          recipeName.trim() !== '' &&
                          (uploadPercentage == 0 || uploadPercentage == 100) &&
                          (uploadImagePercentage == 0 ||
                            uploadImagePercentage == 100) &&
                          !hasSlugCheckConfirm &&
                          !isRecipeImageGenerating &&
                          !hasDisableButtonDueToDimension
                        ? 'btn-green'
                        : 'disabled-button btn-green'
                      "
                  >
                    {{ recipeButtonState }}
                  </button>
                  <button type="button"
                    v-if="hasShowPublishButton"
                    @click="displayPublishPopupAsync('clickedOnPublish')"
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                    :class="
                      (recipeData.state === 'publishing' ||
                        uploadPercentage !== 0 ||
                        uploadPercentage != 100) &&
                        recipeName.trim() !== '' &&
                        !hasSlugCheckConfirm &&
                        hasDisableButtonDueToDimension
                        ? 'disabled-button btn-green'
                        : 'btn-green'
                    "
                    :disabled="isReadOnlyProvider(recipeData?.provider)"
                  >
                    {{ recipeButtonPublishState }}
                  </button>
                </template>
                <template v-else>
                  <button type="button"
                    @click="displayPublishPopupAsync('clickedOnPublish')"
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                    :class="
                      recipeData.state === 'publishing'
                        ? 'disabled-button btn-green'
                        : isCampaignModified &&
                          !hasSlugCheckConfirm &&
                          recipeName.trim() !== '' &&
                          (uploadPercentage === 0 || uploadPercentage === 100) &&
                          !isRecipeImageGenerating &&
                          !hasDisableButtonDueToDimension
                        ? 'btn-green'
                        : 'disabled-button btn-green'
                    "
                  >
                    {{ recipeButtonPublishState }}
                  </button>
                </template>
              </div>
            </div>
          </div>
          <div class="recipe-content-section">
            <div
              class="left-recipe-section"
              :class="{'disabled-section': isReadOnlyProvider(recipeData.provider)}"
            >
              <div class="new-form-section recipe-title-form-section">
                <recipe-title-section
                  v-model:recipeName="recipeName"
                  v-model:recipeSubtitle="recipeSubtitle"
                  :recipeID="recipeID"
                  :generatorPromptData="generatorPromptData"
                  :saveButtonEnable="saveButtonEnable"
                  :recipeData="recipeData"
                  :toggleDropdownOff="toggleDropdownOff"
                  :setRecipeTitleInput="setRecipeTitleInput"
                  :setRecipeSubtitleInput="setRecipeSubtitleInput"
                  :recipeVariantSelectedLanguage="recipeVariantSelectedLanguage"
                  :defaultLang="defaultLang"
                ></recipe-title-section>
              </div>
              <div class="new-form-section recipe-media-form-section" id="media-section">
                <div v-if="!uploadMediaPopup && !isRecipeImageGeneratorVisible" class="recipe-image-video-container">
                  <recipe-image-video-section
                    :imageList="recipeImageList"
                    :emptyImage="emptyImage"
                    :config="config"
                    :uploadImagePercentage="uploadImagePercentage"
                    :loadedImageSize="loadedImageSize"
                    :uploadImageSize="uploadImageSize"
                    :video="productVideo"
                    :isVideoLoaded="isVideoLoaded"
                    :loadedVideoSize="loadedVideoSize"
                    :uploadPercentage="uploadPercentage"
                    :uploadVideoSize="uploadVideoSize"
                    :isAllRecipeDataFilled="isAllRecipeDataFilled"
                    :isVideoPresent="isVideoPresent"
                    :isLinkPresent="isLinkPresent"
                    :hostNameVideoUrl="hostNameUrlLinkUpdate"
                    :linkURLImage="linkURLImage"
                    :recipeVariantSelectedLanguage="recipeVariantSelectedLanguage"
                    :defaultLang="defaultLang"
                    :openLinkPage="openLinkPage"
                    @setVideoDimensions="setVideoDimensions"
                    @mainImageSelected="mainImageSelected"
                    @deleteImage="deleteImage"
                    @selectUploadMediaOption="selectUploadMediaOption"
                    @deleteVideo="showDeleteVideoPopup"
                    @openVideoPopup="openVideoPopup"
                    @setMainImageWarning="setMainImageWarning"
                  ></recipe-image-video-section>
                </div>

                <div v-if="uploadMediaPopup && !isRecipeImageGeneratorVisible" class="upload-container">
                  <media-upload-section :uploadMediaName="uploadMediaName" @closeUploadMediaPopup="closeUploadMediaPopup"
                    @addButtonImageURLlink="addButtonImageURLlink" @addButtonURLlink="addButtonURLlink" :checkVideoPresent="isLinkPresent || isVideoPresent"
                    @uploadVideoFiles="uploadVideoFiles" @uploadImageFile="uploadImageFile">
                  </media-upload-section>
                </div>
                <div v-if="isRecipeImageGeneratorVisible" class="recipe-generator-container">
                  <recipe-generate-section
                    :isRecipeImageGenerating="isRecipeImageGenerating"
                    :recipeImageListLength="recipeImageList?.length"
                    @closeRecipeImageGeneratorPopup="closeRecipeImageGeneratorPopup"
                    @addSelectedGeneratedRecipeImage="addSelectedGeneratedRecipeImageAsync"
                  ></recipe-generate-section>
                </div>
              </div>
              <div class="new-form-section recipe-description-section">
                <recipeServingdescription
                    :servings="servings" :getRecipePrice="getRecipePrice" :availableServings="availableServings"
                    :recipePrice="recipePrice" @update:recipePrice="updateRecipePrice" @update:yieldData="updateYieldData"
                    @update:time="updateTimeData" :description="description" @update:description="updateDescriptionData"
                    @update:servings="handleUpdateServings" @update:availableServings="handleUpdateAvailableServings"
                    :recipeCurrency="recipeCurrency" :yieldData="yieldData" :hour="hour" :minute="minute" :second="second"
                    :prep-hour="prepHour" :prep-minute="prepMinute" :prep-second="prepSecond" :cook-hour="cookHour"
                    :cook-minute="cookMinute" :cook-second="cookSecond" :toggle-dropdown-off="toggleDropdownOff"
                    :recipe-data="recipeData" :recipe-variant-selected-language="recipeVariantSelectedLanguage"
                    :default-lang="defaultLang" :currency-dropdown-result="currencyDropdownResult" :handle-paste="handlePaste"
                    :save-button-enable="saveButtonEnable" :restrict-special-characters="restrictSpecialCharacters"
                    :disable-scroll="disableScroll" :prevent-non-numeric-input="preventNonNumericInput"
                    :restrict-numeric-input="restrictNumericInput" :validate-zero-input="validateZeroInput"
                    :check-time-data="checkTimeData" :calculate-total-time="calculateTotalTime"
                    :select-price-currency="selectPriceCurrency" :get-available-servings="getAvailableServings"
                    :remove-leading-zeros="removeLeadingZeros" :total-time-change="totalTimeChange"
                    :setRecipeAbstractInput="setRecipeAbstractInput"
                ></recipeServingdescription>
              </div>
            </div>

          <div class="right-recipe-section">

            <div class="new-form-section form-container publish-section" v-if="isShowPublishSection">
            <div
              :class="
                recipeVariantSelectedLanguage != defaultLang
                  ? 'disable-new'
                  : 'new'
              "
            >
              <div
                :class="
                  recipeData.state === 'publishing'
                    ? 'disable-recipe-detail form-title'
                    : 'form-title'
                "
              >
                <p
                  :class="
                    isSchedulePublishVisible
                      ? 'form-title-header publish-button-header disable'
                      : 'form-title-header publish-button-header'
                  "
                >
                  Publish
                </p>
                <span
                  class="publish-toggle-section"
                  :class="{
                    'simple-data-tooltip simple-data-tooltip-edge': tooltipText,
                  }"
                  :data-tooltip-text="tooltipText"
                >
                  <label
                    :class="
                      isSchedulePublishVisible
                        ? 'switch disable-switch'
                        : 'switch'
                    "
                    @click.prevent="
                      toggleSwitch();
                      toggleDropdownOff();
                    "
                  >
                    <input
                      :checked="
                        hasShowPublishButton || isShowSaveButton
                      "
                      type="checkbox"
                    />
                    <span class="slider round"></span>
                  </label>
                </span>
              </div>
              <div
                class="form-title-schedule-section"
                :class="
                  recipeVariantSelectedLanguage != defaultLang
                    ? 'disable-section'
                    : ''
                "
              >
                <div
                  v-if="
                    isAddScheduleButtonVisible &&
                    recipeSchedulePublishDate == '' &&
                    recipeScheduleEndDate == '' &&
                    !isSchedulingCalendarVisible &&
                    !isSchedulePublishVisible
                  "
                  class="add-schedule-main-section"
                  :class="{
                    'simple-data-tooltip': publishTooltipText
                  }"
                  @click="hasShowPublishButton ? '' : openSchedulePublish()"
                  :data-tooltip-text="publishTooltipText"
                >
                  <img
                    alt=""
                    :class="
                      hasShowPublishButton || isShowSaveButton
                        ? 'add-image disable'
                        : 'add-image'
                    "
                    src="@/assets/images/category-add.png"
                  />
                  <span
                    :class="
                      hasShowPublishButton || isShowSaveButton
                        ? 'add-sechedule-container text-h3 disable'
                        : 'add-sechedule-container text-h3'
                    "
                    >{{ $t('COMMON.ADD_SCHEDULE') }}</span
                  >
                </div>
                <div
                  v-if="isSchedulePublishVisible && !isAddScheduleButtonVisible"
                  class="schedule-publish-main-section"
                  id="recipe-calendar-section"
                >
                  <div class="schedule-publish-container">
                    <div class="schedule-image-title">
                      <img
                        alt=""
                        class="schedule-publish-image"
                        src="~/assets/images/schedule-publish-green.png"
                      />
                      <div class="schedule-publish-text-container">
                        Schedule Publish
                      </div>
                    </div>
                    <img
                      @click="closeSchedulePublish()"
                      alt=""
                      class="schedule-publish-cross"
                      src="~/assets/images/exit-gray.png"
                    />
                  </div>
                  <CalendarPicker
                    v-model="range"
                    :isRange="true"
                    :isInRecipeDetails="true"
                    :startDate="recipeSchedulePublishDate"
                    :endDate="recipeScheduleEndDate"
                    :hasPublishedDateTimeout="hasPublishedDateTimeout"
                    :hasUnpublishedDateTimeout="hasUnpublishedDateTimeout"
                    @update:model-value="handleDateClick"
                    :getErrorMessage="getErrorMessage"
                    :viewMoreDetail="viewMoreDetail"
                  />
                </div>
              </div>
            </div>
            <div class="form-title recipe-id">
              <p class="form-title-header">Recipe ID</p>
              <span>{{ recipeID }}</span>
            </div>
            <div class="form-title slug-id">
              <p class="form-title-header">Slug</p>
              <div
                class="slug-main-container"
                :class="{
                  'simple-data-tooltip': isSlugToolTip && slugEdit,
                }"
                :data-tooltip-text="isSlugToolTip && slugEdit"
              >
                <input
                  class="form-slug-input-text"
                  id="slugField"
                  autocomplete="off"
                  v-model.trim="slugEdit"
                  v-if="!isReadOnlyProvider(recipeData?.provider)"
                  @keypress="restrictToAlphanumeric($event)"
                  @mouseover="checkSlugLength()"
                  @mouseleave="hideSlugToolTip()"
                  @input="saveButtonEnable(), hideSlugToolTip(), debounceInput()"
                />
                <div
                  v-else-if="isReadOnlyProvider(recipeData?.provider)"
                  class="form-slug-input-text"
                  @mouseover="checkSlugLength()"
                  @mouseleave="hideSlugToolTip()"
                  id="slugField"
                >
                  {{ slugEdit }}
                </div>
              </div>
              <div
                v-if="
                  slugData &&
                  slugData[recipeVariantSelectedLanguage] &&
                  slugData[recipeVariantSelectedLanguage].slugStatus
                "
              >
                <b style="color: red; font-size: 12px">
                  This Slug Already exists</b
                >
              </div>
            </div>

            <div
              class="recipe-preview"
              :class="{
                'disable-recipe-detail': isPreviewDeleteBtnDisabled
              }"
            >
              <span @click="openRecipeComponent()">
                <img
                  alt="spoon icon"
                  src="@/assets/images/spoon.png"
                  width="18"
                  height="20"
                />
                {{ $t('COMMON.RECIPE_PREVIEW') }}
              </span>
            </div>
            <div
              :class="
                recipeData.state === 'publishing'
                  ? 'disable-recipe-detail description-section'
                  : 'description-section'
              "
            >
              <button
                type="button"
                class="delete btn-reset"
                :class="{
                  'disable-delete-btn': isPreviewDeleteBtnDisabled,
                  'simple-data-tooltip': isRecipeIncludeInHero,
                  'inactive-button': isRecipeIncludeInHero
                }"
                :disabled="isRecipeIncludeInHero"
                :data-tooltip-text="isRecipeIncludeInHero && $t('COMMON.RECIPE_IN_HERO')"
                @click="deleteModalVisible(null, null, null, null)"
              >
                <img
                  alt="trash icon"
                  src="@/assets/images/delete-icon.png"
                  width="18"
                  height="20"
                />
                <span>{{ $t('COMMON.DELETE_RECIPE') }}</span>
              </button>
              <div
                v-if="recipeVariantSelectedLanguage != defaultLang"
                class="delete-recipe-variant-container"
              >
                <span
                  class="delete-recipe-variant"
                  @click="deleteRecipeVariantPopUp()"
                >
                  <img
                    alt=""
                    src="@/assets/images/delete-icon.png"
                    width="18"
                    height="20"
                  />
                  <p>Remove Recipe Variant</p>
                </span>
              </div>
            </div>
          </div>
          <div
            class="new-form-section recipe-variant-main-container form-container publish-section"
            v-if="
              isShowPublishSection &&
              isRecipeVariant &&
              finalAvailableLangs &&
              finalAvailableLangs?.length > 1
            "
          >
            <div class="recipe-variant">
              <span>Recipe Variant</span>
            </div>
            <div class="recipe-variant-language-container">
              <div
                v-for="(result, index) in availableLang"
                :key="index"
                v-show="
                  availableLang?.length == 1 && result && result.default_check
                "
                class="recipe-variant-language"
              >
                <img alt="" src="@/assets/images/us-flag.png" />
                <span class="default-lang-title text-title-2 font-normal"
                  >{{
                    result && result.language_name ? result.language_name : ""
                  }}<span v-if="result && result.default_check">
                    (default)</span
                  ></span
                >
              </div>
              <div
                v-if="availableLang?.length > 1"
                @click="showAvailableLanguage()"
                class="recipe-variant-drop-down"
              >
                <div class="recipe-variant-drop-down-container">
                  <div
                    class="recipe-variant-container"
                    v-for="(result, index) in availableLang"
                    :key="index"
                  >
                    <div
                      v-if="
                        result &&
                        result.language === recipeVariantSelectedLanguage
                      "
                    >
                      <img
                        alt=""
                        class="recipe-variant-drop-down-image"
                        :src="
                          result && result.languageFlag
                            ? result.languageFlag
                            : ''
                        "
                      />
                      <span class="recipe-variant-drop-down-data text-title-2 font-normal">
                        {{
                          result && result.language_name
                            ? result.language_name
                            : ""
                        }}<span v-if="result && result.default_check">
                          (default)</span
                        >
                      </span>
                    </div>
                  </div>
                  <img
                    alt=""
                    class="recipe-variant-dropdown-icon"
                    src="@/assets/images/arrow-right.png"
                    :class="{
                      rotate: availableLanguageDropdown,
                    }"
                  />
                </div>
                <ul
                  v-if="availableLanguageDropdown"
                  class="recipe-variant-autocomplete-results"
                >
                  <div>
                    <li
                      v-for="(result, index) in availableLang"
                      :key="index"
                      :class="{
                        'recipe-variant-autocomplete-result': true,
                        'is-active':
                          recipeVariantSelectedLanguage == result.language,
                      }"
                      @click="chooseLanguageAsync(result.language)"
                    >
                      <div class="recipe-variant-drop-down-image-title">
                        <img
                          alt=""
                          class="drop-down-recipe-variant-drop-down-image"
                          :src="
                            result && result.languageFlag
                              ? result.languageFlag
                              : ''
                          "
                        />
                        <span class="drop-down-recipe-variant-drop-down-data text-title-2 font-normal"
                          >{{
                            result && result.language_name
                              ? result.language_name
                              : ""
                          }}<span v-if="result.default_check">
                            (default)</span
                          ></span
                        >
                      </div>
                    </li>
                  </div>
                </ul>
              </div>
            </div>
            <div
              v-if="displayAddVariantButton"
              class="add-recipe-variant-button"
              :class="
                isReadOnlyProvider(recipeData?.provider)
                  ? 'disable-variant-button'
                  : 'add-recipe-variant-button'
              "
            >
              <span
                class="add-recipe-variant"
                @click="recipeVariantLanguageModal()"
              >
                <img alt="" src="@/assets/images/add_icon.png"/>
                <p>ADD NEW</p>
              </span>
            </div>
            <div
              v-if="
                recipeVariantSelectedLanguage != defaultLang && recipeIsin == ''
              "
              class="delete-recipe-variant-container"
            >
              <span
                class="delete-recipe-variant"
                @click="deleteRecipeVariantPopUp()"
              >
                <img
                  alt=""
                  src="@/assets/images/delete-icon.png"
                  width="18"
                  height="20"
                />
                <p>Remove Recipe Variant</p>
              </span>
            </div>
          </div>
          <div
            class="new-form-section form-container publish-section"
            v-if="!isShowPublishSection"
          >
            <div
              v-if="isAddScheduleButtonVisible && !isSchedulingCalendarVisible"
              class="add-schedule-main-section"
              @click="hasShowPublishButton ? '' : openSchedulePublishNew()"
              :class="{
                'simple-data-tooltip': !isAllRequiredFieldFilled() && !isSchedulePublishVisible,
              }"
              :data-tooltip-text="(!isAllRequiredFieldFilled() && !isSchedulePublishVisible) && requiredFieldsTooltip"
            >
              <img
                alt=""
                :class="
                  isAllRequiredFieldFilled() ? 'add-image' : 'add-image disable'
                "
                src="@/assets/images/category-add.png"
              />
              <span
                :class="
                  isAllRequiredFieldFilled()
                    ? 'add-sechedule-container text-h3'
                    : 'add-sechedule-container text-h3 disable'
                "
              >
                {{ $t('COMMON.ADD_SCHEDULE') }}
              </span>
            </div>
            <div
              v-if="isSchedulePublishVisible && !isAddScheduleButtonVisible"
              class="schedule-publish-main-section"
              id="recipe-calendar-section"
            >
              <div class="schedule-publish-container">
                <div class="schedule-image-title">
                  <img
                    alt=""
                    class="schedule-publish-image"
                    src="~/assets/images/schedule-publish-green.png"
                  />
                  <div class="schedule-publish-text-container">
                    Schedule Publish
                  </div>
                </div>
                <img
                  @click="closeSchedulePublish()"
                  alt=""
                  class="schedule-publish-cross"
                  src="~/assets/images/exit-gray.png"
                />
              </div>
              <CalendarPicker
                v-model="range"
                :isRange="true"
                :isInRecipeDetails="true"
                :startDate="recipeSchedulePublishDate"
                :endDate="recipeScheduleEndDate"
                @update:model-value="handleDateClick"
              />
            </div>
            <div class="slug-id">
              <p class="slug-title-header">Slug</p>
              <div
                class="slug-main-container"
                :class="{
                  'simple-data-tooltip': isSlugToolTip && slugEdit,
                }"
                :data-tooltip-text="isSlugToolTip && slugEdit"
              >
                <input
                  class="form-slug-input-text"
                  id="slugField"
                  autocomplete="off"
                  v-model.trim="slugEdit"
                  @keypress="restrictToAlphanumeric($event)"
                  @mouseover="checkSlugLength()"
                  @mouseleave="hideSlugToolTip()"
                  @input="saveButtonEnable(), hideSlugToolTip(), debounceInput()"
                />
              </div>
            </div>
            <div v-if="!hasSlugCheckConfirm && hasSlugExist">
              <b style="color: red; font-size: 12px">
                This Slug Already exists</b
              >
            </div>
          </div>
          <div class="new-form-section author-publisher-form-container author-publisher-section">
            <div class="form-title author-section">
              <p class="author-form-title-header text-h3">Author</p>
              <div
                class="author-section-main"
                :class="{
                  'simple-data-tooltip': isRecipeAuthorFocus,
                }"
                :data-tooltip-text="isRecipeAuthorFocus && recipeAttributionAuthor"
              >
                <input
                  type="text"
                  class="form-author-input-text text-title-2 font-normal"
                  id="attributionAuthor"
                  autocomplete="off"
                  v-model="recipeAttributionAuthor"
                  @input="saveButtonEnable(), hideRecipeAuthorTip()"
                  v-if="
                    !isReadOnlyProvider(recipeData?.provider) &&
                    recipeVariantSelectedLanguage == defaultLang
                  "
                  @mouseover="checkEditRecipeAuthor()"
                  @mouseleave="hideRecipeAuthorTip()"
                  @keydown="hideRecipeAuthorTip()"
                />
                <div
                  v-else-if="
                    isReadOnlyProvider(recipeData?.provider) ||
                    recipeVariantSelectedLanguage != defaultLang
                  "
                  class="form-author-input-text text-title-2 font-normal"
                  id="attributionAuthor"
                  @mouseover="checkEditRecipeAuthor()"
                  @mouseleave="hideRecipeAuthorTip()"
                >
                  {{ recipeAttributionAuthor }}
                </div>
              </div>
            </div>
            <div v-show="isDisplayPublisher" class="form-title author-section">
              <div class="publisher-form-main-container">
                <p class="publisher-form-title-header">Publisher</p>
                <div
                  class="publisher-form-info-icon-main simple-data-tooltip"
                  :data-tooltip-text="addPublisherTooltip"
                >
                  <img
                    alt=""
                    class="info-icon"
                    src="@/assets/images/informationSymbol.png"
                  />
                </div>
              </div>
              <p class="none-publisher text-light-h3" v-show="!publisherDataList?.length">
                None
              </p>
              <div class="publisher-drop-down-main-section">
                <div
                  class="publisher-drop-down"
                  v-show="publisherDataList?.length > 0"
                  @click="showPublisherData()"
                  :class="{
                    'disable-recipe-detail-content':
                      isReadOnlyProvider(recipeData?.provider) ||
                      recipeVariantSelectedLanguage != defaultLang,
                  }"
                >
                  <div class="publisher-drop-down-container">
                    <div class="publisher-container">
                      <div class="publisher-drop-down-data">
                        <div
                          v-if="selectedPublisherName !== $t('COMMON.SELECT_ONE')"
                          class="publisher-image-container"
                        >
                          <img
                            alt=""
                            class="publisher-image"
                            :src="selectedPublisherImage || defaultOrganizationsImage"
                          />
                        </div>
                        <div class="publisher-text-container text-title-2 font-normal">
                          {{ selectedPublisherName }}
                        </div>
                      </div>
                    </div>
                    <img
                      alt=""
                      class="publisher-dropdown-icon"
                      src="@/assets/images/arrow-right.png"
                      :class="{
                        rotate: publisherDropdownResult,
                      }"
                    />
                  </div>
                </div>
                <ul
                  v-show="publisherDropdownResult"
                  class="publisher-autocomplete-results"
                  id="selectOneText"
                >
                  <div @click="resetPublisher()" class="select-one-text text-title-2 font-normal">
                    Select One
                  </div>
                  <li
                    v-for="(result, index) in publisherDataList"
                    :key="index"
                    :class="{
                      'publisher-autocomplete-result text-title-2 font-normal': true,
                      'is-active': selectedPublisherName == result?.name,
                    }"
                    @click="selectedPublisherProduct(result)"
                  >
                    <div class="publisher-image">
                      <img
                        alt=""
                        class="publisher-result-icon"
                        :src="
                          result && result.image && result.image.url
                            ? result.image.url
                            : defaultOrganizationsImage
                        "
                      />
                    </div>
                    <span class="publisher-result-name">{{
                      result && result.name
                    }}</span>
                  </li>
                </ul>
              </div>
            </div>
            <div v-if="isShowPublishSection" class="form-title author-section">
              <p class="author-form-title-header text-h3">{{ $t('EXTERNAL_ID') }}</p>
              <div
                class="author-section-main"
                :class="{
                  'simple-data-tooltip': isRecipeExternalIdFocus,
                }"
                :data-tooltip-text="isRecipeExternalIdFocus && recipeAttributionExternalId"
              >
                <div
                  class="form-author-input-text text-title-2 font-normal"
                  id="attributionExternal"
                  @mouseover="checkEditRecipeExternalId()"
                  @mouseleave="hideRecipeExternalIdTip()"
                >
                  {{
                    recipeAttributionExternalId || ""
                  }}
                </div>
              </div>
            </div>
          </div>
          </div>
          </div>
          <div
            :class="
              recipeVariantSelectedLanguage != defaultLang
                ? 'form-container form-section filter-background filter-data'
                : 'form-container form-section filter-background'
            "
          >
            <div class="form-section-lower-part">
              <div class="diet-category-tag-container">
                <div class="flter-show-hide-title">
                  <div class="recipe-variant-filter-heading">
                    <div class="recipe-variant-flag-section">
                      <img
                        alt=""
                        v-if="recipeVariantSelectedLanguage == 'en-US'"
                        class="recipe-flag"
                        src="@/assets/images/us-flag.png"
                      />
                      <img
                        alt=""
                        v-if="recipeVariantSelectedLanguage == 'fr-FR'"
                        class="recipe-flag"
                        src="@/assets/images/france-flag.png"
                      />
                      <img
                        alt=""
                        v-if="recipeVariantSelectedLanguage == 'es-US'"
                        class="recipe-flag"
                        src="@/assets/images/spain-flag.png"
                      />
                    </div>
                    <div class="filter-heading">{{ $t('COMMON.FILTERS') }}</div>
                  </div>
                  <button type="button" class="hide-heading btn-reset" @click="hideFilterOption()">
                    <span v-if="isShowFilterOption">{{ $t('COMMON.HIDE') }}</span>
                    <span v-else>{{ $t('COMMON.VIEW') }}</span>
                    <div class="filter-hide-drop-down">
                      <img
                        alt="arrow-icon"
                        :style="isShowFilterOption ? transformIcon : ''"
                        src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                        class="filter-hide-dropdown-icon"
                      />
                    </div>
                  </button>
                </div>
                <div v-if="isShowFilterOption">
                  <div class="filter-diets-section">
                    <div class="filter-section-heading text-title-2">Diets:</div>
                    <div class="filter-diets-container">
                      <div class="display-filter-diets-section">
                        <div class="filter-diets-available">
                          <div
                            v-for="(diet, index) in selectedDiets"
                            :key="index"
                          >
                            <div class="filter-image-and-diets-name">
                              <div class="filter-diets-image">
                                <img
                                  alt=""
                                  :src="diet && diet.image ? diet.image : ''"
                                />
                              </div>
                              <div class="filter-diets-item-name">
                                {{ diet && diet.name ? diet.name : "" }}
                              </div>
                              <div
                                class="remove-diet-section"
                                v-if="
                                  recipeVariantSelectedLanguage == defaultLang
                                "
                              >
                                <img
                                  alt=""
                                  @click="removeDiets(diet, index)"
                                  class="filter-diets-remove-image"
                                  src="@/assets/images/close.svg?skipsvgo=true"
                                />
                              </div>
                            </div>
                          </div>
                          <input
                            v-if="recipeVariantSelectedLanguage == defaultLang"
                            id="dietInput"
                            v-model="dietQuery"
                            placeholder="Select Diets"
                            type="text"
                            :class="
                              (selectedDiets && selectedDiets?.length == 0) ||
                              isDietAutocompleteOpen
                                ? 'filter-diets-select-input text-title-2 font-normal'
                                : 'hide-dropdown'
                            "
                            autocomplete="off"
                            tabindex="0"
                            dir=""
                            autocapitalize="off"
                            @input="getRecipeDiets"
                            @keyup.down="dietAutocompleteArrowDown"
                            @keyup.up="dietAutocompleteArrowUp"
                            @keyup.enter="dietAutocompleteEnter"
                            @click="
                              toggleDropdownAsync('diet', true);
                              toggleDropdownOff();
                            "
                          />
                          <input
                            v-if="
                              recipeVariantSelectedLanguage != defaultLang &&
                              !isDietAutocompleteOpen
                            "
                            :class="
                              (selectedDiets && selectedDiets?.length == 0) ||
                              isDietAutocompleteOpen
                                ? 'filter-diets-select-input text-title-2 font-normal'
                                : 'hide-dropdown'
                            "
                            placeholder="No Diets. Select Diets in English Recipe form."
                            readonly
                            style="cursor: default"
                          />
                        </div>
                        <div
                          v-if="recipeVariantSelectedLanguage == defaultLang"
                          class="filter-diets-drop-icon"
                          @click="toggleDropdownAsync('diet', false)"
                        >
                          <img
                            alt=""
                            src="@/assets/images/arrow-right.png"
                            class="filter-diets-dropdown-icon"
                            :class="{
                              rotate: isDietAutocompleteOpen,
                            }"
                          />
                        </div>
                      </div>
                      <div class="filter-select-diets-item">
                        <div
                          class="filter-diets-no-result-found text-title-2"
                          v-show="isDietAutocompleteOpen && !dietsList?.length"
                        >
                          {{ $t('COMMON.NO_RESULT') }}
                        </div>
                        <ul
                          v-show="isDietAutocompleteOpen && dietsList?.length"
                          class="filter-diets-autocomplete-results"
                          id="dietsList"
                        >
                          <li
                            v-for="(result, i) in dietsList"
                            :key="i"
                            class="diets-checkbox-title"
                            @click.prevent="setDietResult(result)"
                            :class="{
                              'filter-diets-autocomplete-result': true,
                              'is-active':
                                i === dietAutocompleteArrowCounter ||
                                result.isAlreadyInSelectedDiet,
                              'already-selected-filter-diets':
                                result.isAlreadyInSelectedDiet,
                            }"
                          >
                            <div class="filter-diets-checkbox">
                              <div class="filter-diets-round">
                                <input
                                  v-if="result.isAlreadyInSelectedDiet"
                                  type="checkbox"
                                />
                                <label for="filter-diets-round" aria-label="filter-diets-round" />
                              </div>
                            </div>
                            <div class="filter-diets-dropdown-image">
                              <img
                                alt=""
                                :src="
                                  result && result.image ? result.image : ''
                                "
                              />
                            </div>
                            <span class="filter-diets-dropdown-title text-title-2 font-normal">{{
                              result && result.name ? result.name : ""
                            }}</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="filter-categories-section">
                    <div class="filter-section-heading text-title-2">
                      {{ $t('CATEGORY.CATEGORY_TEXT') }}:
                    </div>
                    <div class="filter-categories-container">
                      <div class="display-filter-categories-section">
                        <div class="filter-category-available">
                          <div
                            v-for="(category, index) in selectedCategories"
                            :key="index"
                          >
                            <div class="filter-image-and-category-name">
                              <div class="filter-categories-image">
                                <img
                                  alt=""
                                  :src="
                                    category.data[recipeVariantSelectedLanguage]
                                      ? category.data[
                                          recipeVariantSelectedLanguage
                                        ].image
                                      : category.data[lang].image
                                  "
                                />
                              </div>
                              <div class="filter-categories-item-name text-title-2 font-normal">
                                {{
                                  category.data[recipeVariantSelectedLanguage]
                                    ? category.data[
                                        recipeVariantSelectedLanguage
                                      ].name
                                    : category.data[lang].name
                                }}
                              </div>
                              <div
                                v-if="
                                  recipeVariantSelectedLanguage !=
                                    defaultLang &&
                                  !category.data[recipeVariantSelectedLanguage]
                                "
                                class="category-edit-btn"
                                @click="editCategoryVariantName(category)"
                              >
                                <img
                                  alt=""
                                  src="@/assets/images/edit_icon_black.png"
                                />
                              </div>
                              <div
                                class="remove-category-section"
                                v-if="
                                  recipeVariantSelectedLanguage == defaultLang
                                "
                              >
                                <img
                                  alt=""
                                  @click="removeCategory(category, index)"
                                  class="filter-categories-remove-image"
                                  src="@/assets/images/close.svg?skipsvgo=true"
                                />
                              </div>
                            </div>
                          </div>
                          <input
                            v-if="recipeVariantSelectedLanguage == defaultLang"
                            id="categoryInput"
                            v-model="categoryQuery"
                            placeholder="Select Categories"
                            type="text"
                            :class="
                              (selectedCategories &&
                                selectedCategories?.length == 0) ||
                              isCategoryAutocompleteOpen
                                ? 'filter-categories-select-input text-title-2 font-normal'
                                : 'hide-dropdown'
                            "
                            autocomplete="off"
                            tabindex="0"
                            dir=""
                            autocapitalize="off"
                            @input="getRecipeCategories('input')"
                            @keyup.down="autocompleteArrowDown"
                            @keyup.up="autocompleteArrowUp"
                            @keyup.enter="autocompleteEnter"
                            @click="
                              toggleDropdownAsync('category', true);
                              toggleDropdownOff();
                            "
                          />
                          <input
                            v-if="
                              recipeVariantSelectedLanguage != defaultLang &&
                              !isCategoryAutocompleteOpen
                            "
                            :class="
                              (selectedCategories &&
                                selectedCategories?.length == 0) ||
                              isCategoryAutocompleteOpen
                                ? 'filter-categories-select-input text-title-2 font-normal'
                                : 'hide-dropdown'
                            "
                            placeholder="No Categories. Select Categories in English Recipe form."
                            readonly
                            style="cursor: default"
                          />
                        </div>
                        <div
                          class="category-filter-tooltip-section"
                          v-if="recipeVariantSelectedLanguage != defaultLang"
                        >
                          <div
                            v-if="!hasEnableCategoryInfoIcon"
                            class="tooltip-main-container-for-category-filter simple-data-tooltip"
                            :data-tooltip-text="categoryAutomaticName"
                          >
                            <img
                              alt=""
                              class="alert-image"
                              src="@/assets/images/red-info.svg?skipsvgo=true"
                            />
                          </div>
                        </div>
                        <div
                          v-if="recipeVariantSelectedLanguage == defaultLang"
                          class="filter-categories-drop-icon"
                          @click="toggleDropdownAsync('category', false)"
                        >
                          <img
                            alt=""
                            src="@/assets/images/arrow-right.png"
                            class="filter-categories-dropdown-icon"
                            :class="{
                              rotate: isCategoryAutocompleteOpen,
                            }"
                          />
                        </div>
                      </div>
                      <div class="filter-select-categories-item">
                        <div
                          class="filter-categories-no-result-found text-title-2"
                          v-show="
                            isCategoryAutocompleteOpen && !categoriesList?.length
                          "
                        >
                          {{ $t('COMMON.NO_RESULTS') }}
                        </div>
                        <ul
                          v-show="
                            isCategoryAutocompleteOpen && categoriesList?.length
                          "
                          class="filter-categories-autocomplete-results"
                          id="categoryList"
                        >
                          <li
                            v-for="(result, i) in categoriesList"
                            :key="i"
                            class="categories-checkbox-title"
                            @click.prevent="setResult(result)"
                            :class="{
                              'filter-categories-autocomplete-result': true,
                              'is-active':
                                i === categoryAutocompleteArrowCounter ||
                                result.isAlreadyInSelectedCategory,
                              'already-selected-filter-category':
                                result.isAlreadyInSelectedCategory,
                            }"
                          >
                            <div class="filter-categories-checkbox">
                              <div class="filter-categories-round">
                                <input
                                  v-if="
                                    result.isAlreadyInSelectedCategory
                                  "
                                  type="checkbox"
                                />
                                <label for="filter-categories-round" aria-label="filter-categories-round" />
                              </div>
                            </div>
                            <div class="filter-categories-dropdown-image">
                              <img
                                alt=""
                                :src="
                                  result.data[lang]
                                    ? result.data[lang].image
                                    : ''
                                "
                              />
                            </div>
                            <span class="filter-categories-dropdown-title text-title-2 font-normal">{{
                              result.data[lang] ? result.data[lang].name : ""
                            }}</span>
                          </li>
                          <li
                            class="filter-category-load-more"
                            v-if="
                              fromPopUpCategory + sizePopUpCategory <
                              categoriesTotal
                            "
                            @click="clickCategoriesLoadMoreAsync('clickedLoadMore')"
                          >
                            <div class="input-loading">
                              <div class="loader-image"></div>
                            </div>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="filter-tags-section">
                    <div class="filter-section-heading text-title-2">TAGS:</div>
                    <div class="filter-tags-container">
                      <div class="display-filter-tags-section">
                        <div class="filter-tags-available">
                          <div
                            v-for="(tag, index) in selectedTags"
                            :key="index"
                          >
                            <div class="filter-image-and-tags-name">
                              <div class="filter-tags-item-name text-title-2 font-normal">
                                {{
                                  tag.data[recipeVariantSelectedLanguage]
                                    ? tag.data[recipeVariantSelectedLanguage]
                                        .name
                                    : tag.data[lang].name
                                }}
                              </div>
                              <div
                                v-if="
                                  recipeVariantSelectedLanguage !=
                                    defaultLang &&
                                  !tag.data[recipeVariantSelectedLanguage]
                                "
                                class="tag-edit-btn"
                                @click="editTagVariantName(tag)"
                              >
                                <img
                                  alt=""
                                  src="@/assets/images/edit_icon_black.png"
                                />
                              </div>
                              <div
                                v-if="
                                  recipeVariantSelectedLanguage == defaultLang
                                "
                                class="remove-tag-section"
                              >
                                <img
                                  alt=""
                                  @click="removeTag(tag, index)"
                                  class="filter-tags-remove-image"
                                  src="@/assets/images/close.svg?skipsvgo=true"
                                />
                              </div>
                            </div>
                          </div>
                          <input
                            v-if="recipeVariantSelectedLanguage == defaultLang"
                            id="tagInput"
                            v-model="tagsQuery"
                            placeholder="Select Tags"
                            type="text"
                            :class="
                              (selectedTags && selectedTags?.length == 0) ||
                              isTagsAutocompleteOpen
                                ? 'filter-tags-select-input text-title-2 font-normal'
                                : 'hide-dropdown'
                            "
                            autocomplete="off"
                            tabindex="0"
                            dir=""
                            autocapitalize="off"
                            @input="getRecipeTags('input')"
                            @keyup.down="tagsAutocompleteArrowDown"
                            @keyup.up="tagsAutocompleteArrowUp"
                            @keyup.enter="tagsAutocompleteEnter"
                            @click="
                              toggleDropdownAsync('tag', true);
                              toggleDropdownOff();
                            "
                          />
                          <input
                            v-if="
                              recipeVariantSelectedLanguage != defaultLang &&
                              !isTagsAutocompleteOpen
                            "
                            :class="
                              (selectedTags && selectedTags?.length == 0) ||
                              isTagsAutocompleteOpen
                                ? 'filter-tags-select-input text-title-2 font-normal'
                                : 'hide-dropdown'
                            "
                            placeholder="No Tags. Select Tags in English Recipe form. "
                            readonly
                            style="cursor: default"
                          />
                        </div>
                        <div
                          class="tag-filter-tooltip-section"
                          v-if="recipeVariantSelectedLanguage != defaultLang"
                        >
                          <div
                            v-if="!hasEnableTagInfoIcon"
                            class="tooltip-main-container-for-tag-filter simple-data-tooltip"
                            :data-tooltip-text="tagAutomaticName"
                          >
                            <img
                              alt=""
                              class="alert-image"
                              src="@/assets/images/red-info.svg?skipsvgo=true"
                            />
                          </div>
                        </div>
                        <div
                          v-if="recipeVariantSelectedLanguage == defaultLang"
                          class="filter-tags-drop-icon"
                          @click="toggleDropdownAsync('tag', false)"
                        >
                          <img
                            alt=""
                            src="@/assets/images/arrow-right.png"
                            class="filter-tags-dropdown-icon"
                            :class="{
                              rotate: isTagsAutocompleteOpen,
                            }"
                          />
                        </div>
                      </div>
                      <div class="filter-select-tags-item">
                        <div
                          class="filter-tags-no-result-found text-title-2"
                          v-show="isTagsAutocompleteOpen && !tagsList?.length"
                        >
                          {{ $t('COMMON.NO_RESULTS') }}
                        </div>
                        <ul
                          v-show="isTagsAutocompleteOpen && tagsList?.length"
                          class="filter-tags-autocomplete-results"
                          id="tagsList"
                        >
                          <li
                            v-for="(result, i) in tagsList"
                            :key="i"
                            class="checkbox-title"
                            @click.prevent="setTagsResult(result)"
                            :class="{
                              'filter-tags-autocomplete-result': true,
                              'is-active':
                                i === tagsAutocompleteArrowCounter ||
                                result.isAlreadyInSelectedTag,
                              'already-selected-filter-tags':
                                result.isAlreadyInSelectedTag,
                            }"
                          >
                            <div class="filter-tags-checkbox">
                              <div class="filter-tags-round">
                                <input
                                  v-if="result.isAlreadyInSelectedTag"
                                  type="checkbox"
                                />
                                <label for="filter-tags-round" aria-label="filter-tags-round" />
                              </div>
                            </div>
                            <span class="filter-tags-dropdown-title text-title-2 font-normal">{{
                              result?.data?.[lang]?.name
                            }}</span>
                          </li>
                          <li
                            class="filter-tags-load-more"
                            v-if="fromPopUpTags + sizePopUpTags < tagsTotal"
                            @click="clickTagsLoadMoreAsync('clickedTagsLoadMore')"
                          >
                            <div class="input-loading">
                              <div class="loader-image"></div>
                            </div>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div v-if="isDisplayAllergens" class="filter-allergens-section">
                    <div class="filter-section-heading text-title-2">Allergens:</div>
                    <div class="filter-allergens-container">
                      <div class="display-filter-allergens-section">
                        <div class="filter-allergens-available">
                          <div
                            v-for="(allergens, index) in selectedAllergens"
                            :key="index"
                          >
                            <div class="filter-image-and-allergens-name">
                              <div class="filter-allergens-image">
                                <img
                                  alt=""
                                  :src="
                                    allergens && allergens.image
                                      ? allergens.image
                                      : ''
                                  "
                                />
                              </div>
                              <div class="filter-allergens-item-name text-title-2 font-normal">
                                {{
                                  allergens && allergens.name
                                    ? allergens.name
                                    : ""
                                }}
                              </div>
                              <div
                                class="remove-allergens-section"
                                v-if="
                                  recipeVariantSelectedLanguage == defaultLang
                                "
                              >
                                <img
                                  alt=""
                                  @click="removeAllergens(allergens, index)"
                                  class="filter-allergens-remove-image"
                                  src="@/assets/images/close.svg?skipsvgo=true"
                                />
                              </div>
                            </div>
                          </div>
                          <input
                            v-if="recipeVariantSelectedLanguage == defaultLang"
                            id="allergensInput"
                            v-model="allergensQuery"
                            placeholder="Select Allergens"
                            type="text"
                            :class="
                              (selectedAllergens &&
                                selectedAllergens?.length == 0) ||
                              isAllergensAutocompleteOpen
                                ? 'filter-allergens-select-input text-title-2 font-normal'
                                : 'hide-dropdown'
                            "
                            autocomplete="off"
                            tabindex="0"
                            dir=""
                            autocapitalize="off"
                            @input="getRecipeAllergens('')"
                            @keyup.down="allergensAutocompleteArrowDown"
                            @keyup.up="allergensAutocompleteArrowUp"
                            @keyup.enter="allergensAutocompleteEnter"
                            @click="
                              toggleDropdownAsync('allergens', true);
                              toggleDropdownOff();
                            "
                          />
                        </div>
                        <div
                          v-if="recipeVariantSelectedLanguage == defaultLang"
                          class="filter-allergens-drop-icon"
                          @click="toggleDropdownAsync('allergens', false)"
                        >
                          <img
                            alt=""
                            src="@/assets/images/arrow-right.png"
                            class="filter-allergens-dropdown-icon"
                            :class="{
                              rotate: isAllergensAutocompleteOpen,
                            }"
                          />
                        </div>
                      </div>
                      <div class="filter-select-allergens-item">
                        <div
                          class="filter-allergens-no-result-found text-title-2"
                          v-show="
                            isAllergensAutocompleteOpen && !allergensList?.length
                          "
                        >
                          {{ $t('COMMON.NO_RESULT') }}
                        </div>
                        <ul
                          v-show="
                            isAllergensAutocompleteOpen && allergensList?.length
                          "
                          class="filter-allergens-autocomplete-results"
                          id="allergensList"
                        >
                          <li
                            v-for="(result, i) in allergensList"
                            :key="i"
                            class="allergens-checkbox-title"
                            @click.prevent="setAllergensResult(result)"
                            :class="{
                              'filter-allergens-autocomplete-result': true,
                              'is-active':
                                i === allergensAutocompleteArrowCounter ||
                                result.isAlreadyInSelectedAllergens,
                              'already-selected-filter-allergens':
                                result.isAlreadyInSelectedAllergens,
                            }"
                          >
                            <div class="filter-allergens-checkbox">
                              <div class="filter-allergens-round">
                                <input
                                  v-if="
                                    result.isAlreadyInSelectedAllergens
                                  "
                                  type="checkbox"
                                />
                                <label for="filter-allergens-round" aria-label="filter-allergens-round" />
                              </div>
                            </div>
                            <div class="filter-allergens-dropdown-image">
                              <img
                                alt=""
                                :src="
                                  result && result.image ? result.image : ''
                                "
                              />
                            </div>
                            <span class="filter-allergens-dropdown-title text-title-2 font-normal">{{
                              result && result.name ? result.name : ""
                            }}</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="form-container ingredients-section"
            :class="{
              'disable-recipe-detail-content': isReadOnlyProvider(
                recipeData?.provider
              ),
            }"
          >
            <div class="recipe-variant-ingredient-text">
              <div class="recipe-variant-flag-section">
                <img
                  alt=""
                  v-if="recipeVariantSelectedLanguage == 'en-US'"
                  class="recipe-flag"
                  src="@/assets/images/us-flag.png"
                />
                <img
                  alt=""
                  v-if="recipeVariantSelectedLanguage == 'fr-FR'"
                  class="recipe-flag"
                  src="@/assets/images/france-flag.png"
                />
                <img
                  alt=""
                  v-if="recipeVariantSelectedLanguage == 'es-US'"
                  class="recipe-flag"
                  src="@/assets/images/spain-flag.png"
                />
              </div>
              <div class="form-title">
                <p class="form-title-header">Ingredients</p>
                <span class="compulsory-field">*</span>
              </div>
            </div>
            <div v-if="isUpdatingIngredient" class="description-section">
              <table
                style="width: 100%"
                v-if="isReadOnlyProvider(recipeData?.provider)"
                class="ingredients-table"
              >
                <caption></caption>
                <thead>
                  <tr>
                    <th style="width: 80px; max-width: 4%"></th>
                    <th style="width: 500px; max-width: 25%">
                      INGREDIENT NAME
                      <span class="compulsory-field">*</span>
                    </th>
                    <th style="width: 400px; max-width: 20%">MODIFIER</th>
                    <th style="width: 400px; max-width: 20%">NOTES</th>
                    <th style="width: 280px; max-width: 14%">
                      {{ $t('QUANTITY') }}
                    </th>
                    <th style="width: 280px; max-width: 14%">UOM</th>
                    <th style="min-width: 40px"></th>
                  </tr>
                </thead>
                <tbody class="tbody">
                  <div
                    class="table-group-data"
                    v-if="
                      ingredientsData &&
                      ingredientsData[recipeVariantSelectedLanguage] &&
                      ingredientsData[recipeVariantSelectedLanguage].children
                    "
                  >
                    <div
                      class="group-content"
                      v-for="(ingredientGroup, groupIndex) in ingredientsData[
                        recipeVariantSelectedLanguage
                      ].children"
                      :key="groupIndex"
                    >
                      <div
                        class="group-name-section"
                        v-if="ingredientGroup.displayGroup"
                      >
                        <div class="left-side-group">
                          <input
                            @click="toggleDropdownOff()"
                            v-on:blur="setIngredientGroupNameAsync(groupIndex)"
                            v-model="ingredientGroup.name"
                            autocomplete="off"
                            type="text"
                            :id="`groupName${groupIndex}`"
                            @input="saveButtonEnable()"
                            class="filter-group-input"
                            placeholder="Enter Group Name"
                            :disabled="isReadOnlyProvider(recipeData?.provider)"
                          />
                        </div>
                        <span class="delete-group">
                          <img
                            alt=""
                            src="@/assets/images/delete-icon.png"
                            @click="displayRemovePopUp(groupIndex)"
                            width="18"
                            height="20"
                          />
                        </span>
                      </div>
                      <draggable
                        :list="ingredientGroup.children"
                        group="ingredients"
                        class="table-row-data"
                        :scroll-sensitivity="200"
                        :force-fallback="true"
                        ghost-class="hidden-list"
                        @start="drag = true"
                        @end="drag = false"
                        handle=".draggable-icon"
                        :disabled="isAddSortConfirm"
                        @change="handleDrag"
                      >
                        <tr
                          v-show="ingredientGroup.children?.length > 0"
                          v-for="(
                            ingredient, index
                          ) in ingredientGroup.children"
                          :key="index"
                        >
                          <td
                            style="width: 80px; max-width: 4%"
                            :class="
                              isReadOnlyProvider(recipeData?.provider) ||
                              recipeVariantSelectedLanguage != defaultLang
                                ? 'disabled-draggable-icon'
                                : 'draggable-icon'
                            "
                          >
                            <img
                              alt=""
                              src="@/assets/images/drag-text.svg?skipsvgo=true"
                              @click="toggleDropdownOff()"
                            />
                          </td>
                          <td
                            style="width: 500px; max-width: 25%"
                            class="ingredient-name-box"
                          >
                            <span
                              class="ing-tool"
                              @mouseover="
                                ingNameCheck(ingredient.name, groupIndex, index)
                              "
                              @mouseleave="
                                ingNameCheckHide(
                                  ingredient.name,
                                  groupIndex,
                                  index
                                )
                              "
                              :class="{
                                'simple-data-tooltip': isIngredientNameVisible,
                              }"
                              :data-tooltip-text="isIngredientNameVisible && ingredient.name"
                            >
                              <input
                                @keypress="preventSpecialCharacters($event)"
                                @click="toggleDropdownOff()"
                                v-model.trim="ingredient.name"
                                v-on:blur="
                                  checkOneGlobalIngredient(
                                    groupIndex,
                                    index,
                                    ingredient.name
                                  )
                                "
                                @input="
                                  saveButtonEnable(),
                                    updateNameMirror(ingredient)
                                "
                                type="text"
                                :id="`foodItemName${groupIndex}${index}`"
                                class="filter-select-input text-light-h3"
                                autocomplete="off"
                                tabindex="0"
                                dir=""
                                autocapitalize="off"
                                placeholder="Enter Food Item"
                                :disabled="
                                  isReadOnlyProvider(recipeData?.provider)
                                "
                              />
                            </span>
                          </td>
                          <td
                            style="width: 400px; max-width: 20%"
                            class="modifier-cart-menu"
                          >
                            <div class="modifier-cart-menu-box">
                              <input
                                type="text"
                                :id="`modifier${groupIndex}${index}`"
                                @click="toggleDropdownOff()"
                                @input="saveButtonEnable()"
                                v-model="ingredient.modifier"
                                class="filter-select-input text-light-h3"
                                autocomplete="off"
                                tabindex="0"
                                dir=""
                                autocapitalize="off"
                                placeholder="Enter Modifier"
                                :disabled="
                                  isReadOnlyProvider(recipeData?.provider)
                                "
                              />
                            </div>
                          </td>
                          <td
                            style="width: 400px; max-width: 20%"
                            class="ingredient-name-box"
                          >
                            <span
                              @mouseover="ingNoteCheck(groupIndex, index)"
                              @mouseleave="ingredientNoteCheckHide()"
                              class="ing-note"
                              :class="{
                                'simple-data-tooltip': isNoteTooltipVisible,
                              }"
                              :data-tooltip-text="isNoteTooltipVisible && ingredient.note"
                            >
                              <input
                                @click="toggleDropdownOff()"
                                @input="saveButtonEnable()"
                                :id="`ingredientNote${groupIndex}${index}`"
                                v-model="ingredient.note"
                                type="text"
                                class="filter-select-input text-light-h3"
                                autocomplete="off"
                                tabindex="0"
                                dir=""
                                autocapitalize="off"
                                placeholder="Ingredient note (optional)"
                                :disabled="
                                  isReadOnlyProvider(recipeData?.provider)
                                "
                              />
                            </span>
                          </td>
                          <td style="width: 280px; max-width: 14%">
                            <input
                              type="Number"
                              :id="`quantity${groupIndex}${index}`"
                              @click="toggleDropdownOff()"
                              v-model="ingredient.quantity"
                              @keypress="restrictNumericInput($event)"
                              @input="
                                saveButtonEnable(),
                                  updateQuantityMirror(ingredient)
                              "
                              class="filter-select-input text-light-h3"
                              autocomplete="off"
                              tabindex="0"
                              min="0"
                              max="1000"
                              dir=""
                              autocapitalize="off"
                              :disabled="
                                isReadOnlyProvider(recipeData?.provider)
                              "
                            />
                          </td>
                          <td
                            style="width: 280px; max-width: 14%"
                            class="filter-icon"
                          >
                            <input
                              :id="`ingredientUom${groupIndex}${index}`"
                              v-model="ingredient.UOM"
                              type="text"
                              class="filter-select-input text-light-h3 uom-input"
                              @keypress="restrictToAlphabets($event)"
                              autocomplete="off"
                              tabindex="0"
                              dir=""
                              autocapitalize="off"
                              style="left: 18px"
                              @input="
                                toggleDropdownUOM(
                                  'ingredientUom',
                                  index,
                                  ingredient,
                                  groupIndex
                                ),
                                  searchUOMList(ingredient),
                                  saveButtonEnable(),
                                  updateUOMMirror(ingredient)
                              "
                              @blur="closeUomDropdownAsync(ingredient)"
                              @keyup.down="
                                ingredientUomAutocompleteArrowDown(ingredient)
                              "
                              @keyup.up="
                                ingredientUomAutocompleteArrowUp(ingredient)
                              "
                              @keyup.enter="
                                ingredientUomAutocompleteEnter(ingredient)
                              "
                              @click="
                                toggleDropdownUOM(
                                  'ingredientUom',
                                  index,
                                  ingredient,
                                  groupIndex
                                ),
                                  searchUOMList(ingredient)
                              "
                              :disabled="
                                isReadOnlyProvider(recipeData?.provider)
                              "
                            />
                            <div class="filter-icon-box" style="left: 4px">
                              <img
                                alt=""
                                src="@/assets/images/arrow-right.png"
                                @click="
                                  toggleDropdownAsync(
                                    'ingredientUom',
                                    false,
                                    index,
                                    ingredient,
                                    groupIndex
                                  )
                                "
                                class="dropdown-icon-hide"
                                :class="{
                                  rotate:
                                    ingredient.uomAutocomplete &&
                                    ingredientsUomList,
                                }"
                              />
                            </div>
                            <ul
                              v-if="ingredient.uomAutocomplete"
                              class="autocomplete-results"
                              id="ingredientsUomList"
                            >
                              <div v-if="searchedUomText == ''">
                                <li
                                  v-for="(result, i) in ingredientsUomList"
                                  :key="i"
                                  :class="{
                                    'autocomplete-result': true,
                                    'is-active':
                                      i ===
                                      ingredientUomAutocompleteArrowCounter,
                                  }"
                                  @click.prevent="
                                    setIngredientUomResult(result, ingredient)
                                  "
                                >
                                  {{ result.display }}
                                </li>
                              </div>
                              <div v-if="searchedUomText !== ''">
                                <li
                                  v-for="(result, i) in searchedUomList"
                                  :key="i"
                                  :class="{
                                    'autocomplete-result': true,
                                    'is-active':
                                      i ===
                                      ingredientUomAutocompleteArrowCounter,
                                  }"
                                  @click.prevent="
                                    setIngredientUomResult(result, ingredient)
                                  "
                                >
                                  {{ result.display }}
                                </li>
                              </div>
                            </ul>
                          </td>
                          <td style="min-width: 40px" class="menu">
                            <div class="menu-box" :id="index">
                              <img
                                v-if="!isReadOnlyProvider(recipeData?.provider)"
                                alt=""
                                @click="
                                  deleteModalVisible(
                                    'ingredientModal',
                                    index,
                                    ingredient,
                                    groupIndex
                                  )
                                "
                                src="@/assets/images/delete-icon.png"
                                width="18"
                                height="20"
                              />
                            </div>
                          </td>
                          <div class="ingredient-keyword-main-container">
                            <div
                              class="ingredient-drop-down"
                              v-if="!ingredient.isDropDown"
                            >
                              <img
                                alt=""
                                src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                                class="dropdown-icon"
                                @click="
                                  ingredientClickDropDown(
                                    groupIndex,
                                    index,
                                    ingredient.isDropDown
                                  )
                                "
                              />
                            </div>
                            <div
                              class="ingredient-full-view-container"
                              v-if="ingredient.isDropDown"
                            >
                              <div class="ingredient-keywords-container">
                                <div class="ingredient-border-line">
                                  <div class="border"></div>
                                </div>
                                <div class="ingredient">
                                  <div class="ingredient-keywords">
                                    <span class="ingredient-keyword-text text-h3">
                                      Keywords:
                                    </span>

                                    <div class="ingredient-keyword-container text-light-h3">
                                      <div
                                        v-for="(
                                          keyword, keywordIndex
                                        ) in ingredient.keywords"
                                        class="selected-ingredient-keyword"
                                        :key="keywordIndex"
                                        :class="{
                                          'simple-data-tooltip': isKeywordTooltipVisible,
                                        }"
                                        :data-tooltip-text="isKeywordTooltipVisible && ingredient.keyword"
                                      >
                                        <div
                                          @mouseover="
                                            showKeywordTip(keywordIndex)
                                          "
                                          @mouseleave="
                                            hideKeywordTip(keywordIndex)
                                          "
                                          class="ingredient-keyword-name"
                                        >
                                          {{ keyword ? keyword : "" }}
                                        </div>
                                        <div class="ingredient-keyword-remove">
                                          <img
                                            alt=""
                                            @click="
                                              removeIngredientKeyword(
                                                groupIndex,
                                                index,
                                                keywordIndex
                                              )
                                            "
                                            class="remove-ingredient-keyword-image"
                                            src="@/assets/images/close.svg?skipsvgo=true"
                                          />
                                        </div>
                                      </div>
                                      <input
                                        type="text"
                                        :class="
                                          ingredient.keywords &&
                                          ingredient.keywords?.length > 0
                                            ? 'keywords keywordsPosition'
                                            : 'keywords'
                                        "
                                        id="keywords-name"
                                        v-model="ingredient.keywordInput"
                                        :disabled="
                                          isReadOnlyProvider(
                                            recipeData?.provider
                                          )
                                        "
                                        @click="toggleDropdownOff()"
                                        autocomplete="off"
                                        tabindex="0"
                                        dir=""
                                        autocapitalize="off"
                                        :placeholder="
                                          ingredient.keywords &&
                                          ingredient.keywords?.length == 0
                                            ? 'List keywords separated by a comma (optional)'
                                            : 'Write keywords'
                                        "
                                      />
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="ingredient-minimize-view">
                                <img
                                  alt=""
                                  src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                                  class="dropdown-icon"
                                  @click="
                                    ingredientClickDropDown(
                                      groupIndex,
                                      index,
                                      ingredient.isDropDown
                                    )
                                  "
                                />
                              </div>
                            </div>
                          </div>
                        </tr>
                      </draggable>

                      <div
                        class="empty-group-ingredient"
                        v-show="
                          ingredientGroup.children?.length == 0 &&
                          ingredientGroup.displayGroup
                        "
                      >
                        <span class="text-ingredient">
                          Drag ingredients into ingredient group
                        </span>
                      </div>
                    </div>
                  </div>
                </tbody>
                <tbody>
                  <tr>
                    <td>
                      <button
                        v-if="!isReadOnlyProvider(recipeData?.provider)"
                        type="button"
                        class="btn-green-outline add-ingredient-button"
                        @click="addIngredientConfirm()"
                        @keydown="preventEnterAndSpaceKeyPress($event)"
                      >
                        {{ $t('COMMON.ADD_INGREDIENT') }}
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tbody>
                  <tr>
                    <td>
                      <span
                        v-if="!isReadOnlyProvider(recipeData?.provider)"
                        class="add-group-button"
                        @click="addGroupConfirm()"
                      >
                        <img
                          alt=""
                          class="add-image"
                          src="@/assets/images/category-add.png"
                        />
                        <span>{{ $t('COMMON.ADD_GROUP') }}</span>
                      </span>
                    </td>
                  </tr>
                </tbody>
                <div
                  v-if="
                    isRecipeLoading &&
                    hasDisplayShoppableReview &&
                    checkIngredientsLength()
                  "
                  @click="shopPreview()"
                  class="shoppable-review-button"
                >
                  <img
                    alt=""
                    style="width: 29px; height: 15px"
                    class="add-image"
                    src="@/assets/images/Shop-preview-icon.jpeg"
                  />
                  <span>{{ $t('COMMON.SHOPPABLE_REVIEW') }}</span>
                </div>
              </table>
              <table style="width: 100%" v-else class="ingredients-table">
                <caption></caption>
                <thead>
                  <tr>
                    <th style="width: 80px; max-width: 4%"></th>
                    <th style="width: 600px; max-width: 30%">
                      INGREDIENT NAME
                      <span class="compulsory-field">*</span>
                    </th>
                    <th style="width: 520px; max-width: 26%">NOTES</th>
                    <th style="width: 280px; max-width: 14%">
                      {{ $t('QUANTITY') }}
                    </th>
                    <th style="width: 280px; max-width: 14%">
                      {{ $t('UOM') }}
                    </th>
                    <th style="min-width: 40px"></th>
                  </tr>
                </thead>
                <tbody class="tbody">
                  <div
                    class="table-group-data"
                    v-if="
                      ingredientsData &&
                      ingredientsData[recipeVariantSelectedLanguage] &&
                      ingredientsData[recipeVariantSelectedLanguage].children
                    "
                  >
                    <div
                      class="group-content"
                      v-for="(ingredientGroup, groupIndex) in ingredientsData[
                        recipeVariantSelectedLanguage
                      ].children"
                      :key="groupIndex"
                    >
                      <div
                        class="group-name-section"
                        v-if="ingredientGroup.displayGroup"
                      >
                        <div class="left-side-group">
                          <input
                            @click="toggleDropdownOff()"
                            @input="saveButtonEnable()"
                            v-on:blur="setIngredientGroupNameAsync(groupIndex)"
                            v-model="ingredientGroup.name"
                            autocomplete="off"
                            type="text"
                            :id="`groupName${groupIndex}`"
                            class="filter-group-input"
                            placeholder="Enter Group Name"
                            :disabled="isReadOnlyProvider(recipeData?.provider)"
                          />
                        </div>
                        <span
                          class="delete-group"
                          :class="
                            recipeVariantSelectedLanguage == defaultLang
                              ? 'delete-group'
                              : 'disable-delete-group'
                          "
                        >
                          <img
                            alt=""
                            src="@/assets/images/delete-icon.png"
                            @click="displayRemovePopUp(groupIndex)"
                            width="18"
                            height="20"
                          />
                        </span>
                      </div>
                      <draggable
                        :list="ingredientGroup.children"
                        group="ingredients"
                        class="table-row-data"
                        ghost-class="hidden-list"
                        :scroll-sensitivity="200"
                        :force-fallback="true"
                        @start="checkIngredientPosition"
                        @end="checkMoveIngredientAsync"
                        handle=".draggable-icon"
                        :disabled="isAddSortConfirm"
                        @change="handleDrag"
                      >
                        <tr
                          v-show="ingredientGroup.children?.length > 0"
                          v-for="(
                            ingredient, index
                          ) in ingredientGroup.children"
                          :key="index"
                        >
                          <td
                            style="width: 80px; max-width: 4%"
                            class="draggable-icon"
                            :class="
                              recipeVariantSelectedLanguage != defaultLang
                                ? 'disabled-draggable-icon'
                                : 'draggable-icon'
                            "
                          >
                            <img
                              alt=""
                              src="@/assets/images/drag-text.svg?skipsvgo=true"
                              @click="toggleDropdownOff()"
                            />
                          </td>
                          <td
                            style="width: 600px; max-width: 30%"
                            class="ingredient-name-box"
                          >
                            <span
                              class="ing-tool"
                              @mouseover="
                                ingNameCheck(ingredient.name, groupIndex, index)
                              "
                              @mouseleave="
                                ingNameCheckHide(
                                  ingredient.name,
                                  groupIndex,
                                  index
                                )
                              "
                              :class="{
                                'simple-data-tooltip': isIngredientNameVisible,
                              }"
                              :data-tooltip-text="isIngredientNameVisible && ingredient.name"
                            >
                              <input
                                @keypress="preventSpecialCharacters($event)"
                                @click="toggleDropdownOff()"
                                v-on:blur="
                                  checkOneGlobalIngredient(
                                    groupIndex,
                                    index,
                                    ingredient.name
                                  )
                                "
                                @input="
                                  saveButtonEnable(),
                                    changeReferenceProductId(ingredient),
                                    updateNameMirror(ingredient)
                                "
                                v-model.trim="ingredient.name"
                                type="text"
                                :id="`foodItemName${groupIndex}${index}`"
                                class="filter-select-input text-light-h3"
                                autocomplete="off"
                                tabindex="0"
                                dir=""
                                autocapitalize="off"
                                placeholder="Enter Food Item"
                                :disabled="
                                  isReadOnlyProvider(recipeData?.provider)
                                "
                              />
                            </span>
                          </td>
                          <td
                            style="width: 520px; max-width: 26%"
                            class="ingredient-name-box"
                          >
                            <span
                              class="ing-note"
                              :class="{
                                'simple-data-tooltip': isNoteTooltipVisible,
                              }"
                              :data-tooltip-text="isNoteTooltipVisible && ingredient.note"
                            >
                              <input
                                @click="toggleDropdownOff()"
                                :id="`ingredientNote${groupIndex}${index}`"
                                v-model.trim="ingredient.note"
                                type="text"
                                class="filter-select-input text-light-h3"
                                autocomplete="off"
                                tabindex="0"
                                dir=""
                                @input="saveButtonEnable()"
                                @mouseover="
                                  ingNoteCheck(
                                    groupIndex,
                                    index,
                                    ingredient.note
                                  )
                                "
                                @mouseleave="
                                  ingredientNoteCheckHide()
                                "
                                @keydown="ingredientNoteCheckHide()"
                                autocapitalize="off"
                                placeholder="Ingredient note (optional)"
                                :disabled="
                                  isReadOnlyProvider(recipeData?.provider)
                                "
                              />
                            </span>
                          </td>
                          <td style="width: 280px; max-width: 14%">
                            <input
                              type="Number"
                              :id="`quantity${groupIndex}${index}`"
                              @click="toggleDropdownOff(), disableScroll()"
                              v-model="ingredient.quantity"
                              @keypress="restrictNumericInput($event)"
                              @input="
                                saveButtonEnable(),
                                  updateQuantityMirror(ingredient)
                              "
                              v-on:blur="getQuantity(ingredient)"
                              class="filter-select-input text-light-h3 no-scroll"
                              @paste="
                                handlePaste(
                                  $event,
                                  'quantity',
                                  index,
                                  groupIndex
                                )
                              "
                              autocomplete="off"
                              tabindex="0"
                              dir=""
                              min="0"
                              max="1000"
                              autocapitalize="off"
                              :disabled="
                                isReadOnlyProvider(recipeData?.provider)
                              "
                            />
                          </td>
                          <td
                            style="width: 280px; max-width: 14%"
                            class="filter-icon"
                          >
                            <input
                              :id="`ingredientUom${groupIndex}${index}`"
                              v-model="ingredient.UOM"
                              type="text"
                              class="filter-select-input text-light-h3 uom-input"
                              @keypress="restrictToAlphabets($event)"
                              autocomplete="off"
                              tabindex="0"
                              dir=""
                              autocapitalize="off"
                              style="left: 18px"
                              @blur="closeUomDropdownAsync(ingredient)"
                              @input="
                                toggleDropdownUOM(
                                  'ingredientUom',
                                  index,
                                  ingredient,
                                  groupIndex
                                ),
                                  searchUOMList(ingredient),
                                  saveButtonEnable(),
                                  updateUOMMirror(ingredient)
                              "
                              @keyup.down="
                                ingredientUomAutocompleteArrowDown(ingredient)
                              "
                              @keyup.up="
                                ingredientUomAutocompleteArrowUp(ingredient)
                              "
                              @keyup.enter="
                                ingredientUomAutocompleteEnter(ingredient)
                              "
                              @click="
                                toggleDropdownUOM(
                                  'ingredientUom',
                                  index,
                                  ingredient,
                                  groupIndex
                                ),
                                  searchUOMList(ingredient)
                              "
                              :disabled="
                                isReadOnlyProvider(recipeData?.provider)
                              "
                            />
                            <div class="filter-icon-box" style="left: 4px">
                              <img
                                alt=""
                                src="@/assets/images/arrow-right.png"
                                @click="
                                  toggleDropdownAsync(
                                    'ingredientUom',
                                    false,
                                    index,
                                    ingredient,
                                    groupIndex
                                  )
                                "
                                class="dropdown-icon"
                                :class="{
                                  rotate:
                                    ingredient.uomAutocomplete &&
                                    ingredientsUomList,
                                }"
                              />
                            </div>
                            <div
                              class="autocomplete-results-nouom"
                              v-show="
                                ingredient.uomAutocomplete &&
                                !searchedUomText == ''
                              "
                            >
                              {{ $t('COMMON.NO_RESULT') }}
                            </div>
                            <ul
                              v-if="ingredient.uomAutocomplete"
                              class="autocomplete-results"
                              id="ingredientsUomList"
                            >
                              <div v-if="searchedUomText == ''">
                                <li
                                  v-for="(result, i) in ingredientsUomList"
                                  :key="i"
                                  :class="{
                                    'autocomplete-result': true,
                                    'is-active':
                                      i ===
                                      ingredientUomAutocompleteArrowCounter,
                                  }"
                                  @click.prevent="
                                    setIngredientUomResult(result, ingredient)
                                  "
                                >
                                  {{ result.display }}
                                </li>
                              </div>
                              <div v-if="searchedUomText !== ''">
                                <li
                                  v-for="(result, i) in searchedUomList"
                                  :key="i"
                                  :class="{
                                    'autocomplete-result': true,
                                    'is-active':
                                      i ===
                                      ingredientUomAutocompleteArrowCounter,
                                  }"
                                  @click.prevent="
                                    setIngredientUomResult(result, ingredient)
                                  "
                                >
                                  {{ result?.display }}
                                </li>
                              </div>
                            </ul>
                          </td>
                          <td style="min-width: 40px" class="menu">
                            <div
                              v-if="!isReadOnlyProvider(recipeData?.provider)"
                              class="menu-box"
                              :id="index"
                              :class="
                                recipeVariantSelectedLanguage === defaultLang
                                  ? 'menu-box'
                                  : 'disable-menu'
                              "
                            >
                              <img
                                alt=""
                                @click="
                                  deleteModalVisible(
                                    'ingredientModal',
                                    index,
                                    ingredient,
                                    groupIndex
                                  )
                                "
                                src="@/assets/images/delete-icon.png"
                                width="18"
                                height="20"
                              />
                            </div>
                          </td>
                          <div class="ingredient-keyword-main-container">
                            <div
                              class="ingredient-drop-down"
                              v-if="!ingredient.isDropDown"
                            >
                              <img
                                alt=""
                                src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                                class="dropdown-icon"
                                @click="
                                  ingredientClickDropDown(
                                    groupIndex,
                                    index,
                                    ingredient.isDropDown
                                  )
                                "
                              />
                            </div>
                            <div
                              class="ingredient-full-view-section"
                              v-if="ingredient.isDropDown"
                            >
                              <div class="ingredient-keywords-container">
                                <div class="ingredient-border-line">
                                  <div class="border"></div>
                                </div>
                                <div class="ingredient" v-if="isAdminCheck">
                                  <div class="ingredient-keywords">
                                    <span
                                      v-if="
                                        ingredient.globalKeyword?.length &&
                                        ingredient.keywords?.length == 0
                                      "
                                      class="ingredient-keyword-text text-h3"
                                    >
                                      Global Keywords:
                                    </span>
                                    <span
                                      v-if="ingredient.keywords?.length > 0"
                                      class="ingredient-keyword-text text-h3"
                                    >
                                      Keywords:
                                    </span>
                                    <span
                                      v-if="
                                        ingredient.keywords?.length == 0 &&
                                        ingredient.globalKeyword?.length == 0
                                      "
                                      class="ingredient-keyword-text text-h3"
                                    >
                                      Keywords:
                                    </span>
                                    <div class="head-container-breadcrumb">
                                      <div
                                        v-if="ingredient.keywords?.length > 0"
                                        class="main-container-breadcrumb"
                                      >
                                        <div
                                          v-for="(
                                            data, keyIndex
                                          ) in ingredient.keywords"
                                          :key="keyIndex"
                                          class="breadcrumb"
                                        >
                                          <div
                                            class="data-name"
                                            :id="`ingredientPopProductName${ingredient.id}${groupIndex}${keyIndex}`"
                                            @mouseover="
                                              checkIngredientPopName(
                                                ingredient.id,
                                                groupIndex,
                                                keyIndex
                                              )
                                            "
                                            @mouseleave="
                                              hideIngredientNamePopTip(
                                                ingredient.id,
                                                groupIndex,
                                                keyIndex
                                              )
                                            "
                                            :class="{
                                              'simple-data-tooltip': isIngredientProductNameVisible,
                                            }"
                                            :data-tooltip-text="isIngredientProductNameVisible && data"
                                          >
                                            {{ data }}
                                          </div>
                                        </div>
                                        <div
                                          class="edit-breadcrumb text-h3"
                                          @click="
                                            ingredientKeywordsPopup(
                                              groupIndex,
                                              index,
                                              ingredient.name,
                                              ingredient.UOM,
                                              ingredient.quantity,
                                              ingredient.note,
                                              ingredient.keywords,
                                              ingredient.globalKeyword,
                                              EDIT
                                            )
                                          "
                                        >
                                          {{ $t('BUTTONS.EDIT_BUTTON') }}
                                        </div>
                                      </div>
                                      <div
                                        v-if="
                                          ingredient.globalKeyword?.length > 0 &&
                                          ingredient.keywords?.length == 0
                                        "
                                        class="main-container-breadcrumb"
                                      >
                                        <div
                                          v-for="(
                                            data, keyIndex
                                          ) in ingredient.globalKeyword"
                                          :key="keyIndex"
                                          class="breadcrumb"
                                        >
                                          <div
                                            :id="`ingredientPopProductName${ingredient.id}${groupIndex}${keyIndex}`"
                                            @mouseover="
                                              checkIngredientPopName(
                                                ingredient.id,
                                                groupIndex,
                                                keyIndex
                                              )
                                            "
                                            @mouseleave="
                                              hideIngredientNamePopTip(
                                                ingredient.id,
                                                groupIndex,
                                                keyIndex
                                              )
                                            "
                                            class="data-name"
                                            :class="{
                                              'simple-data-tooltip': isIngredientProductNameVisible,
                                            }"
                                            :data-tooltip-text="isIngredientProductNameVisible && data"
                                          >
                                            {{ data }}
                                          </div>
                                        </div>
                                        <div
                                          class="edit-breadcrumb text-h3"
                                          @click="
                                            ingredientKeywordsPopup(
                                              groupIndex,
                                              index,
                                              ingredient.name,
                                              ingredient.UOM,
                                              ingredient.quantity,
                                              ingredient.note,
                                              ingredient.keywords,
                                              ingredient.globalKeyword,
                                              OVERRIDE
                                            )
                                          "
                                        >
                                          override
                                        </div>
                                      </div>
                                      <div
                                        v-if="
                                          ingredient.globalKeyword?.length ==
                                            0 && ingredient.keywords?.length == 0
                                        "
                                        class="main-container-breadcrumb"
                                      >
                                        <div
                                          class="add-breadcrumb"
                                          @click="
                                            ingredientKeywordsPopup(
                                              groupIndex,
                                              index,
                                              ingredient.name,
                                              ingredient.UOM,
                                              ingredient.quantity,
                                              ingredient.note,
                                              ingredient.keywords,
                                              ingredient.globalKeyword,
                                              ADD
                                            )
                                          "
                                        >
                                          Add
                                        </div>
                                      </div>
                                    </div>
                                    <div
                                      class="ingredient-keyword-container text-light-h3"
                                    ></div>
                                  </div>
                                  <div
                                    v-if="
                                      ingredient.volumeInMl == 0 &&
                                      ingredient.weightInGrams == 0
                                    "
                                    class="ingredient-size"
                                  >
                                    <div
                                      :class="
                                        ingredient && ingredient.externalId
                                          ? 'ingredient-size-main-container'
                                          : 'ingredient-size-main-container ingredient-size-main-no-externalId'
                                      "
                                    >
                                      <span class="ingredient-size-text text-h3"
                                        >Size:</span
                                      >
                                      <div
                                        :class="
                                          ingredient && ingredient.externalId
                                            ? 'ingredient-add-size-container text-h3'
                                            : 'ingredient-add-size-no-externalId'
                                        "
                                      >
                                        <span
                                          @click="
                                            isShowAddQuantityMethod(
                                              groupIndex,
                                              index,
                                              ingredient.name,
                                              ingredient.UOM,
                                              ingredient.quantity,
                                              ingredient.note
                                            )
                                          "
                                          >Add</span
                                        >
                                      </div>
                                    </div>
                                    <div
                                      v-if="ingredient && ingredient.externalId"
                                      :class="
                                        ingredient.weightInGrams > 0 ||
                                        ingredient.volumeInMl > 0
                                          ? 'ingredient-external-id-main ingredient-external-id-main-no-g-ml'
                                          : 'ingredient-external-id-main'
                                      "
                                    >
                                      <div class="ingredient-external-id-text">
                                        Ing ID: {{ ingredient.externalId }}
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    v-if="
                                      ingredient.weightInGrams > 0 ||
                                      ingredient.volumeInMl > 0
                                    "
                                    :class="
                                      ingredient && ingredient.externalId
                                        ? 'ingredient-numeric-size'
                                        : 'ingredient-numeric-size-no-externalId ingredient-numeric-size'
                                    "
                                  >
                                    <div
                                      :class="
                                        ingredient && ingredient.externalId
                                          ? 'ingredient-numeric-size-container'
                                          : 'ingredient-numeric-size-container ingredient-numeric-size-no-externalId'
                                      "
                                    >
                                      <div class="ingredient-numeric-heading text-h3">
                                        Size:
                                      </div>
                                      <div class="numeric-text">
                                        <div
                                          class="ing-count"
                                          v-if="
                                            ingredient &&
                                            ingredient.weightInGrams &&
                                            ingredient.weightInGrams?.length > 0
                                          "
                                        >
                                          <span>
                                            {{
                                              ingredient &&
                                              ingredient.weightInGrams
                                                ? ingredient.weightInGrams
                                                : ""
                                            }}
                                          </span>
                                        </div>
                                        <span
                                          class="ing-value"
                                          v-if="ingredient.weightInGrams > 0"
                                          >g</span
                                        >
                                        <div
                                          class="ing-count"
                                          v-if="
                                            ingredient &&
                                            ingredient.volumeInMl &&
                                            ingredient.volumeInMl?.length > 0
                                          "
                                        >
                                          <span>
                                            {{
                                              ingredient &&
                                              ingredient.volumeInMl
                                                ? ingredient.volumeInMl
                                                : ""
                                            }}
                                          </span>
                                        </div>
                                        <span
                                          class="ing-value"
                                          v-if="ingredient.volumeInMl > 0"
                                          >ml</span
                                        >
                                        <div
                                          class="close-icon"
                                          @click="
                                            removeIngredientWeight(
                                              groupIndex,
                                              index
                                            )
                                          "
                                        >
                                          <img
                                            alt=""
                                            width="10px"
                                            height="12px"
                                            src="@/assets/images/exit-search.svg?skipsvgo=true"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                    <div
                                      v-if="ingredient && ingredient.externalId"
                                      :class="
                                        ingredient.weightInGrams > 0 ||
                                        ingredient.volumeInMl > 0
                                          ? 'ingredient-external-id-main ingredient-external-id-main-no-g-ml'
                                          : 'ingredient-external-id-main'
                                      "
                                    >
                                      <div class="ingredient-external-id-text">
                                        Ing ID: {{ ingredient.externalId }}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="internal-notes-section">
                                  <div class="internal-notes">
                                    <div Class="internal-text">
                                      Internal notes:
                                    </div>
                                  </div>
                                  <input
                                    class="internal-notes-input"
                                    @click="toggleDropdownOff()"
                                    v-model.trim="ingredient.remark"
                                    type="text"
                                    autocomplete="off"
                                    tabindex="0"
                                    @input="saveButtonEnable()"
                                    dir=""
                                    autocapitalize="off"
                                    placeholder="Enter internal notes"
                                    :disabled="
                                      isReadOnlyProvider(recipeData?.provider)
                                    "
                                  />
                                </div>
                                <div
                                  class="admin-weight"
                                  v-if="!isAdminCheck"
                                >
                                  <div
                                    v-if="
                                      ingredient.volumeInMl == 0 &&
                                      ingredient.weightInGrams == 0
                                    "
                                    class="ingredient-size"
                                  >
                                    <div class="ingredient-size-main-container">
                                      <span class="ingredient-size-text text-h3"
                                        >Size:</span
                                      >
                                      <div
                                        class="ingredient-add-size-container text-h3"
                                      >
                                        <span
                                          @click="
                                            isShowAddQuantityMethod(
                                              groupIndex,
                                              index,
                                              ingredient.name,
                                              ingredient.UOM,
                                              ingredient.quantity,
                                              ingredient.note
                                            )
                                          "
                                          >Add</span
                                        >
                                      </div>
                                    </div>
                                    <div
                                      v-if="ingredient && ingredient.externalId"
                                      class="ingredient-external-id-main"
                                    >
                                      <div class="ingredient-external-id-text">
                                        Ing ID: {{ ingredient.externalId }}
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    v-if="
                                      ingredient.weightInGrams > 0 ||
                                      ingredient.volumeInMl > 0
                                    "
                                    class="ingredient-numeric-size"
                                  >
                                    <div
                                      class="ingredient-numeric-size-container"
                                    >
                                      <div class="ingredient-numeric-heading text-h3">
                                        Size:
                                      </div>
                                      <div class="numeric-text">
                                        <div
                                          class="ing-count"
                                          v-if="
                                            ingredient &&
                                            ingredient.weightInGrams &&
                                            ingredient.weightInGrams?.length > 0
                                          "
                                        >
                                          <span>
                                            {{
                                              ingredient &&
                                              ingredient.weightInGrams
                                                ? ingredient.weightInGrams
                                                : ""
                                            }}
                                          </span>
                                        </div>
                                        <span
                                          class="ing-value"
                                          v-if="ingredient.weightInGrams > 0"
                                          >g</span
                                        >
                                        <div
                                          class="ing-count"
                                          v-if="
                                            ingredient &&
                                            ingredient.volumeInMl &&
                                            ingredient.volumeInMl?.length > 0
                                          "
                                        >
                                          <span>
                                            {{
                                              ingredient &&
                                              ingredient.volumeInMl
                                                ? ingredient.volumeInMl
                                                : ""
                                            }}
                                          </span>
                                        </div>
                                        <span
                                          class="ing-value"
                                          v-if="ingredient.volumeInMl > 0"
                                          >ml</span
                                        >
                                        <div
                                          class="close-icon"
                                          @click="
                                            removeIngredientWeight(
                                              groupIndex,
                                              index
                                            )
                                          "
                                        >
                                          <img
                                            alt=""
                                            width="10px"
                                            height="12px"
                                            src="@/assets/images/exit-search.svg?skipsvgo=true"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                    <div
                                      v-if="ingredient && ingredient.externalId"
                                      class="ingredient-external-id-main"
                                    >
                                      <div class="ingredient-external-id-text">
                                        Ing ID: {{ ingredient.externalId }}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="ingredient-minimize-view">
                                <img
                                  alt=""
                                  src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                                  class="dropdown-icon"
                                  @click="
                                    ingredientClickDropDown(
                                      groupIndex,
                                      index,
                                      ingredient.isDropDown
                                    )
                                  "
                                />
                              </div>
                            </div>
                          </div>
                        </tr>
                      </draggable>

                      <div
                        class="empty-group-ingredient"
                        v-show="
                          ingredientGroup.children?.length == 0 &&
                          ingredientGroup.displayGroup
                        "
                      >
                        <span class="text-ingredient">
                          Drag ingredients into ingredient group
                        </span>
                      </div>
                    </div>
                  </div>
                </tbody>
                <tbody>
                  <tr>
                    <td>
                      <span class="add-ingredient-add-group-shoppable-review-main-container">
                        <span v-if="recipeVariantSelectedLanguage == defaultLang" class="add-button-container">
                          <button type="button"
                            class="btn-green-outline add-ingredient-button"
                            :class="{
                              'hide-button': isReadOnlyProvider(recipeData?.provider) ||
                              recipeVariantSelectedLanguage !== defaultLang,
                            }"
                            @click="addIngredientConfirm()"
                            @keydown="preventEnterAndSpaceKeyPress($event)"
                            :disabled="isReadOnlyProvider(recipeData?.provider)">
                            {{ $t('COMMON.ADD_INGREDIENT') }}
                          </button>
                          <span class="add-group-button"
                            :class="{
                              'hide-button': isReadOnlyProvider(recipeData?.provider) ||
                              recipeVariantSelectedLanguage != defaultLang,
                            }"
                            @click="addGroupConfirm()">
                            <img alt="" class="add-image" src="@/assets/images/category-add.png" />
                            <span>{{ $t('COMMON.ADD_GROUP') }}</span>
                          </span>
                        </span>
                        <span v-if="recipeVariantSelectedLanguage == defaultLang" class="shoppable-review-main-container">
                          <span v-if="isRecipeLoading && hasDisplayShoppableReview && checkIngredientsLength()"
                            @click="shopPreview()" class="shoppable-review-button">
                            <img alt="" class="add-image" src="@/assets/images/Shop-preview-icon.jpeg" />
                            <span>{{ $t('COMMON.SHOPPABLE_REVIEW') }}</span>
                          </span>
                        </span>
                      </span>
                    </td>
                  </tr>
                </tbody>
                <tbody>
                  <tr>
                    <td>
                      <span
                        class="add-button-text"
                        :class="isReadOnlyProvider(recipeData?.provider) || recipeVariantSelectedLanguage != defaultLang || availableLang?.length < 2 ? 'hide-add-button' : ''"
                        data-v-inspector="src/pages/recipe-detail.vue:3469:17"
                      >
                        Adding and sorting changes will be applied to all variants.
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-if="!isUpdatingIngredient" class="loading">
              <div class="input-loading">
                <div class="loader-image"></div>
              </div>
            </div>
          </div>
          <recipeSteps
            v-if="!displayInstructionsPage"
            :isRecipeStepDropDown="isRecipeStepDropDown"
            :recipeDropDown="recipeDropDown"
            :stepDragStart="stepDragStart"
            :handleDrag="handleDrag"
            :stepDragEnd="stepDragEnd"
            :recipeVariantSelectedLanguage = "recipeVariantSelectedLanguage"
            :tasksData="tasksData"
            :defaultLang="defaultLang"
            :availableLang="availableLang"
            :videoUrl="videoUrl"
            :recipeData ="recipeData"
            :recipeStepFormDropDown="recipeStepFormDropDown"
            :editRecipeStep="editRecipeStep"
            :deleteModalVisible="deleteModalVisible"
            :addStepConfirm="addStepConfirm"
            :openRecipeStepVideoPopup="openRecipeStepVideoPopup"
            :transformIcon="transformIcon"
          ></recipeSteps>
          <div
            class="form-container notes-section"
            :class="{
              'disable-recipe-detail-content': isReadOnlyProvider(
                recipeData?.provider
              ),
            }"
          >
            <div class="recipe-variant-notes-text-container">
              <div class="recipe-variant-flag-section">
                <img
                  alt=""
                  v-if="recipeVariantSelectedLanguage == 'en-US'"
                  class="recipe-flag"
                  src="@/assets/images/us-flag.png"
                />
                <img
                  alt=""
                  v-if="recipeVariantSelectedLanguage == 'fr-FR'"
                  class="recipe-flag"
                  src="@/assets/images/france-flag.png"
                />
                <img
                  alt=""
                  v-if="recipeVariantSelectedLanguage == 'es-US'"
                  class="recipe-flag"
                  src="@/assets/images/spain-flag.png"
                />
              </div>
              <div class="form-title">
                <p class="form-title-header">Notes</p>
              </div>
            </div>
            <div class="description-section" id="nutrition_section_scroll">
              <textarea
                @click="toggleDropdownOff()"
                class="description-notes"
                id="notes"
                v-model="notes"
                @input="saveButtonEnable()"
                v-on:blur="setRecipeNotesInput()"
                :disabled="isReadOnlyProvider(recipeData?.provider)"
              ></textarea>
            </div>
          </div>
          <div class="form-container nutrition-section">
            <div class="nutrition-section-container">
              <div class="form-title">
                <div class="recipe-variant-flag-section">
                  <img
                    alt=""
                    v-if="recipeVariantSelectedLanguage == 'en-US'"
                    class="recipe-flag"
                    src="@/assets/images/us-flag.png"
                  />
                  <img
                    alt=""
                    v-if="recipeVariantSelectedLanguage == 'fr-FR'"
                    class="recipe-flag"
                    src="@/assets/images/france-flag.png"
                  />
                  <img
                    alt=""
                    v-if="recipeVariantSelectedLanguage == 'es-US'"
                    class="recipe-flag"
                    src="@/assets/images/spain-flag.png"
                  />
                </div>
                <p class="form-title-header">Nutrition</p>
              </div>
              <div class="nutrition-minimize-view">
                <div
                  class="nutrition-form-dropDown"
                  @click="nutritionDropdownMethodAsync()"
                >
                  <span class="info-text" v-show="isNutritionDropDown">{{ $t('COMMON.HIDE') }}</span>
                  <span class="info-text" v-show="!isNutritionDropDown"
                    >{{ $t('COMMON.VIEW') }}</span
                  >
                  <img
                    alt="arrow-icon"
                    :style="isNutritionDropDown ? transformIcon : ''"
                    src="@/assets/images/angle-arrow-down.svg?skipsvgo=true"
                    class="dropdown-icon"
                  />
                </div>
              </div>
            </div>
            <div class="nutrtion-maximize-view" v-show="isNutritionDropDown">
              <div class="nutrition-toogle-serving-view-container">
                <div class="nutrition-selection-container">
                  <div
                    :class="
                      isNutritionDropDownIcon
                        ? 'nutrition-drop-down-container box-container'
                        : 'nutrition-drop-down-container box-container disable-drop-down-box'
                    "
                    @click="showNutritionDropDown()"
                    id="nutrition-drop-down"
                  >
                    <span
                      v-if="selectedNutritionType == 'perServing'"
                      class="selected-nutrition"
                      >per serving</span
                    >
                    <span
                      v-if="selectedNutritionType == 'per100g'"
                      class="selected-nutrition"
                    >
                      per 100g</span
                    >
                    <img
                      alt=""
                      v-if="isNutritionDropDownIcon"
                      class="nutrition-dropdown-icon"
                      src="@/assets/images/arrow-right.png"
                      :class="{
                        rotate: isNutritionDropdownResult,
                      }"
                    />
                  </div>
                  <ul
                    v-if="isNutritionDropdownResult"
                    class="nutrition-autocomplete-results"
                  >
                    <div
                      v-for="(result, index) in nutritionServingList"
                      :key="index"
                    >
                      <li
                        class="nutrition-autocomplete-result"
                        @click="selectedNutrition(result.type)"
                      >
                        <p>{{ result.name }}</p>
                      </li>
                    </div>
                  </ul>
                </div>
                <div
                  class="nutrition-yield-serving-main-section"
                  v-if="selectedNutritionType == 'perServing'"
                >
                  <div class="serving-size-section">
                    <div class="serving-text-section">Serving Size</div>
                    <div class="serving-input-section">
                      <input
                        type="text"
                        @click="toggleDropdownOff()"
                        class="input-section"
                        id="nutrition-serving-size"
                        @paste="handlePaste($event, 'nutritionServingSize')"
                        @input="saveButtonEnable()"
                        autocomplete="off"
                        v-model.trim="nutritionServingSize"
                        :class="{
                          'disable-recipe-detail-content': isReadOnlyProvider(
                            recipeData?.provider
                          ),
                        }"
                        :disabled="
                          isReadOnlyProvider(recipeData?.provider) ||
                          recipeVariantSelectedLanguage != defaultLang
                        "
                      />
                    </div>
                  </div>
                  <div class="serving-size-per-container-section">
                    <div class="serving-text-section">
                      Serving Size per Container
                    </div>
                    <div class="serving-input-section">
                      <input
                        type="text"
                        @click="toggleDropdownOff()"
                        class="input-section"
                        @input="saveButtonEnable()"
                        id="nutrition-serving-size-per-container"
                        autocomplete="off"
                        @paste="
                          handlePaste(
                            $event,
                            'nutritionServingSizePerContainer'
                          )
                        "
                        @blur="
                          checkServingSizePerContainer(
                            nutritionServingSizePerContainer
                          )
                        "
                        v-model.trim="nutritionServingSizePerContainer"
                        :disabled="
                          isReadOnlyProvider(recipeData?.provider) ||
                          recipeVariantSelectedLanguage != defaultLang
                        "
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="nutrition-table-container">
                 <table class="nutrition-table">
                  <caption></caption>
                  <thead class="nutrition-table-head">
                    <tr>
                      <th class="nutrition-name-head">Nutrient name</th>
                      <th class="nutrition-sub-name-head">Sub-nutrient name</th>
                      <th class="nutrition-value-head">Nutr. value</th>
                      <th class="nutrition-value-unit-head">
                        Nutr. value unit
                      </th>
                      <th class="nutrition-percentage-value-unit-head">
                        % Daily Value*
                      </th>
                    </tr>
                  </thead>
                  <tbody class="nutrition-table-body">
                    <tr
                      v-for="(item, index) in choosePerServingPer100g()"
                      :key="index"
                      class="nutrition-table-body-row"
                    >
                      <td
                        class="nutrition-name-column"
                        v-if="item && !item.subNutrientName"
                      >
                        {{ item && item.nutrientName ? item.nutrientName : "" }}
                      </td>
                      <td
                        class="nutrition-name-column"
                        v-if="item && item.subNutrientName"
                      ></td>
                      <td
                        class="nutrition-sub-name-column"
                        v-if="item && item.subNutrientName"
                      >
                        {{ item && item.nutrientName ? item.nutrientName : "" }}
                      </td>
                      <td
                        class="nutrition-sub-name-column"
                        v-if="item && !item.subNutrientName"
                      ></td>
                      <td class="nutrition-value-column">
                        <div class="nutrition-value-area-section">
                          <input
                            @input="
                              saveButtonEnable(),
                                debounceNutritionalDVP(item),
                                displayNutritionData(
                                  item.valueAmount,
                                  index,
                                  choosePerServingPer100g()
                                )
                            "
                            type="Number"
                            @click="toggleDropdownOff(), disableScroll()"
                            @paste="
                              handlePaste($event, 'nutritionAmount', index)
                            "
                            class="input-section no-scroll nutrition-value-area"
                            :id="`nutrition-value${index}`"
                            autocomplete="off"
                            v-on:blur="
                              checkNutritionalData(
                                choosePerServingPer100g(),
                                item.valueAmount,
                                index
                              )
                            "
                            v-model.trim="item.valueAmount"
                            @keypress="restrictNumericInput($event)"
                            min="0"
                            max="10000"
                            :class="{
                              'disable-nutrition-detail-content':
                                isReadOnlyProvider(recipeData?.provider) ||
                                recipeVariantSelectedLanguage != defaultLang ||
                                item.isHeader,
                            }"
                            :disabled="
                              isReadOnlyProvider(recipeData?.provider) ||
                              recipeVariantSelectedLanguage != defaultLang ||
                              item.isHeader
                            "
                          />
                        </div>
                      </td>
                      <td class="nutrition-value-unit-column">
                        {{
                          item.valueUnit && !item.isHeader
                            ? item.valueUnit
                            : item.nutrientUnit && !item.isHeader
                            ? item.nutrientUnit
                            : ""
                        }}
                      </td>
                      <td
                        class="nutrition-percentage-value-unit-column"
                        v-if="item && item.hasDvp && !item.isHeader"
                      >
                        {{
                          item.dvpValue !== ""
                            ? Number(Number(item.dvpValue).toFixed(2)) + "%"
                            : ""
                        }}
                      </td>
                      <td
                        class="nutrition-percentage-value-unit-column"
                        v-if="item && !item.hasDvp && item.isHeader"
                      ></td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="nutrition-notes-text-section">
                <span class="nutrition-text text-light-h4"
                  >* Percent Daily Values based on a 2,000 calorie diet. Your
                  daily value may be higher or lower depending on your calorie
                  needs.</span
                >
              </div>
            </div>
          </div>
        </div>
        <addInstructions
          v-if="displayInstructionsPage"
          :instructionIdx="instructionIdx"
          :editInstructionClicked="editInstructionClicked"
          :instructionIndex="instructionIndex"
          :closeInstructionPageAsync="closeInstructionPageAsyncAsync"
          :stepsIngredientPopUp="stepsIngredientPopUp"
          :recipeIsin="recipeIsin"
          :ingredientsUomList="ingredientsUomList"
          :recipeName="
            recipeData && recipeData.title && recipeData.title[defaultLang]
              ? recipeData.title[defaultLang]
              : ''
          "
          :project="project"
          :availableLang="availableLang"
          :defaultLang="defaultLang"
          :recipeVariantSelectedLanguage="recipeVariantSelectedLanguage"
        ></addInstructions>
        <shoppableReview
          v-if="isShowShopPreview"
          :recipeImage="recipeImage"
          :ingredientsData="ingredientsData[defaultLang]"
          :recipeName="
            recipeData && recipeData.title && recipeData.title[defaultLang]
              ? recipeData.title[defaultLang]
              : ''
          "
          :recipeSubtitle="
            recipeData &&
            recipeData.subtitle &&
            recipeData.subtitle[defaultLang]
              ? recipeData.subtitle[defaultLang]
              : ''
          "
          :recipeID="recipeID"
          :recipeProvider="recipeData.provider"
          :imageUrlLinkUpdate="imageUrlLinkUpdate"
          :isAdminCheck="isAdminCheck"
          :hostNameImageUrlLinkUpdate="hostNameImageUrlLinkUpdate"
        >
        </shoppableReview>
        <technicalIssueModal
          v-if="isTechnicalIssuePopupVisible"
          :hasUnpublishedDateTimeout="hasUnpublishedDateTimeout"
          :hasPublishedDateTimeout="hasPublishedDateTimeout"
          :closeTechnicalIssueModal="closeTechnicalIssueModal"
        >
        </technicalIssueModal>
        <Modal v-if="isOpenPreviewRecipe" @close="closeModal">
          <template #noProductMatches>
            <div class="open-preview-recipe-modal">
              <div class="recipe-main-preview-header">
                <div class="recipe-preview-header text-h2">
                  {{ $t('COMMON.RECIPE_PREVIEW') }}
                </div>
              </div>
              <img
                alt=""
                class="close-preview-recipe-modal"
                @click="closeModal"
                src="@/assets/images/exit-gray.png"
              />
              <div class="open-preview-recipe-main">
                <recipePreview
                  :recipeVariantSelectedLanguage="recipeVariantSelectedLanguage"
                  :selectedTags="selectedTags"
                  :selectedCategories="selectedCategories"
                  :selectedDiets="selectedDiets"
                  :selectedAllergens="selectedAllergens"
                  :isDisplayAllergens="isDisplayAllergens"
                  :recipeImage="recipeImage"
                  :servings="servings"
                  :availableServings="availableServings"
                  :hour="hour"
                  :minute="minute"
                  :cookHour="cookHour"
                  :cookMinute="cookMinute"
                  :prepHour="prepHour"
                  :prepMinute="prepMinute"
                  :tasksData="tasksData"
                  :ingredientsData="ingredientsData ? ingredientsData : []"
                  :productVideo="productVideo"
                  :ingredientsCount="ingredientsCount"
                  :isCampaignModified="isCampaignModified"
                  :hasShowPublishButton="hasShowPublishButton"
                  :isLinkPresent="isLinkPresent"
                  :linkURLImage="linkURLImage"
                  :urlLinkUpdate="urlLinkUpdate"
                  :slugData="slugData"
                  :hostNameUrlLinkUpdate="hostNameUrlLinkUpdate"
                  :recipeAttributionAuthor="recipeAttributionAuthor"
                  :recipeAttributionExternalId="recipeAttributionExternalId"
                  :selectedPublisherImage="selectedPublisherImage"
                  :selectedPublisherName="selectedPublisherName"
                  :availableLang="availableLang"
                  :recipeData="recipeData"
                  :imageUrlLinkUpdate="imageUrlLinkUpdate"
                  :hostNameImageUrlLinkUpdate="hostNameImageUrlLinkUpdate"
                  :nutrientTableData="nutrientTableData"
                  :nutritionServingSizePerContainer="
                    nutritionServingSizePerContainer
                  "
                  :nutritionServingSize="nutritionServingSize"
                  :ingredientsUomList="ingredientsUomList"
                  :checkVideoPopupStatus="checkVideoPopupStatus"
                  :recipePrice="recipePrice"
                  :recipeCurrency="recipeCurrency"
                ></recipePreview>
              </div>
            </div>
          </template>
        </Modal>
        <Modal
          v-show="isPreviewModalVisible"
          @close="closeModal"
          id="editProductMatches"
        >
          <template #editProductMatches>
            <div class="edit-product-matches-modal">
              <div class="edit-product-modal-content">
                <div class="edit-description">
                  Are you sure you want to preview this recipe? Any unsaved
                  changes will be lost.
                </div>
                <div class="edit-product-matches-button-container">
                  <button type="button"
                    class="btn-green"
                    @click="backPreviewConfirm()"
                  >
                    {{ $t('BUTTONS.CONFIRM_BUTTON') }}
                  </button>
                  <button type="button"
                    class="disabled-button"
                    @click="closeModal"
                  >
                    {{ $t('BUTTONS.CANCEL_BUTTON') }}
                  </button>
                </div>
              </div>
            </div>
          </template>
        </Modal>
        <Modal
          v-show="isAddIngredientModalVisible"
          @close="closeModal"
          id="editProductMatches"
        >
          <template #editProductMatches>
            <div class="edit-product-matches-modal add-ingredient-modal">
              <div
                class="edit-product-modal-content add-ingredient-modal-content"
              >
                <img
                  alt=""
                  class="close-ingredient-modal"
                  @click="closeModal"
                  src="@/assets/images/exit-gray.png"
                />
                <div class="edit-description add-ingredient-text">
                  Add Ingredient
                </div>
                <div class="input-ingredient">
                  <div class="input-setion-text">
                    Ingredient info (quantity, unit, name)
                    <span class="red-text">*</span>
                  </div>
                  <input
                    type="text"
                    @click="toggleDropdownOff()"
                    @keypress="preventSpecialCharacters($event)"
                    class="input-section-ingredient"
                    id="ingredientsDataId"
                    autocomplete="off"
                    v-model="ingredientsDataText"
                    placeholder="(Ex: 1 cup chicken broth)"
                  />
                </div>
                <div class="input-ingredient">
                  <div class="input-setion-text">
                    Ingredient note (optional)
                  </div>
                  <input
                    type="text"
                    @click="toggleDropdownOff()"
                    @keypress="preventSpecialCharacters($event)"
                    class="input-section-ingredient"
                    id="ingredientsDataId"
                    autocomplete="off"
                    v-model="ingredientsDataNotes"
                    placeholder="(Ex: use water if you don’t have broth)"
                  />
                </div>
                <div class="edit-product-matches-button-container">
                  <button type="button"
                    id="addButton"
                    :class="
                      ingredientsDataText.trim() != ''
                        ? 'btn-green'
                        : 'btn-green disabled-button'
                    "
                    @click="addIngredientRow()"
                  >
                    {{ $t('COMMON.ADD') }}
                  </button>
                </div>
              </div>
            </div>
          </template>
        </Modal>
        <deleteModal
          v-if="isDeleteScheduleModalVisible"
          :closeModal="closeScheduleModal"
          :productInfoTitle="'Do you want to delete the current schedule?'"
          :productionDescriptionRed="productionDescriptionRedText"
          :deleteItem="deleteSelectedScheduleRecipeAsync"
          :availableLanguage="availableLang?.length"
        />
        <deleteModal
          v-if="isDeleteModalVisible"
          :closeModal="closeModal"
          :productInfoTitle="deleteModalInfoTitle"
          :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
          :productDescriptionTwo="deleteModalInfoName"
          :deleteItem="deleteModalFunction"
          :availableLanguage="availableLang?.length"
        />
        <addIngredientGroupStep
          v-if="isAddStepConfirm"
          :closeModal="closeModal"
          :closeAddConfirmModal="closeAddConfirmModal"
          :openAddPopUp="addStepRow"
          :addItem="'Step'"
        />
        <addIngredientGroupStep
          v-if="isAddIngredientConfirm"
          :closeModal="closeModal"
          :closeAddConfirmModal="closeAddConfirmModal"
          :openAddPopUp="openIngredientPopUp"
          :addItem="'Ingredient'"
        />
        <addIngredientGroupStep
          v-if="isAddGroupConfirm"
          :closeModal="closeModal"
          :closeAddConfirmModal="closeAddConfirmModal"
          :openAddPopUp="addGroup"
          :addItem="'Group'"
        />
        <Modal v-show="isIngredientKeywordsPopupModal" @close="closeModal">
          <template #nutrition>
            <div class="main-ingredient-keywords-popup-modal">
              <div class="main-edit-keyword-section">
                <div class="edit-keyword-section">
                  Add keywords* to ingredient
                </div>
                <div class="close-image-icon">
                  <img
                    alt=""
                    @click="closeModal"
                    src="@/assets/images/exit-gray.png"
                  />
                </div>
              </div>
              <div v-if="tempingdataquantity || tempingdatauom || tempingdataname" class="ingredients-quantity-section text-title-2 font-normal">
                {{ tempingdataquantity }} {{ tempingdatauom }},{{
                  tempingdatanotes
                }}
                <span class="ingredients-text-section text-title-2">{{
                  tempingdataname
                }}</span>
              </div>
              <div class="input-container">
                <div
                  :class="
                    keywordData?.length > 0
                      ? 'ingredient-keyword-after-edit text-light-h3'
                      : 'ingredient-keyword-container text-light-h3'
                  "
                  @click="focusKeywordInput($event)"
                >
                  <img
                    alt=""
                    :class="
                      keywordData?.length > 0
                        ? 'cross-icon-edit'
                        : 'cross-icon-off'
                    "
                    src="@/assets/images/exit-gray.png"
                    @click="removeAllKeyword()"
                  />
                  <div class="selected-ingredient-keyword-main">
                    <div
                      v-for="(data, index) in keywordData"
                      class="selected-ingredient-keyword-popup text-h4 font-light"
                      :key="index"
                    >
                      <div class="ingredient-data-keyword">
                        <div
                          class="ingredient-data-keyword-section"
                          :class="{
                            'simple-data-tooltip': isProductNameTooltipVisible,
                          }"
                          :data-tooltip-text="isProductNameTooltipVisible && data"
                        >
                          <div
                            class="data-truncated"
                            :id="`ingredientePopProductName${index}`"
                            @mouseover="checkIngredientPopNameIndex(index)"
                            @mouseleave="hideIngredientNamePopToolTip(index)"
                          >
                            {{ data }}
                          </div>
                        </div>
                        <img
                          alt=""
                          @click="removeKeyWord(index)"
                          class="remove-ingredient-keyword-image"
                          src="@/assets/images/close.svg?skipsvgo=true"
                        />
                      </div>
                    </div>
                    <input
                      type="text"
                      :class="
                        keywordData?.length > 0
                          ? 'keywords'
                          : 'keywords-full-version text-h3'
                      "
                      v-model.trim="tempKeyword"
                      @blur="inputDataKeyword(tempKeyword)"
                      @keyup.enter="inputDataKeyword(tempKeyword)"
                      id="keywords-name"
                      @click="toggleDropdownOff()"
                      autocomplete="off"
                      tabindex="0"
                      dir=""
                      autocapitalize="off"
                      :placeholder="
                        keywordData?.length == 0
                          ? 'List keywords separated by a comma (optional)'
                          : 'Enter keyword(s)'
                      "
                    />
                  </div>
                </div>
              </div>
              <div class="keyword-article">
                *Keywords set at the recipe level will override the global
                keywords (when applicable).
              </div>
              <div class="global-keywords">Global keywords:</div>
              <div class="main-section-breadcrumb-global-keywords">
                <div
                  v-for="(keywords, keyIndex) in recievedGlobalKeywords"
                  :key="keyIndex"
                  class="breadcrumb-global-keywords"
                >
                  <div
                    :id="`ingredientePopupProductName${keyIndex}`"
                    @mouseover="checkIngredientPopupName(keyIndex)"
                    @mouseleave="hideIngredientNamePopupTip(keyIndex)"
                    class="display-keyword"
                    :class="{
                      'simple-data-tooltip': isIngTooltipVisible,
                    }"
                    :data-tooltip-text="isIngTooltipVisible && keywords"
                  >
                    {{ keywords }}
                  </div>
                </div>
              </div>
              <div
                v-if="recievedGlobalKeywords?.length == 0"
                class="global-keywords-empty"
              >
                None.
              </div>
              <div v-if="recievedGlobalKeywords?.length" class="btn-div"></div>
              <div class="button-div-container">
              <div class="btn-div">
                <button type="button"
                  class="btn-green-outline"
                  @click="closeModal"
                >
                  {{ $t('BUTTONS.CANCEL_BUTTON') }}
                </button>
              </div>
              <div class="btn-div">
                <button type="button"
                  :class="
                    !hasKeywordModified
                      ? 'btn-green disabled-button'
                      : 'btn-green'
                  "
                  @click="addKeywordBackword()"
                >
                  {{ buttonString }}
                </button>
              </div>
            </div>
          </div>
          </template>
        </Modal>
        <Modal v-show="isAddSortConfirm">
          <template #recipeVariantModal>
            <div class="recipe-variant-modal">
              <div class="recipe-variant-image">
                <img alt="" src="@/assets/images/Sort-icon.png" />
              </div>
              <div class="recipe-variant-content">
                <div class="recipe-variant-title">Change sorting?</div>
                <div class="recipe-variant-desc">
                  New sort will apply to all variant recipes.
                </div>
                <div class="recipe-variant-button-container">
                  <button type="button"
                    class="btn-green-outline"
                    @click="addSortConfirm()"
                  >
                    {{ $t('BUTTONS.CANCEL_BUTTON') }}
                  </button>
                  <button type="button"
                    class="btn-green"
                    @click="closeAddSortConfirmModal()"
                  >
                    {{ $t('BUTTONS.CONFIRM_BUTTON') }}
                  </button>
                </div>
              </div>
            </div>
          </template>
        </Modal>
        <deleteModal
          v-if="isDeleteGroupModal"
          :closeModal="closeModal"
          :productInfoTitle="'Delete Group?'"
          :productDescriptionOne="'Are you sure you want to delete this group and'"
          :productDescriptionTwo="'its ingredients?'"
          :deleteItem="deleteGroup"
          :availableLanguage="availableLang?.length"
        />
        <deleteModal
          v-if="isDeleteRecipeVariant"
          :closeModal="closeModal"
          :productInfoTitle="'Delete Recipe Variant?'"
          :productDescriptionOne="'Are you sure you want to remove this variant from the'"
          :productDescriptionTwo="'recipe?'"
          :deleteItem="deleteRecipeVariantAsync"
          :availableLanguage="availableLang?.length"
        />
        <deleteModal
          v-if="isDeleteVideoModal"
          :closeModal="closeModal"
          :productInfoTitle="deleteVideoInfoTitle"
          :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
          :productDescriptionTwo="deleteVideoInfoName"
          :deleteItem="deleteVideo"
          :availableLanguage="availableLang?.length"
        />
        <deleteModal
          v-if="isDeleteImageModal"
          :closeModal="closeModal"
          :productInfoTitle="deleteImageInfoTitle"
          :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
          :productDescriptionTwo="deleteImageInfoName"
          :deleteItem="deleteImage"
          :availableLanguage="availableLang?.length"
        />
        <Modal
          v-show="hasRecipeVariantLanguagePopup"
          @close="closeModal"
          id="isRecipeVariant"
        >
          <template #recipeVariant>
            <div class="recipe-variant-modal-container">
              <div class="recipe-variant">
                <span>{{ $t('SELECT_LANGUAGE') }}</span>
                <button type="button" @click="closeModal" class="btn-reset">
                  <img alt="close" src="@/assets/images/exit-gray.png" />
                </button>
              </div>
              <div class="recipe-variant-language-dropdown">
                <div
                  :class="
                    recipeVariantLanguageList &&
                    recipeVariantLanguageList?.length > 1
                      ? 'recipe-variant-selected-language'
                      : 'recipe-variant-selected-language dropdown-disabled'
                  "
                  @click="
                    recipeVariantLanguageList &&
                    recipeVariantLanguageList?.length > 1
                      ? showRecipeVariantLanguageMatches()
                      : ''
                  "
                >
                  <span>
                    <img
                      alt=""
                      :src="
                        recipeVariantLanguageList &&
                        recipeVariantLanguageList[0] &&
                        recipeVariantLanguageList[0].language &&
                        recipeVariantLanguageList[0].languageFlag
                          ? recipeVariantLanguageList[0].languageFlag
                          : ''
                      "
                    />
                    <p>
                      {{
                        recipeVariantLanguageList &&
                        recipeVariantLanguageList[0] &&
                        recipeVariantLanguageList[0].language &&
                        recipeVariantLanguageList[0].language_name
                          ? recipeVariantLanguageList[0].language_name
                          : ""
                      }}<span
                        v-if="
                          recipeVariantLanguageList &&
                          recipeVariantLanguageList[0] &&
                          recipeVariantLanguageList[0].default_check
                        "
                      >
                        (default)</span
                      >
                    </p>
                  </span>
                  <img
                    alt=""
                    src="@/assets/images/arrow-right.png"
                    class="dropdown-icon"
                    v-if="
                      recipeVariantLanguageList &&
                      recipeVariantLanguageList?.length > 1
                    "
                    :class="{
                      rotate: hasRecipeVariantLanguageResult,
                    }"
                  />
                </div>
                <ul
                  v-if="hasRecipeVariantLanguageResult"
                  class="autocomplete-results"
                >
                  <li
                    v-for="(result, index) in recipeVariantLanguageList"
                    :key="index"
                    class="recipe-language-list"
                    :class="{
                      'autocomplete-result': true,
                      'is-active': '',
                    }"
                    @click="setRecipeVariantLanguageMatches(result)"
                  >
                    <img
                      alt=""
                      :src="
                        result && result.languageFlag
                          ? result.languageFlag
                          : ''
                      "
                    />
                    <p>
                      {{
                        result && result.language_name
                          ? result.language_name
                          : ""
                      }}<span v-if="result && result.default_check">
                        (default)</span
                      >
                    </p>
                  </li>
                </ul>
              </div>
              <div class="recipe-variant-add-button">
                <button type="button"
                  @click="setRecipeVariantLanguageAsync()"
                  @keydown="preventEnterAndSpaceKeyPress($event)"
                  class="btn-green"
                >
                  <p>{{ $t('COMMON.ADD') }}</p>
                </button>
              </div>
            </div>
          </template>
        </Modal>
        <saveModal
          v-show="isPublishModalVisible"
          :hasSlugCheckConfirm="hasSlugCheckConfirm"
          :hasSlugExist="hasSlugExist"
          :availableLang="availableLang"
          :closeModal="closeModal"
          :saveAndPublishFunction="savePublishRecipeAsync"
          :buttonName="'Publish'"
          :description="$t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')"
          :imageName="'@/assets/images/publish-variant-icon.png'"
        />
        <saveModal
          v-show="isSaveModalVisible"
          :isUnpublishingText="isUnpublishingText"
          :hasSlugCheckConfirm="hasSlugCheckConfirm"
          :hasSlugExist="hasSlugExist"
          :availableLang="availableLang"
          :closeModal="closeModal"
          :saveAndPublishFunction="saveRecipeButtonAsync"
          :recipeData="recipeData"
          :buttonName="$t('BUTTONS.SAVE_BUTTON')"
          :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
          :imageName="'@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png'"
        />
        <Modal v-if="isUnableToLeaveVariant" @close="closeModal">
          <template #nutrition>
            <div class="unable-to-leave-recipe-varients">
              <div class="popup">
                <div class="cross-image-div">
                  <img
                    class="cross-image"
                    src="@/assets/images/Group 2.png"
                    alt="cross"
                  />
                </div>
                <div class="text-heading text-title-3">
                  <span
                    >Unable to save Recipe<span
                      v-show="recipeVariantSelectedLanguage !== 'en-US'"
                    >
                      Variant</span
                    >. Fill in the required fields:</span
                  >
                </div>
              </div>
              <div
                class="unable-to-leave-empty-field"
                v-if="
                  isUnableToSaveRecipeName ||
                  isUnableToSaveServings ||
                  isUnableToSaveAvailableServings ||
                  isUnableToSaveTime ||
                  isUnableToSaveIngredients ||
                  isUnableToSaveSteps ||
                  isUnableToSaveTitleSteps ||
                  isUnableToSaveGroupName ||
                  isUnableToSaveStepInstruction ||
                  isUnableToSaveStepInstruction ||
                  isUnableToSaveStepIngredientsRawText ||
                  isUnableToSavePlateUp
                "
              >
                <ul class="error-list">
                  <li v-if="isUnableToSaveRecipeName" class="unable-to-save-list">
                    Recipe Name
                  </li>
                  <li v-if="isUnableToSaveServings" class="unable-to-save-list">
                    Servings
                  </li>
                  <li
                    v-if="isUnableToSaveAvailableServings"
                    class="unable-to-save-list"
                  >
                    Available Servings
                  </li>
                  <li v-if="isUnableToSaveTime" class="unable-to-save-list">
                    Total Time (required if Prep Time or Cook Time is set)
                  </li>
                  <li v-if="isUnableToSaveIngredients" class="unable-to-save-list">
                    Ingredients
                  </li>
                  <li v-if="isUnableToSaveSteps" class="unable-to-save-list">
                    Recipe Steps
                  </li>
                  <li v-if="isUnableToSaveTitleSteps" class="unable-to-save-list">
                    Recipe Steps Title
                  </li>
                  <li v-if="isUnableToSaveGroupName" class="unable-to-save-list">
                    Ingredients Group Name
                  </li>
                  <li v-if="isUnableToSavePlateUp" class="unable-to-save-list">
                    Plate-up image/image link/video/video link
                  </li>
                  <li
                    v-if="isUnableToSaveStepInstruction"
                    class="unable-to-save-list"
                  >
                    Recipe Step Instructions
                  </li>
                  <li
                    v-if="isUnableToSaveStepInstruction"
                    class="unable-to-save-list"
                  >
                    Ingredient's Food Item
                  </li>
                  <li
                    v-if="isUnableToSaveStepIngredientsRawText"
                    class="unable-to-save-list"
                  >
                    Ingredient's Name
                  </li>
                </ul>
              </div>
              <div class="button-container">
                <button type="button" class="btn-red" @click="closeModal">
                  Ok
                </button>
              </div>
            </div>
          </template>
        </Modal>
        <Modal v-if="isScheduleWarningPopupVisible" @close="closeModal">
          <template #nutrition>
            <div class="unable-to-leave-recipe-varients">
              <div class="popup">
                <div class="cross-image-div">
                  <img
                    class="cross-image"
                    src="@/assets/images/cross-icon-red.png"
                    alt="cross"
                  />
                </div>
                <div class="text-heading text-title-3">
                  <span>Unable to Schedule Recipe. Fill in the required fields:</span>
                </div>
              </div>
              <div class="unable-to-leave-empty-field">
                <ul class="error-list">
                  <li class="unable-to-save-list">Recipe Start and End Date</li>
                </ul>
              </div>
              <div style="float: right; margin-right: 10px;">
                <button type="button" class="btn-red" @click="closeModal">Ok</button>
              </div>
            </div>
          </template>
        </Modal>
        <Modal v-if="isShowAddQuantity" @close="closeModal">
          <template #nutrition>
            <div class="add-ingredient-weights-popup">
              <div class="add-ingredient-headin-text-and-cross-logo">
                <div class="add-ingredient-heading-text">
                  Add the total size ingredient
                </div>
                <img
                  class="add-ingredient-cross-image"
                  @click="closeModal"
                  src="@/assets/images/exit-gray.png"
                  alt="cross"
                />
              </div>
              <div class="sub-text-container text-title-4">
                <p class="sub-text">
                  {{ tempingdata.quantity }} {{ tempingdata.uom }},
                  {{ tempingdata.notes }}
                  <span class="sub-text-bold-text">{{ tempingdata.name }}</span>
                </p>
              </div>
              <div class="input-container">
                <div class="input-box-container text-h3 font-normal">
                  <input
                    autocomplete="off"
                    class="input-box"
                    v-model.trim="inputIngredientWeight"
                    placeholder="Qty (whole number only)"
                    @keypress="restrictNumericInput($event), restrictSpecialCharacters($event)"
                    name="text-box"
                    maxlength="4"
                  />
                </div>
                <div
                  v-for="(info, index) in searchListData"
                  :key="index"
                  :class="
                    info.isChecked
                      ? 'add-ingredients-background radio-button-container-one'
                      : 'radio-button-container-one'
                  "
                >
                  <div class="round">
                    <input v-if="info.isChecked" type="radio" />
                    <label for="round" aria-label="round" @click="selectedRecipeStatuses(info)"></label>
                  </div>
                  <div class="radio-btn-text-container">
                    <div
                      @click="selectedRecipeStatuses(info)"
                      class="radio-btn-text text-title-2 font-normal"
                    >
                      {{ info.name }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="btn-div">
                <button type="button"
                  class="btn-green-outline"
                  @click="closeModal"
                >
                  Cancel
                </button>
                <button type="button"
                  :class="
                    inputIngredientWeight !== '' &&
                    disableAddButton(inputIngredientWeight) &&
                    Number(inputIngredientWeight) != 0
                      ? 'btn-green'
                      : 'btn-green disabled-button'
                  "
                  @click="setIngredientWeight()"
                >
                  {{$t('COMMON.ADD')}}
                </button>
              </div>
            </div>
          </template>
        </Modal>
        <Modal v-show="isErrorOccuredModal" @close="closeModal">
          <template #nutrition>
            <div class="error-save-recipe-modal">
              <div class="publish-content">
                <div class="publish-head" v-if="clickedOnPublish == ''">
                  <div
                    v-if="
                      isUnableToSaveRecipeName ||
                      isUnableToSaveServings ||
                      isUnableToSaveAvailableServings ||
                      isUnableToSaveTime ||
                      isUnableToSaveIngredients ||
                      isUnableToSaveSteps ||
                      isUnableToSaveTitleSteps ||
                      isUnableToSaveGroupName ||
                      isUnableToSaveStepInstruction ||
                      isUnableToSaveStepInstruction ||
                      isUnableToSaveStepIngredientsRawText ||
                      isUnableToSavePlateUp
                    "
                  >
                    <span class="unable-to-save" v-if="isSchedulePublishVisible">
                      Unable to Schedule Recipe
                    </span>
                    <span class="unable-to-save" v-else>
                      Unable to Save Recipe
                    </span>
                    <br />
                    <span class="unable-to-save-title">
                      The following field(s) cannot be empty:
                    </span>
                    <br />
                    <ul class="error-list">
                      <li
                        v-if="isUnableToSaveRecipeName"
                        class="unable-to-save-list"
                      >
                        Recipe Name
                      </li>
                      <li v-if="isUnableToSaveServings" class="unable-to-save-list">
                        Servings
                      </li>
                      <li
                        v-if="isUnableToSaveAvailableServings"
                        class="unable-to-save-list"
                      >
                        Available Servings
                      </li>
                      <li v-if="isUnableToSaveTime" class="unable-to-save-list">
                        Total Time (required if Prep Time or Cook Time is set)
                      </li>
                      <li
                        v-if="isUnableToSaveIngredients"
                        class="unable-to-save-list"
                      >
                        Ingredients
                      </li>
                      <li v-if="isUnableToSaveSteps" class="unable-to-save-list">
                        Recipe Steps
                      </li>
                      <li
                        v-if="isUnableToSaveTitleSteps"
                        class="unable-to-save-list"
                      >
                        Recipe Steps Title
                      </li>
                      <li v-if="isUnableToSaveGroupName" class="unable-to-save-list">
                        Ingredients Group Name
                      </li>
                      <li v-if="isUnableToSavePlateUp" class="unable-to-save-list">
                        Plate-up image/image link/video/video link
                      </li>
                      <li
                        v-if="isUnableToSaveStepInstruction"
                        class="unable-to-save-list"
                      >
                        Recipe Step Instructions
                      </li>
                      <li
                        v-if="isUnableToSaveStepInstruction"
                        class="unable-to-save-list"
                      >
                        Ingredient's Food Item
                      </li>
                      <li
                        v-if="isUnableToSaveStepIngredientsRawText"
                        class="unable-to-save-list"
                      >
                        Ingredient's Name
                      </li>
                    </ul>
                  </div>
                  <span
                    class="unable-to-save-and-publish"
                    v-if="
                      !isUnableToSaveRecipeName &&
                      !isUnableToSaveServings &&
                      !isUnableToSaveAvailableServings &&
                      !isUnableToSaveTime &&
                      !isUnableToSaveIngredients &&
                      !isUnableToSaveSteps &&
                      !isUnableToSaveTitleSteps &&
                      !isUnableToSaveGroupName &&
                      !isUnableToSaveStepInstruction &&
                      !isUnableToSaveStepInstruction &&
                      !isUnableToSaveStepIngredientsRawText &&
                      !isUnableToSavePlateUp
                    "
                    >Unable to Save Recipe
                    <p class="error-resolution-text">
                      An unexpected error occurred. Please contact
                      <a href="mailto: <EMAIL>."
                        ><EMAIL>.</a
                      >
                    </p>
                  </span>
                </div>
                <div
                  class="publish-head"
                  v-if="clickedOnPublish == 'clickedOnPublish'"
                >
                  <div
                    v-if="
                      isUnableToSaveRecipeName ||
                      isUnableToSaveServings ||
                      isUnableToSaveAvailableServings ||
                      isUnableToSaveTime ||
                      isUnableToSaveIngredients ||
                      isUnableToSaveSteps ||
                      isUnableToSaveTitleSteps ||
                      isUnableToSaveGroupName ||
                      isUnableToSaveStepInstruction ||
                      isUnableToSaveStepInstruction ||
                      isUnableToSaveStepIngredientsRawText ||
                      isUnableToSavePlateUp
                    "
                  >
                    <span class="unable-to-save">
                      Unable to Save & Publish Recipe
                    </span>
                    <br />
                    <span class="unable-to-save-title">
                      The following field(s) cannot be empty:
                    </span>
                    <br />
                    <ul class="error-list">
                      <li
                        v-if="isUnableToSaveRecipeName"
                        class="unable-to-save-list"
                      >
                        Recipe Name
                      </li>
                      <li v-if="isUnableToSaveServings" class="unable-to-save-list">
                        Servings
                      </li>
                      <li
                        v-if="isUnableToSaveAvailableServings"
                        class="unable-to-save-list"
                      >
                        Available Servings
                      </li>
                      <li v-if="isUnableToSaveTime" class="unable-to-save-list">
                        Total Time (required if Prep Time or Cook Time is set)
                      </li>
                      <li
                        v-if="isUnableToSaveIngredients"
                        class="unable-to-save-list"
                      >
                        Ingredients
                      </li>
                      <li v-if="isUnableToSaveSteps" class="unable-to-save-list">
                        Recipe Steps
                      </li>
                      <li
                        v-if="isUnableToSaveTitleSteps"
                        class="unable-to-save-list"
                      >
                        Recipe Steps
                      </li>
                      <li v-if="isUnableToSaveGroupName" class="unable-to-save-list">
                        Ingredients Group Name
                      </li>
                      <li
                        v-if="isUnableToSaveStepInstruction"
                        class="unable-to-save-list"
                      >
                        Recipe Step Instructions
                      </li>
                      <li
                        v-if="isUnableToSaveStepInstruction"
                        class="unable-to-save-list"
                      >
                        Ingredient's Food Item
                      </li>
                      <li
                        v-if="isUnableToSaveStepIngredientsRawText"
                        class="unable-to-save-list"
                      >
                        Ingredient's Name
                      </li>
                      <li v-if="isUnableToSavePlateUp" class="unable-to-save-list">
                        Plate-up image/image link/video/video link
                      </li>
                    </ul>
                  </div>
                  <span
                    class="unable-to-save-and-publish"
                    v-if="
                      !isUnableToSaveRecipeName &&
                      !isUnableToSaveServings &&
                      !isUnableToSaveAvailableServings &&
                      !isUnableToSaveTime &&
                      !isUnableToSaveIngredients &&
                      !isUnableToSaveSteps &&
                      !isUnableToSaveTitleSteps &&
                      !isUnableToSaveGroupName &&
                      !isUnableToSaveStepInstruction &&
                      !isUnableToSaveStepInstruction &&
                      !isUnableToSaveStepIngredientsRawText &&
                      !isUnableToSavePlateUp
                    "
                  >
                    Unable to Save & Publish Recipe
                    <p class="error-resolution-text">
                      An unexpected error occurred. Please contact
                      <a href="mailto: <EMAIL>."
                        ><EMAIL>.</a
                      >
                    </p>
                  </span>
                </div>
                <div class="button-container">
                  <button type="button" class="btn-green" @click="closeModal">
                    Okay
                  </button>
                </div>
              </div>
            </div>
          </template>
        </Modal>
        <cancelModal
          v-if="isConfirmModalVisible"
          :isCampaignModifiedFromShoppableReview="
            isCampaignModifiedFromShoppableReview
          "
          :availableLang="availableLang"
          :callConfirm="backToRecipe"
          :closeModal="closeModal"
        />
        <Modal v-if="isConfirmScheduleRecipePopupVisible && !$nuxt.isOffline">
          <template #BannerForm>
            <div class="recipe-schedule-form-modal-container">
              <div class="recipe-schedule-sub-container">
                <div class="recipe-schedule-top-container">
                  <div class="schedule-text">
                    Please, confirm the Schedule for the recipe.
                  </div>
                  <div @click="closeModal" class="close-icon">
                    <img
                      alt=""
                      class="close-icon-image"
                      src="@/assets/images/exit-gray.png"
                    />
                  </div>
                </div>
                <div class="publish-text">Published Dates:</div>
                 <div class="recipe-schedule-date-picker-container">
                  <CalendarPicker
                    v-model="range"
                    :isRange="true"
                    :isInRecipe="true"
                    :isInRecipePopup="true"
                    :startDate="recipeSchedulePublishDate"
                    :endDate="recipeScheduleEndDate"
                  />
                </div>
                <div class="recipe-schedule-bottom-container">
                  <div class="recipe-schedule-button-section">
                    <button type="button"
                      class="btn-green-outline"
                      @click="recipeSchedulePopupClose()"
                    >
                      {{ $t('BUTTONS.CANCEL_BUTTON') }}
                    </button>
                    <button type="button"
                      :class="
                        recipeSchedulePublishDate !== '' &&
                        recipeScheduleEndDate !== ''
                          ? 'btn-green'
                          : 'btn-green disabled-button'
                      "
                      @click="saveRecipeButtonAsync()"
                    >
                      <span
                        v-if="
                          (isSchedulePublishDateDisable() ||
                            isSameScheduleDate()) &&
                          recipeButtonPublishState == 'Publish'
                        "
                        class="button-text"
                        >Publish</span
                      >
                      <span v-else class="button-text">Schedule</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </Modal>
        <Modal v-show="isImageLinkPopup" @close="closeModal">
          <template #editProductMatches>
            <div class="edit-product-matches-modal add-ingredient-modal">
              <div
                class="edit-product-modal-content add-ingredient-modal-content"
              >
                <img
                  alt=""
                  class="close-ingredient-modal"
                  @click="closeModal"
                  src="@/assets/images/exit-gray.png"
                />
                <div class="edit-description add-ingredient-text">
                  Paste image URL link
                </div>
                <div class="input-ingredient">
                  <input
                    type="text"
                    class="input-section-upload-link"
                    id="ingredientsDataId"
                    autocomplete="off"
                    v-model="uploadImageLinkText"
                    placeholder="Enter image source URL link "
                    @click="toggleDropdownOff()"
                    @input="urlCheckingDataForImage()"
                  />
                  <span
                    v-if="
                      !isValidImageURLlink && uploadImageLinkText !== ''
                    "
                    class="error-message"
                    >Invalid URL</span
                  >
                  <img
                    alt=""
                    class="link-image"
                    src="@/assets/images/link.svg?skipsvgo=true"
                  />
                  <img
                    alt=""
                    v-if="
                      !isValidImageURLlink && uploadImageLinkText !== ''
                    "
                    class="error-image"
                    src="@/assets/images/red-info.svg?skipsvgo=true"
                  />
                  <div
                    v-if="isImageLoadingIcon"
                    class="input-loading-image-url"
                  >
                    <div class="loader-image-url"></div>
                  </div>
                  <img
                    alt=""
                    v-if="hasCorrectImageIcon"
                    class="correct-icon-image-url"
                    src="@/assets/images/check.svg?skipsvgo=true"
                  />
                </div>
                <div class="edit-product-matches-button-container">
                  <button type="button"
                    :class="
                      isAddButtonValidImageURL
                        ? 'btn-green'
                        : 'btn-green disabled-button'
                    "
                    @click="addButtonImageURLlink()"
                  >
                    Add
                  </button>
                </div>
              </div>
            </div>
          </template>
        </Modal>
        <Modal v-show="isVideoLinkPopup" @close="closeModal">
          <template #editProductMatches>
            <div class="edit-product-matches-modal add-ingredient-modal">
              <div
                class="edit-product-modal-content add-ingredient-modal-content"
              >
                <img
                  alt=""
                  class="close-ingredient-modal"
                  @click="closeModal"
                  src="@/assets/images/exit-gray.png"
                />
                <div class="edit-description add-ingredient-text">
                  Paste YouTube link or other video source URL
                </div>
                <div class="input-ingredient">
                  <input
                    type="text"
                    class="input-section-upload-link"
                    id="ingredientsDataId"
                    autocomplete="off"
                    v-model="uploadLinkText"
                    placeholder="Enter video source URL or YouTube link"
                    @click="toggleDropdownOff()"
                    @input="urlCheckingData()"
                  />
                  <span v-if="!isValidURLlink" class="error-message"
                    >Invalid URL</span
                  >
                  <img
                    alt=""
                    class="link-image"
                    src="@/assets/images/link.svg?skipsvgo=true"
                  />
                  <img
                    alt=""
                    v-if="!isValidURLlink"
                    class="error-image"
                    src="@/assets/images/red-info.svg?skipsvgo=true"
                  />
                  <div
                    v-if="isVideoLoadingIcon"
                    class="input-loading-image-url"
                  >
                    <div class="loader-image-url"></div>
                  </div>
                  <img
                    alt=""
                    v-if="hasCorrectVideoIcon"
                    class="correct-icon-image-url"
                    src="@/assets/images/check.svg?skipsvgo=true"
                  />
                </div>
                <div class="edit-product-matches-button-container">
                  <button type="button"
                    :class="
                      isAddButtonValidURL
                        ? 'btn-green'
                        : 'btn-green disabled-button'
                    "
                    @click="addButtonURLlink()"
                  >
                    Add
                  </button>
                </div>
              </div>
            </div>
          </template>
        </Modal>
        <recipeVideo
          v-if="hasOpenVideo"
          :videoLink="videoLink"
          :closeModal="closeModal"
        />
        <Modal v-show="isEditCategoryVariantName" @close="closeModal">
          <template #nutrition>
            <div class="add-variant-category-name">
              <div class="top-section-recipe-variant">
                <span class="add-variant-text text-h2"
                  >Edit a Variant Category name</span
                >
                <img
                  alt=""
                  class="close-icon"
                  src="@/assets/images/exit-gray.png"
                  @click="closeModal"
                />
              </div>
              <div class="middle-section-recipe-variant">
                <span class="variant-category-select">
                  <p v-if="recipeVariantSelectedLanguage == 'fr-FR'">French</p>
                  <p v-if="recipeVariantSelectedLanguage == 'es-US'">Spanish</p>
                  &nbsp;Variant Category&nbsp;
                  <span>«</span>
                  <p class="category-name">
                    {{ editCategoriesVariantName.trim() }}
                  </p>
                  <span>»</span>
                </span>
                <input
                  type="text"
                  placeholder="Enter Name"
                  class="variant-name-input text-title-2 font-normal"
                  v-model="categoryVariantName"
                />
              </div>
              <div class="bottom-section-recipe-variant">
                <button type="button"
                  class="btn-green"
                  :class="
                    categoryVariantName.trim().length != ''
                      ? 'btn-green'
                      : 'disabled-button btn-green'
                  "
                  @click="saveVariantName('category')"
                  @keydown="preventEnterAndSpaceKeyPress($event)"
                >
                  {{ $t('BUTTONS.SAVE_BUTTON') }}
                </button>
              </div>
            </div>
          </template>
        </Modal>
        <Modal v-show="isEditTagVariantName" @close="closeModal">
          <template #nutrition>
            <div class="add-variant-category-name">
              <div class="top-section-recipe-variant">
                <span class="add-variant-text text-h2">Edit a Variant Tag name</span>
                <img
                  alt=""
                  class="close-icon"
                  src="@/assets/images/exit-gray.png"
                  @click="closeModal"
                />
              </div>
              <div class="middle-section-recipe-variant">
                <span class="variant-category-select">
                  <p v-if="recipeVariantSelectedLanguage == 'fr-FR'">French</p>
                  <p v-if="recipeVariantSelectedLanguage == 'es-US'">Spanish</p>
                  &nbsp;Variant Tag&nbsp;
                  <span>«</span>
                  <p>{{ editTagsVariantName.trim() }}</p>
                  <span>»</span>
                </span>
                <input
                  type="text"
                  placeholder="Enter Name"
                  class="variant-name-input text-title-2 font-normal"
                  v-model="tagVariantName"
                />
              </div>
              <div class="bottom-section-recipe-variant">
                <button type="button"
                  class="btn-green"
                  :class="
                    tagVariantName.trim().length != ''
                      ? 'btn-green'
                      : 'disabled-button btn-green'
                  "
                  @click="saveVariantName('tag')"
                  @keydown="preventEnterAndSpaceKeyPress($event)"
                >
                  {{ $t('BUTTONS.SAVE_BUTTON') }}
                </button>
              </div>
            </div>
          </template>
        </Modal>
        <savingModal
          v-show="isRecipeSaving"
          :status="
            isSchedulePublishVisible &&
            recipeVariantSelectedLanguage === defaultLang &&
            !hasShowPublishButton
              ? 'scheduling'
              : hasShowPublishButton
              ? 'publishing'
              : 'saving'
          "
        />
        <deletingModal v-show="isDeletingModalVisible" />
    </content-wrapper>
  </client-only>
</template>
<script setup>
import { ref, computed, onMounted, watch, watchEffect } from 'vue';

// service
import RecipeService from "@/services/RecipeService";
import axios from "axios";
import OrganizationsService from "@/services/OrganizationsService";
import { debounce } from "lodash";
import AIService from "@/services/AIService";

// components
import recipeVideo from "@/components/recipeVideo";
import savingModal from "@/components/saving-modal";
import cancelModal from "@/components/cancel-modal";
import saveModal from "@/components/save-modal";
import deletingModal from "@/components/deleting-modal";
import Modal from "@/components/Modal";
import deleteModal from "@/components/delete-modal";
import addIngredientGroupStep from "@/components/add-ingredient-group-step";
import addInstructions from "@/components/addInstructions";
import shoppableReview from "@/components/shoppableReview";
import technicalIssueModal from "@/components/technical-issue-modal.vue";
import recipePreview from "@/components/recipe-preview";
import recipeImageVideoSection from "@/components/pages/recipe-detail/recipe-image-video-section.vue";
import recipeServingdescription from "@/components/pages/recipe-detail/recipe-servings-description.vue";
import mediaUploadSection from "@/components/pages/recipe-detail/upload-media-section.vue";
import recipeGenerateSection from "@/components/pages/recipe-detail/recipe-generate-section.vue";
import recipeTitleSection from "@/components/pages/recipe-detail/recipe-title-section.vue";
import recipeSteps from "@/components/recipe-steps.vue";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";

// modal
import { parseEventData, CONTENT_GENERATION_STEP, CONTENT_GENERATION_TYPE } from "@/models/recipe-generator.model";

// constants
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";

// composables
import { useTimeUtils } from '~/composables/useTimeUtils';
import { useRefUtils } from '@/composables/useRefUtils';
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useDelayTimer } from "@/composables/useDelayTimer";
import { useEventUtils } from "@/composables/useEventUtils";
import { useProjectLang } from "@/composables/useProjectLang";

// utility
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from 'vue-router';

// images
import defaultOrganizationsImage from "@/assets/images/default_organizations.png";
import emptyVideo from "@/assets/images/empty-video.png";
import emptyImage from "@/assets/images/recipe-detail-upload.png";


const drag = ref(null)
const isOnline = ref(navigator.onLine);
const { formatDateToReadableString, convertToTimestamp } = useTimeUtils();
const { getRef } = useRefUtils();
const { delay } = useDelayTimer();
const {  triggerLoading, routeToPage, generateUUID, extractTimeParts, isEmptyOrWhitespace } = useCommonUtils();
const { preventNonNumericInput, restrictSpecialCharacters, restrictNumericInput, restrictToAlphanumeric, preventSpecialCharacters, preventEnterAndSpaceKeyPress, restrictToAlphabets, onEscapeKeyPress } = useEventUtils();
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const { $tracker, $eventBus, $auth, $keys } = useNuxtApp();

// create local reactive variables
const recipeIsin = ref(null)
const step = ref({});
const nutritionKey = ref({});
const selectedNutritionType = ref("");
const isBatchGenerator = ref(false);
const imagesGenerationTimeout = ref(null);
const imagesGenerationAbortTime = ref(180);
const isRecipeImageGenerating = ref(false);
const buttonStateBeforeImageGeneration = ref(null);
const videoLink = ref("");
const isDeleteScheduleModalVisible = ref(false);
const productionDescriptionRedText = ref("");
const project = ref({});
const deleteModalInfoTitle = ref('');
const deleteModalInfoName = ref('');
const isVideoLoaded = ref(false);
const hasStepDragged = ref(false);
const isFirstDrag = ref(false);
const items = ref([]);
const stepItems = ref([]);
const recipePreviewVideo = ref(false);
const imageUrlThumbnailUpdate = ref("");
const imageUrlLinkUpdate = ref("");
const buttonString = ref("");
const ADD = ref(t('COMMON.ADD'));
const OVERRIDE = ref($keys.KEY_NAMES.OVERRIDE);
const EDIT = ref(t('BUTTONS.EDIT_BUTTON'));
const tempKeyword = ref("");
const hasKeywordModified = ref(false);
const isDeletingModalVisible = ref(false);
const isRecipeAuthorFocus = ref(false);
const isRecipeExternalIdFocus = ref(false);
const isMaxImagePopupVisible = ref(false);
const showLoader = ref(false);
const isMaxVideoPopupVisible = ref(false);
const categoryVariantName = ref("");
const hostNameImageUrlLinkUpdate = ref("");
const editCategoriesVariantName = ref("");
const editCategoriesVariantImage = ref("");
const editCategoriesVariantIsin = ref("");
const tagVariantName = ref("");
const editTagsVariantName = ref("");
const editTagsVariantIsin = ref("");
const isEditCategoryVariantName = ref(false);
const hasUnpublishedDateTimeout = ref(false);
const hasPublishedDateTimeout = ref(false);
const isTechnicalIssuePopupVisible = ref(false);
const isEditTagVariantName = ref(false);
const availableLang = ref([]);
const availableLanguageDropdown = ref(false);
const isInvalidImageModalVisible = ref(false);
const isIngredientNameVisible = ref(false);
const isNoteTooltipVisible = ref(false);
const isIngTooltipVisible = ref(false);
const isKeywordTooltipVisible = ref(false);
const isProductNameTooltipVisible = ref(false);
const isIngredientProductNameVisible = ref(false);
const requiredFieldsTooltip = 'Fill in the required fields in the recipe';
const addPublisherTooltip = 'Add Publisher from Organizations page';
const categoryAutomaticName = 'Category name was automatically created. Please edit for correct translations.';
const tagAutomaticName = 'Tag name was automatically created. Please edit for correct translations.';
const isInvalidVideoModalVisible = ref(false);
const isDataLoading = ref(true);
const isRecipeSaving = ref(false);
const publisherDropdownResult = ref(false);
const currencyDropdownResult = ref(false);
const recipeCurrency = ref([
  {
    subName: "usd",
    isChecked: true,
    name: "Dollars",
  },
]);
const selectedCurrency = ref("");
const selectedPublisherName = ref("Select One");
const selectedPublisherImage = ref("");
const recipeButtonState = ref("Save");
const recipeButtonPublishState = ref("Publish");
const publisherDataList = ref([]);
const isSchedulePublishVisible = ref(false);
const isAddScheduleButtonVisible = ref(true);
const isSchedulingCalendarVisible = ref(false);
const recipeAttributionAuthor = ref("");
const recipeAttributionOrganization = ref("");
const attributionAuthor = ref(null);
const recipeAttributionExternalId = ref("");
const recipeName = ref("");
const recipeSubtitle = ref("");
const isAddButtonValidURL = ref(false);
const isAddButtonValidImageURL = ref(false);
const isValidURLlink = ref(true);
const isValidImageURLlink = ref(true);
const slugEdit = ref("");
const slugData = ref({});
const isVideoLinkPopup = ref(false);
const isIngredientKeywordsPopupModal = ref(false);
const isImageLinkPopup = ref(false);
const ingredientsData = ref([]);
const recipeID = ref("");
const isDeleteModalVisible = ref(false);
const isDeleteGroupModal = ref(false);
const deleteGroupIndex = ref(0);
const isAddIngredientModalVisible = ref(false);
const categoriesList = ref([]);
const cancelVideo = ref({});
const categoryQuery = ref("");
const selectedCategories = ref([]);
const selectedDiets = ref([]);
const selectedAllergens = ref([]);
const dietsList = ref([]);
const allergensList = ref([]);
const isDisplayAllergens = ref(false);
const dietQuery = ref("");
const allergensQuery = ref("");
const isDietAutocompleteOpen = ref(false);
const isAllergensAutocompleteOpen = ref(false);
const dietAutocompleteArrowCounter = ref(0);
const allergensAutocompleteArrowCounter = ref(0);
const selectedTags = ref([]);
const uploadLinkText = ref("");
const isImageIsMain = ref(false);
const uploadImageLinkText = ref("");
const isCategoryAutocompleteOpen = ref(false);
const categoryAutocompleteArrowCounter = ref(0);
const tagsList = ref([]);
const isDeleteImageModal = ref(false);
const deleteImageInfoTitle = ref("");
const deleteImageInfoName = ref("");
const tagsQuery = ref("");
const transformIcon = ref({
  transform: "rotate(180deg)",
});
const isRecipeStepDropDown = ref(false);
const isTagsAutocompleteOpen = ref(false);
const tagsAutocompleteArrowCounter = ref(0);
const taskID = ref([]);
const isIngredientMatchAutocompleteOpen = ref(false);
const ingredientMatchAutocompleteArrowCounter = ref(0);
const ingredientsUomList = ref([]);
const ingredientUomQuery = ref("");
const selectedUom = ref([]);
const isIngredientUomAutocompleteOpen = ref(false);
const ingredientUomAutocompleteArrowCounter = ref(0);
const recipeImage = ref("");
const productVideo = ref("");
const description = ref("");
const servings = ref("");
const isUnpublishingText = ref(false);
const isTotalTimeAvailable = ref(false);
const hour = ref("");
const minute = ref("");
const second = ref("");
const prepHour = ref("");
const prepMinute = ref("");
const prepSecond = ref("");
const cookHour = ref("");
const cookMinute = ref("");
const cookSecond = ref("");
const file = ref([]);
const videoFile = ref([]);
const isProductMatchesModalVisible = ref(false);
const includedProducts = ref([]);
const isShowPublishSection = ref(false);
const hasShowPublishButton = ref(false);
const recipeData = ref({});
const isDeleteIngredient = ref(false);
const deleteIngredientIndex = ref(null);
const groupIndexForIngredient = ref(null);
const isDeleteRecipeStep = ref(false);
const deleteRecipeStepIndex = ref(null);
const displayInstructionsPage = ref(false);
const instructionIndex = ref(0);
const instructionIdx = ref(0);
const imageResponseUrl = ref("");
const videoResponseUrl = ref("");
const notes = ref("");
const uomIngredient = ref([]);
const isShowSaveButton = ref(false);
const isSaveModalVisible = ref(false);
const isPublishModalVisible = ref(false);
const isPreviewModalVisible = ref(false);
const errorMessage = ref("");
const deletedInstruction = ref([]);
const deleteInstruction = ref([]);
const availableServings = ref([]);
const searchedUomList = ref([]);
const searchedUomText = ref("");
const isCampaignModified = ref(false);
const isCampaignModifiedFromShoppableReview = ref(false);
const tagsTotal = ref("");
const fromPopUpTags = ref(0);
const sizePopUpTags = ref(10);
const fromPopUpCategory = ref(0);
const sizePopUpCategory = ref(10);
const fromPopUpDiet = ref(0);
const categoriesTotal = ref("");
const clickedLoadMore = ref("");
const isIngredientNameTyped = ref(false);
const clickedTagsLoadMore = ref("");
const isConfirmModalVisible = ref(false);
const isErrorOccuredModal = ref(false);
const clickedOnPublish = ref("");
const isRecipeSuccess = ref("");
const yieldData = ref("");
const isImageModified = ref(false);
const isVideoModified = ref(false);
const editInstructionClicked = ref([]);
const tasksData = ref([]);
const newIdForIngredient = ref(0);
const indexOfStep = ref("");
const isUnableToSaveRecipeName = ref(false);
const isUnableToSaveServings = ref(false);
const isUnableToSaveAvailableServings = ref(false);
const isUnableToSavePlateUp = ref(false);
const isUnableToSaveTime = ref(false);
const isUnableToSaveIngredients = ref(false);
const isUnableToSaveSteps = ref(false);
const isIingredientUomAutocompleteOpen = ref (false);
const isUnableToSaveTitleSteps = ref(false);
const isUnableToSaveStepInstruction = ref(false);
const isUnableToSaveStepIngredientsRawText = ref(false);
const isUnableToSaveGroupName = ref(false);
const stepsIngredientPopUp = ref([]);
const isVideoPresent = ref(false);
const isImagePresent = ref(false);
const isLinkPresent = ref(false);
const hasOpenVideo = ref(false);
const videoDimension = ref("");
const ingredientsDataText = ref("");
const ingredientsDataNotes = ref("");
const isDeleteVideoModal = ref(false);
const deleteVideoInfoTitle = ref("");
const deleteVideoInfoName = ref("");
const uploadPercentage = ref(0);
const uploadVideoSize = ref(0);
const loadedVideoSize = ref(0);
const ingredientsCount = ref(0);
const linkURLImage = ref("@/assets/images/empty-video.png");
const urlLink = ref("");
const imageUrlLink = ref("");
const hostNameUrlLink = ref("");
const hostNameImageUrlLink = ref("");
const isOpenPreviewRecipe = ref(false);
const urlLinkUpdate = ref("");
const hostNameUrlLinkUpdate = ref("");
const ingredientMatchIsinData = ref("");
const hasSlugExist = ref(false);
const isOpenRecipeStepVideo = ref(false);
const isSlugInputWarning = ref(false);
const isSlugStatus = ref(false);
const isShowShopPreview = ref(false);
const isRecipeLoading = ref(false);
const isRecipeVariant = ref(true);
const isDeleteRecipeVariant = ref(false);
const hasRecipeVariantLanguagePopup = ref(false);
const hasRecipeVariantLanguageResult = ref(false);
const recipeVariantLanguage = ref("");
const recipeVariantLanguageList = ref([]);
const isAddIngredientConfirm = ref(false);
const isAddGroupConfirm = ref(false);
const isAddSortConfirm = ref(false);
const isAddStepConfirm = ref(false);
const isAddGroupPopup = ref(false);
const isDisplayAddIngredientPopup = ref(false);
const isShowAddStepsPopup = ref(false);
const recipeAttribution = ref(null);
const isShowFilterOption = ref(true);
const saveRemovedRecipeVariants = ref([]);
const initiallyVariantSupported = ref([]);
const hasEnableCategoryInfoIcon = ref(false);
const hasEnableTagInfoIcon = ref(false);
const isUpdatingIngredient = ref(true);
const isSlugToolTip = ref(false);
const recipeTxnId = ref("");
const isUnableToLeaveVariant = ref(false);
const isImageLinkPresent = ref(false);
const isImageLoadingIcon = ref(false);
const hasCorrectImageIcon = ref(false);
const hasCorrectVideoIcon = ref(false);
const isVideoLoadingIcon = ref(false);
const hasSlugCheckConfirm = ref(false);
const isShowAddQuantity = ref(false);
const tempUnit = ref("");
const inputIngredientWeight = ref("");
const tempgroupIndex = ref("");
const tempindex = ref("");
const searchListData = ref([
  { name: "Gram", isChecked: true },
  { name: "Milliliter", isChecked: false },
]);
const tempingdata = ref({});
const tempingdataname = ref("");
const tempingdatauom = ref("");
const tempingdataquantity = ref("");
const tempingdatanotes = ref("");
const cancelImage = ref({});
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const isNutritionDropDown = ref(false);
const isNutritionDropdownResult = ref(false);
const isNutritionDropDownIcon = ref(false);
const nutritionServingSize = ref("");
const nutritionServingSizePerContainer = ref("");
const nutritionDataList = ref([]);
const isBoostInSearch = ref(false);
const nutrientUnitsConstants = ref([]);
const recipePrice = ref("");
const displayNutritionalDataList = ref([]);
const keywordData = ref([]);
const isAdminCheck = ref(false);
const nutritionServingList = ref([
  { name: "per Serving", type: "perServing" },
  { name: "per 100g", type: "per100g" },
]);
const nutrientTableData = ref({
  perServing: [],
  per100g: [],
});
const perServingNutrientData = ref([]);
const isScheduledRecipeDeleted = ref(false);
const per100gNutrientData = ref([]);
const addRecipeNutrientType = ref("");
const addRecipeNutrientData = ref([]);
const ingredientNameList = ref([]);
const globalKeywords = ref([]);
const recievedGlobalKeywords = ref([]);
const hasDisableButtonDueToDimension = ref(false);
const hasDisplayShoppableReview = ref(false);
const isDisplayPublisher = ref(false);
const isConfirmScheduleRecipePopupVisible = ref(false);
const recipeSchedulePopupPublishDate = ref("");
const recipeSchedulePopupEndDate = ref("");
const recipeSchedulePublishDate = ref("");
const recipeScheduleEndDate = ref("");
const publishDate = ref(0);
const endDate = ref(0);
const dummyPublishDate = ref(0);
const dummyEndDate = ref(0);
const range = ref({
  start: null,
  end: null,
});
const rangePopup = ref({
  start: null,
  end: null,
});
const liveRangePopup = ref({
  start: null,
  end: null,
});
const scheduledRecipeIsin = ref("");
const statusesCSV = ref("");
const isRecipeScheduled = ref(false);
const isScheduleWarningPopupVisible = ref(false);
const isPageViewOverview = ref(false);
const generatorPromptData = ref('');
const isRecipeIncludeInHero = ref(false);
const recipeImageList = ref([]);
const config = ref({
  theme: "recipe-details-page",
  isRecipeVariant: false,
  uploadImage: false,
  confirmDelete: true,
  showErrorMessage: false,
  showNoMediaError: false,
  showMainErrorMessage: false,
});
const isMainImageNotSelected = ref(false);
const uploadMediaPopup = ref(false);
const uploadMediaName = ref('');
const isRecipeImageGeneratorVisible = ref(false);
const recipeVariantSelectedLanguage = ref("");
const { getProject, isAdmin, readyProject } = useProjectLang();
import { useNuxtApp } from '#app';
import { useStore } from "vuex";
import { useConfigStore } from "@/stores/config.js";

const store = useStore();
const configStore = useConfigStore();

const lang = computed(() => store.getters["userData/getDefaultLang"]);
const defaultLang = computed(() => store.getters["userData/getDefaultLang"]);
const finalAvailableLangs = computed(() => store.getters['userData/getAvailableLangs']);
const displayAddVariantButton = computed(() => {
  return (
    recipeVariantLanguageList.value.length &&
    isLanguageAvailable() &&
    recipeData.value.provider !== $keys.KEY_NAMES.FIMS
  );
});
const tooltipText = computed(() => {
  if (recipeVariantSelectedLanguage.value !== defaultLang.value && !availableLanguageDropdown.value) {
    return "Published state must be changed on the source recipe form.";
  }

  if (!hasShowPublishButton.value || !isShowSaveButton.value) {
    if (isSchedulePublishVisible.value && recipeVariantSelectedLanguage.value === defaultLang.value) {
      return "To publish a recipe, the schedule must be deleted.";
    }
  }

  if ((hasShowPublishButton.value || isShowSaveButton.value) && isSchedulePublishVisible.value && recipeVariantSelectedLanguage.value === defaultLang.value) {
    return "To unpublish a recipe, the schedule must be deleted.";
  }

  return "";
});
const publishTooltipText = computed(() => {
  if (
    hasShowPublishButton.value ||
    (isShowSaveButton.value &&
      recipeSchedulePublishDate.value === "" &&
      recipeScheduleEndDate.value === "")
  ) {
    return "To add a schedule, Publish toggle must be off.";
  }
  return "";
});
const isPreviewDeleteBtnDisabled = computed(() =>
  isRecipeImageGenerating.value ||
  uploadPercentage.value !== 0 && uploadPercentage.value !== 100 ||
  uploadImagePercentage.value !== 0 && uploadImagePercentage.value !== 100
);


onMounted(async () => {
  if ($auth.isAuthenticated.value) {
    readyProject(async ({ isProjectReady }) => {
      project.value = await getProject();
      if (!(project.value && project.value.id)) {
        router.push({ path: "/create-project" });
        return;
      }
      const queryParams = new URLSearchParams(window.location.search);
      isRecipeIncludeInHero.value = queryParams.get(QUERY_PARAM_KEY.IS_IN_HERO) === "true";
      recipeVariantSelectedLanguage.value = store.getters["userData/getDefaultLang"];
      store.dispatch("recipe/resetGeneratedPrompt");
      isDataLoading.value = true;
      isAdminCheck.value = await isAdmin.value;
      const sourceUrl = window.location.href;
      isPageViewOverview.value = sourceUrl.includes("OverviewPage");
      isBatchGenerator.value = sourceUrl.includes("iq-batch-generator");
      document.addEventListener("click", handleClickSelectLanguagePopup);
      document.addEventListener("click", handleClickOutsideVariant);
      document.addEventListener("click", handleClickOutsideCategories);
      document.addEventListener("click", handleClickOutsideDiets);
      document.addEventListener("click", handleClickOutsideAllergens);
      document.addEventListener("click", handleClickOutsideTags);
      document.addEventListener("click", handleClickOutside);
      document.addEventListener("input", handleTypeInput);
      document.addEventListener("dragend", dragIngredient);
      await getRecipeCategories();
      await getRecipeDiets();
      await getRecipeAllergens("checkAllergens");
      await getRecipeTags();
      await getRecipeUnitConfig();
      await getOrganizationsList();
      await getNutritionsListData();
      await getFeatureConfig();
      if (router.currentRoute.value.query.isin) {
        recipeID.value = router.currentRoute.value.query.isin;
        isShowPublishSection.value = true;
        await getRecipe(router.currentRoute.value.query.isin);
        await getGeneratorDataAsync(router.currentRoute.value.query.isin);
        recipeIsin.value = router.currentRoute.value.query.isin;
        isRecipeLoading.value = true;
      } else {
        isDataLoading.value = false;
        ingredientsData.value = {
          [recipeVariantSelectedLanguage.value]: emptyIngredientGroup(),
        };
        slugData.value = emptySlugGroup();
        recipeData.value = {
          boostInSearch: false,
          labels: [],
          price: {},
          countries: [],
          tags: {},
          categories: {},
          diets: [],
          ingredients: {},
          isin: "",
          langs: [],
          lastPublishDate: "",
          media: {},
          scope: "",
          slug: {},
          state: "",
          provider: "",
          status: {},
          tasks: [],
          time: {},
          title: {},
          yield: {},
          subtitle: {},
          attribution: {},
          description: {},
          nutrientTable: [],
          externalId: "",
        };
        let langData = {};
        if (defaultLang.value == "es-US") {
          langData = {
            language: defaultLang.value,
            default_check: true,
            language_name: "Spanish",
            languageFlag: "/images/flags/spain-flag.png",
          };
        } else if (defaultLang.value == "fr-FR") {
          langData = {
            language: defaultLang.value,
            default_check: true,
            language_name: "French",
            languageFlag: "/images/flags/france-flag.png",
          };
        } else if (defaultLang.value == "en-US") {
          langData = {
            language: defaultLang.value,
            default_check: true,
            language_name: "English",
            languageFlag: "/images/flags/us-flag.png",
          };
        }
        availableLang.value.push(langData);
      }

      $eventBus.on('savePreviewPage', saveRecipeButtonAsync);

      $eventBus.on('cancelShoppableReview', () => {
        isShowShopPreview.value = false;
      });

      $eventBus.on('saveShoppableReview', (data, isCampaignModifiedVal) => {
        if (
          ingredientsData.value?.[defaultLang.value]?.children
        ) {
          ingredientsData.value[defaultLang.value].children = data;
        }
        isCampaignModified.value = true;
        isCampaignModifiedFromShoppableReview.value = true;
        isShowShopPreview.value = false;
      });

      $eventBus.on("newInstructionsData", (payload) => {
        const { isin, modified } = payload || {};

        if (modified !== "data_not_changed") {
          isCampaignModified.value = true;
        }
        if (isin) {
          recipeIsin.value = isin;
        }
      });

      $eventBus.on("originalInstructionsData", ({ data, index, modified }) => {
        if (modified !== "data_not_changed") {
          isCampaignModified.value = true;
        }

        availableLang.value.forEach((lang) => {
          if (lang.language === recipeVariantSelectedLanguage.value && tasksData?.value?.[index]?.[lang.language]) {
            tasksData.value[index][lang.language].instructions = data.instructions;
            tasksData.value[index][lang.language].title = data.title;
            tasksData.value[index][lang.language].ingredients = data.ingredients;
            tasksData.value[index][lang.language].media = data.media;
          }

          tasksData.value.forEach((data, taskIndex) => {
            if (
              data &&
              data[lang.language] &&
              !data[lang.language].title
            ) {
              tasksData.value.splice(taskIndex, 1);
            }
          });
        });
      });

      $eventBus.on('publishPreviewPage', savePublishRecipeAsync);
      $eventBus.on($keys.KEY_NAMES.CAMPAIGN_MODIFIED, (isModified) => {
        if (isModified === false) {
          isCampaignModified.value = false;
        }
      });
    });
  } else {
    router.push({ path: "/login" });
  }
  if (finalAvailableLangs.value) {
    const isRecipeVariant = getDifference(finalAvailableLangs.value, availableLang.value);

    isRecipeVariant.forEach((lang) => {
      let langData = {};
      if (lang == "es-US") {
        langData = {
          language: lang,
          default_check: lang === defaultLang.value,
          language_name: "Spanish",
          languageFlag:"/images/flags/spain-flag.png",
        };
      } else if (lang == "fr-FR") {
        langData = {
          language: lang,
          default_check: lang === defaultLang.value,
          language_name: "French",
          languageFlag: "/images/flags/france-flag.png",
        };
      } else if (lang == "en-US") {
        langData = {
          language: lang,
          default_check: lang === defaultLang.value,
          language_name: "English",
          languageFlag: "/images/flags/us-flag.png",
        };
      }
      recipeVariantLanguageList.value.push(langData);
    });
  }
});
const setRecipePreviewImage = () => {
  let recipeImageFlag = false;

  if (recipeImageList.value?.length) {
    recipeImageList.value.forEach((item) => {
      if (item?.isMainImage) {
        recipeImageFlag = true;
        recipeImage.value = item?.url ?? "";

        if (item?.source === $keys.KEY_NAMES.EXTERNAL_LINK) {
          const url = new URL(item?.url ?? "");
          hostNameImageUrlLink.value = url.hostname || "";
          imageUrlLinkUpdate.value = item?.url ?? "";
          hostNameImageUrlLinkUpdate.value = hostNameImageUrlLink.value.toUpperCase();
          recipeImage.value = "";
        } else {
          hostNameImageUrlLink.value = "";
          imageUrlLinkUpdate.value = "";
          hostNameImageUrlLinkUpdate.value = "";
        }
      }
    });
  }

  if (!recipeImageFlag) {
    recipeImage.value = null;
    imageResponseUrl.value = null;
    hostNameImageUrlLink.value = "";
    imageUrlLinkUpdate.value = "";
    hostNameImageUrlLinkUpdate.value = "";
  }
};

const updateYieldData = (newYieldData) => {
  yieldData.value = newYieldData;
};

const updateTimeData = (timeData) => {
  hour.value = timeData.hour;
  minute.value = timeData.minute;
  second.value = timeData.second;
  prepHour.value = timeData.prepHour;
  prepMinute.value = timeData.prepMinute;
  prepSecond.value = timeData.prepSecond;
  cookHour.value = timeData.cookHour;
  cookMinute.value = timeData.cookMinute;
  cookSecond.value = timeData.cookSecond;
};

const updateDescriptionData = (descriptionData) => {
  description.value = descriptionData;
};

const handleUpdateServings = (newServings) => {
  servings.value = newServings;
};

const handleUpdateAvailableServings = (newAvailableServings) => {
  availableServings.value = newAvailableServings;
};

const updateRecipePrice = (newPrice) => {
  recipePrice.value = newPrice;
};
const getGeneratorDataAsync = async (isin) => {
  try {
    await store.dispatch("recipe/getSearchGeneratorDataAsync", {
      isin,
      lang: lang.value,
    });
    generatorPromptData.value = store.getters['recipe/getSearchGeneratedData']?.results?.[0]?.prompt || "";
  } catch (error) {
    console.error(error);
  }
};
const isAllRequiredFieldFilled = () => {
  return (
    recipeName.value &&
    servings.value &&
    availableServings.value &&
    ingredientsData.value &&
    ingredientsData.value[recipeVariantSelectedLanguage.value]?.children.some(
      (ingredientGroupData) => ingredientGroupData.children?.length
    ) && tasksData.value?.length
  );
};

const closeTechnicalIssueModal = () => {
  isTechnicalIssuePopupVisible.value = false;
};
const viewMoreDetail = () => {
  isTechnicalIssuePopupVisible.value = true;
};
const getErrorMessage = (isUnpublish) => {
  if (isUnpublish && hasUnpublishedDateTimeout.value) {
    return t('TECHNICAL_ISSUES.UNPUBLISH_TIMEOUT');
  } else if (!isUnpublish && hasPublishedDateTimeout.value) {
    return t('TECHNICAL_ISSUES.PUBLISH_TIMEOUT');
  }
};
const isLanguageAvailable = () => {
  recipeVariantLanguageList.value.forEach((data, index) => {
    availableLang.value.forEach((item) => {
      if (data.language === item.language) {
        recipeVariantLanguageList.value.splice(index, 1);
      }
    });
  });
  return true;
};
const patchScheduleSelectedRecipes = async (isin) => {
  scheduledRecipeIsin.value = isin;
  let payload = {
    isin: scheduledRecipeIsin.value,
    publishDate: publishDate.value,
    endDate: endDate.value,
  };
  if (isSchedulePublishDateDisable() || isSameScheduleDate()) {
    payload.publishDate = null;
  }
  try {
    await store.dispatch("recipeDetails/patchScheduleRecipes", {
      payload,
      lang: lang.value,
    });
  } catch (error) {
    console.error("Error in patchScheduleSelectedRecipes:", error);
  }
};

const postRecipeSchedule = async () => {
  let tempIsin = [];
  let isin = recipeData.value.isin ? recipeData.value.isin : recipeIsin.value;
  tempIsin.push(isin);
  let payload = {
    isins: tempIsin,
    publishDate: publishDate.value,
    endDate: endDate.value,
  };
  try {
    await store.dispatch("recipeDetails/postScheduleRecipes", {
      payload,
      lang: lang.value,
    });
  } catch (error) {
    console.error("Error in postRecipeSchedule:", error);
  }
};

const getEditRecipeScheduleData = async (isin) => {
  const params = {
    lang: lang.value,
    isins: isin ?? null,
    excludingState: 'unpublished',
  };
  try {
    await store.dispatch("recipe/getRecipeScheduleDataAsync", { params });
    const response = store.getters["recipe/getRecipeScheduleData"];
    if (response) {
      handleResponseErrors(response);
      setScheduleData(response);
      updateScheduleVisibility();
    }
  } catch (e) {
    console.error(e);
    isRecipeScheduled.value = false;
  }
};

const handleResponseErrors = (response) => {
  if (response?.errorCode === $keys.KEY_NAMES.UNPUBLISH_FAILED) {
    hasUnpublishedDateTimeout.value = true;
  } else if (response?.errorCode === $keys.KEY_NAMES.PUBLISH_FAILED) {
    hasPublishedDateTimeout.value = true;
  }
};

const setScheduleData = (response) => {
  isRecipeScheduled.value = true;
  publishDate.value = response.publishDate || 0;
  endDate.value = response.endDate || 0;
  dummyPublishDate.value = publishDate.value;
  dummyEndDate.value = endDate.value;
  recipeSchedulePublishDate.value = publishDate.value ? formatDateToReadableString(new Date(publishDate.value * 1000)) : "";
  recipeScheduleEndDate.value = endDate.value ? formatDateToReadableString(new Date(endDate.value * 1000)) : "";
  updateRangeDates();
};

const updateRangeDates = () => {
  const startDate = new Date(publishDate.value * 1000);
  const newEndDate = new Date(endDate.value * 1000);
  range.value = { start: startDate, end: newEndDate };
  rangePopup.value = { start: startDate, end: newEndDate };
  liveRangePopup.value = { start: startDate, end: newEndDate };
};

const updateScheduleVisibility = () => {
  if (recipeSchedulePublishDate.value && recipeScheduleEndDate.value) {
    isSchedulePublishVisible.value = true;
    isAddScheduleButtonVisible.value = false;
    updateButtonStates();
  }
};

const updateButtonStates = () => {
  const disablePublishDate = isSchedulePublishDateDisable();
  const sameScheduleDate = isSameScheduleDate();
  if (!disablePublishDate || !sameScheduleDate) {
    recipeButtonPublishState.value = "Schedule";
    recipeButtonState.value = "Schedule";
  }
  if (disablePublishDate || (sameScheduleDate && (hasShowPublishButton.value || isShowSaveButton.value))) {
    recipeButtonPublishState.value = "Publish";
    recipeButtonState.value = "Publish";
  }
};
const recipeSchedulePopupClose = () => {
  isConfirmScheduleRecipePopupVisible.value = false;
  recipeSchedulePopupPublishDate.value = "";
  recipeSchedulePopupEndDate.value = "";
  rangePopup.value = null;
  rangePopup.value = range.value;
};
const stepDragEnd = () => {
  if (availableLang.value?.length > 1 && !isFirstDrag.value) {
    isAddSortConfirm.value = true;
    hasStepDragged.value = true;
    isFirstDrag.value = true;
  }
};
const stepDragStart = () => {
  if (availableLang.value?.length > 1) {
    stepItems.value = [];
    if (tasksData.value && tasksData.value?.length) {
      stepItems.value = JSON.parse(JSON.stringify(tasksData.value));
    }
  }
};
const checkIngredientPosition = () => {
  if (availableLang.value?.length > 1) {
    items.value = [];
    if (
      ingredientsData.value &&
      ingredientsData.value[recipeVariantSelectedLanguage.value] &&
      ingredientsData.value[recipeVariantSelectedLanguage.value].children
    ) {
      items.value = JSON.parse(
        JSON.stringify(
          ingredientsData.value[recipeVariantSelectedLanguage.value].children
        )
      );
    }
  }
};
const addSortConfirm = () => {
  preventDragAsync();
  isAddSortConfirm.value = false;
};
const preventDragAsync = async () => {
  if (
    ingredientsData.value &&
    ingredientsData.value[recipeVariantSelectedLanguage.value] &&
    ingredientsData.value[recipeVariantSelectedLanguage.value].children &&
    !hasStepDragged.value
  ) {
    if (items.value?.length > 0) {
      ingredientsData.value[recipeVariantSelectedLanguage.value].children = [];
      items.value.forEach((item) => {
        ingredientsData.value[recipeVariantSelectedLanguage.value].children.push(item);
      });
    }
  } else if (
    hasStepDragged.value &&
    tasksData.value &&
    tasksData.value?.length &&
    stepItems.value &&
    stepItems.value?.length
  ) {
    tasksData.value = [];
    tasksData.value = stepItems.value;
  }
};
const checkIngredientsLength = () => {
  let ingredientsLength = 0;
  if (
    ingredientsData.value &&
    ingredientsData.value[lang.value] &&
    ingredientsData.value[lang.value].children
  ) {
    ingredientsData.value[lang.value].children.forEach((data) => {
      ingredientsLength += data.children?.length;
    });
  }
  return ingredientsLength > 0;
};
const deleteModalFunction = () => {
  if (isDeleteRecipeStep.value) {
    deleteRecipeStep();
    triggerLoading("newDeletedSuccess");
  } else if (isDeleteIngredient.value) {
    deleteIngredient();
    triggerLoading("newDeletedSuccess");
  } else {
    deleteRecipe();
  }
};

const backToRecipe = () => {
  backButtonConfirm();
  closeModal();
};

const getFeatureConfig = async () => {
  try {
    const featureConfig = configStore.features;
    hasDisplayShoppableReview.value = featureConfig?.shoppableReview || false;
    isDisplayPublisher.value = featureConfig?.organizations || false;
  } catch (e) {
    console.error(e);
  }
};

const totalTimeChange = () => {
  isTotalTimeAvailable.value = true;
};

const calculateTotalTime = () => {
  if (!isTotalTimeAvailable.value || (!hour.value && !minute.value && !second.value)) {
    isTotalTimeAvailable.value = false;
    const { totalHours, totalMinutes, totalSeconds } = calculateTotal();
    const normalizedTime = normalizeTime(totalHours, totalMinutes, totalSeconds);
    hour.value = normalizedTime.totalHours || "";
    minute.value = normalizedTime.totalMinutes || "";
    second.value = normalizedTime.totalSeconds || "";
  }
};

const calculateTotal = () => {
  const prepHourValue = parseInt(prepHour.value) || 0;
  const cookHourValue = parseInt(cookHour.value) || 0;
  const prepMinuteValue = parseInt(prepMinute.value) || 0;
  const cookMinuteValue = parseInt(cookMinute.value) || 0;
  const prepSecondValue = parseInt(prepSecond.value) || 0;
  const cookSecondValue = parseInt(cookSecond.value) || 0;

  const totalHours = prepHourValue + cookHourValue;
  const totalMinutes = prepMinuteValue + cookMinuteValue;
  const totalSeconds = prepSecondValue + cookSecondValue;

  return { totalHours, totalMinutes, totalSeconds };
};

const normalizeTime = (hours, minutes, seconds) => {
  minutes += Math.floor(seconds / 60);
  return {
    totalHours: hours + Math.floor(minutes / 60),
    totalMinutes: minutes % 60,
    totalSeconds: seconds % 60
  };
};

const validateZeroInput = (value) => {
  const resetValue = (val) => val.replace(/[^0-9.]/g, "").replace(/^0+/, "");

  switch (value) {
    case "totalTimehour":
      hour.value = resetValue(hour.value);
      break;
    case "totalTimeMinute":
      minute.value = resetValue(minute.value);
      break;
    case "totalTimeSecond":
      second.value = resetValue(second.value);
      break;
    case "prepTimeHour":
      prepHour.value = resetValue(prepHour.value);
      break;
    case "prepTimeMinute":
      prepMinute.value = resetValue(prepMinute.value);
      break;
    case "prepTimeSecond":
      prepSecond.value = resetValue(prepSecond.value);
      break;
    case "cookTimeHour":
      cookHour.value = resetValue(cookHour.value);
      break;
    case "cookTimeMinute":
      cookMinute.value = resetValue(cookMinute.value);
      break;
    case "cookTimeSecond":
      cookSecond.value = resetValue(cookSecond.value);
      break;
  }
};

const checkServingSizePerContainer = (data) => {
  if (data < 0) {
    nutritionServingSizePerContainer.value = "";
  }
};

const selectPriceCurrency = (data) => {
  if (data?.name === "Dollars") {
    selectedCurrency.value = data?.subName || "";
  }
};

const checkTimeData = (value) => {
  switch (value) {
    case "totalTimehour":
    case "totalTimeMinute":
    case "totalTimeSecond":
      checkTotalTime(value);
      break;
    case "prepTimeHour":
    case "prepTimeMinute":
    case "prepTimeSecond":
      checkPrepTime(value);
      break;
    case "cookTimeHour":
    case "cookTimeMinute":
    case "cookTimeSecond":
      checkCookTime(value);
      break;
    default:
      null;
  }
};

const checkTotalTime = (value) => {
  if (value === "totalTimehour") {
    hour.value = hour.value < 0 ? "" : hour.value;
  } else if (value === "totalTimeMinute") {
    minute.value = minute.value < 0 ? "" : minute.value;
  } else if (value === "totalTimeSecond") {
    second.value = second.value < 0 ? "" : second.value;
  }
};

const checkPrepTime = (value) => {
  if (value === "prepTimeHour") {
    prepHour.value = prepHour.value < 0 ? "" : prepHour.value;
  } else if (value === "prepTimeMinute") {
    prepMinute.value = prepMinute.value < 0 ? "" : prepMinute.value;
  } else if (value === "prepTimeSecond") {
    prepSecond.value = prepSecond.value < 0 ? "" : prepSecond.value;
  }
};

const checkCookTime = (value) => {
  if (value === "cookTimeHour") {
    cookHour.value = cookHour.value < 0 ? "" : cookHour.value;
  } else if (value === "cookTimeMinute") {
    cookMinute.value = cookMinute.value < 0 ? "" : cookMinute.value;
  } else if (value === "cookTimeSecond") {
    cookSecond.value = cookSecond.value < 0 ? "" : cookSecond.value;
  }
};

const handlePaste = (event, value, index, groupIndex) => {
  isCampaignModified.value = true;
  event.preventDefault();
  const pastedText = getPastedText(event);
  if (isValidPaste(pastedText)) {
    handleTimeValues(value, pastedText);
    handleGeneralValues(value, pastedText, index, groupIndex);
  }
};

const getPastedText = (event) => {
  const clipboardData = event.clipboardData || window.clipboardData;
  return clipboardData.getData("text");
};

const isValidPaste = (pastedText) => {
  const isNegative = pastedText.includes("-");
  return (/^-?\d+\.\d+$/.test(pastedText) || !isNegative && !isNaN(pastedText));
};

const handleTimeValues = (value, pastedText) => {
  switch (value) {
    case "totalTimehour":
      hour.value = pastedText;
      break;
    case "totalTimeMinute":
      minute.value = pastedText;
      break;
    case "totalTimeSecond":
      second.value = pastedText;
      break;
    case "prepTimeHour":
      prepHour.value = pastedText;
      break;
    case "prepTimeMinute":
      prepMinute.value = pastedText;
      break;
    case "prepTimeSecond":
      prepSecond.value = pastedText;
      break;
    case "cookTimeHour":
      cookHour.value = pastedText;
      break;
    case "cookTimeMinute":
      cookMinute.value = pastedText;
      break;
    case "cookTimeSecond":
      cookSecond.value = pastedText;
      break;
  }
};

const handleGeneralValues = (value, pastedText, index, groupIndex) => {
  switch (value) {
    case "Serving":
      servings.value = pastedText;
      break;
    case "availableServing":
      availableServings.value = pastedText;
      break;
    case "recipesPrice":
      recipePrice.value = "";
      break;
    case "nutritionServingSizePerContainer":
      nutritionServingSizePerContainer.value = pastedText;
      break;
    case "nutritionServingSize":
      nutritionServingSize.value = pastedText;
      break;
    case "nutritionAmount":
      handleNutritionAmount(pastedText, index);
      break;
    case "quantity":
      handleQuantity(pastedText, groupIndex, index);
      break;
  }
};

const handleNutritionAmount = (pastedText, index) => {
  const targetArray = selectedNutritionType.value === "perServing"
    ? nutrientTableData.value?.['perServing']
    : nutrientTableData.value?.['per100g'];
  if (Array.isArray(targetArray) && index >= 0 && index < targetArray?.length) {
    targetArray[index].valueAmount = pastedText;
  }
};

const handleQuantity = (pastedText, groupIndex, index) => {
  const languageData = ingredientsData.value[recipeVariantSelectedLanguage.value];
  if (languageData?.children?.[groupIndex]?.children?.[index]) {
    languageData.children[groupIndex].children[index].quantity = pastedText;
  }
};

const checkVideoPopupStatus = (data) => {
  recipePreviewVideo.value = !!data;
};


const focusKeywordInput = (event) => {
  const element = getRef("keywords-name");
  if (event.target.id === "keyword" && element !== document.activeElement) {
    element.focus();
  }
};

const changeReferenceProductId = (ingredient) => {
  ingredient.productId = null;
};

const checkOneGlobalIngredient = async (groupIndex, index, name) => {
  try {
    const params = {
      ingredients: [name],
    };
    await store.dispatch("ingredient/getIngredientKeywordsAsync", { params });

    const response = store.getters["ingredient/getIngredientKeywords"];

    if (response) {
      const newReceivedKeywords = response.results.reduce((acc, item) => {
        return item[1] ? [...acc, ...(item[1].keywords || [])] : acc;
      }, []);

      ingredientsData.value[recipeVariantSelectedLanguage.value].children.forEach((item, realIndex) => {
        if (realIndex === groupIndex) {
          item.children.forEach((ing, insideIndex) => {
            if (insideIndex === index) {
              ing.globalKeyword = newReceivedKeywords;
            }
          });
        }
      });
    }
  } catch (e) {
    console.error(e);
  }
};

const removeAllKeyword = () => {
  keywordData.value = [];
  hasKeywordModified.value = false;
};

const saveButtonEnable = () => {
  isCampaignModified.value = true;
};

const showNutritionDropDown = () => {
  isNutritionDropdownResult.value = !isNutritionDropdownResult.value;
};

function choosePerServingPer100g() {
  if (
    nutritionKey.value &&
    !nutritionKey.value?.['per100g'] &&
    !nutritionKey.value?.['perServing']
  ) {
    selectedNutritionType.value = addRecipeNutrientType.value;
  }

  if (selectedNutritionType.value === "perServing") {
    return nutrientTableData.value?.['perServing'];
  } else if (selectedNutritionType.value === "per100g") {
    return nutrientTableData.value?.['per100g'];
  }
}

const checkIngredientPopName = (ingredientId, gIndex, keyIndex) => {
  const name = getRef(`ingredientPopProductName${ingredientId}${gIndex}${keyIndex}`);
  if (name.scrollWidth > name.clientWidth) {
    isIngredientProductNameVisible.value = true;
  }
};

const hideIngredientNamePopTip = (ingredientId, gIndex, keyIndex) => {
  const name = getRef(`ingredientPopProductName${ingredientId}${gIndex}${keyIndex}`);
  if (name.scrollWidth > name.clientWidth) {
    isIngredientProductNameVisible.value = false;
  }
};

const checkIngredientPopNameIndex = (index) => {
  const name = getRef(`ingredientePopProductName${index}`);
  if (name.scrollWidth > name.clientWidth) {
    isProductNameTooltipVisible.value = true;
  }
};

const hideIngredientNamePopToolTip = (index) => {
  const name = getRef(`ingredientePopProductName${index}`);
  if (name.scrollWidth > name.clientWidth) {
    isProductNameTooltipVisible.value = false;
  }
};

const checkIngredientPopupName = (keyIndex) => {
  const name = getRef(`ingredientePopupProductName${keyIndex}`);
  if (name.scrollWidth > name.clientWidth) {
    isIngTooltipVisible.value = true;
  }
};

const hideIngredientNamePopupTip = (keyIndex) => {
  const name = getRef(`ingredientePopupProductName${keyIndex}`);
  if (name.scrollWidth > name.clientWidth) {
    isIngTooltipVisible.value = false;
  }
};

const selectedNutrition = (type) => {
  selectedNutritionType.value = type;
  isNutritionDropdownResult.value = false;
};

const removeKeyWord = (index) => {
  keywordData.value.splice(index, 1);
  hasKeywordModified.value = keywordData.value.length > 0;
};

const addKeywordBackword = () => {
  ingredientsData.value[recipeVariantSelectedLanguage.value].children.forEach((data, tempIndex) => {
    if (tempIndex === tempgroupIndex.value) {
      data.children.forEach((item, iIndex) => {
        if (tempindex.value === iIndex) {
          item.keywords = keywordData.value?.length > 0 ? [...keywordData.value] : [];
        }
      });
    }
  });

  isCampaignModified.value = true;
  isIngredientKeywordsPopupModal.value = false;
};
const inputDataKeyword = (value) => {
  const trimmedValue = value.trim();
  if (trimmedValue) {
    hasKeywordModified.value = true;
    const keywords = trimmedValue.split(", ").filter(item => item);
    keywordData.value.push(...keywords);
    tempKeyword.value = "";
  }
};

const nutritionDropdownMethodAsync = async () => {
  isNutritionDropDown.value = !isNutritionDropDown.value;
  await new Promise(resolve => setTimeout(resolve, 50));
  const element = getRef("nutrition_section_scroll");
  element.scrollIntoView();
};

const getNutritionsListData = async () => {
  try {
    await store.dispatch("recipeDetails/getNutritionalDataAsync");
    let response = store.getters["recipeDetails/getNutritionalData"];
    if (response?.nutrients) {
      nutritionDataList.value = response.nutrients || "";
      if (nutritionKey.value?.per100g || nutritionKey.value?.perServing) {
        nutritionDataList.value = response.nutrients;
      } else {
        addRecipeNutrientType.value = response?.type ?? "";
        nutritionDataList.value = response.nutrients;
        displayNutritionalDataList.value = [];
        compareNutritionData(displayNutritionalDataList.value);
        nutrientTableData.value[response.type] = displayNutritionalDataList.value;
      }
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} getNutritionalData`, error);
  }
};

const getIngredientNames = () => {
  const children = ingredientsData.value[recipeVariantSelectedLanguage.value]?.children || [];
  children.forEach(item => {
    item.children.forEach(ing => {
      ingredientNameList.value.push(ing.name);
    });
  });
  checkForIngredientsAsync();
};

const checkForIngredientsAsync = async () => {
  const data = ingredientNameList.value;
  try {
    const params = {
      ingredients: [data],
    };
    await store.dispatch("ingredient/getIngredientKeywordsAsync", { params });

    const response = store.getters["ingredient/getIngredientKeywords"];
    if (response) {
      globalKeywords.value = [];
      console.log(response);
      const dataEntries = Object.entries(response.results);
      const children = ingredientsData.value[recipeVariantSelectedLanguage.value]?.children || [];
      children.forEach(item => {
        item.children.forEach(ing => {
          dataEntries.forEach(comparedkeyword => {
            if (comparedkeyword[1].ingredient === ing.name) {
              ing.globalKeyword = [...comparedkeyword[1].keywords];
            }
          });
        });
      });
    }
  } catch (error) {
    console.error(error);
  }
};

const removeIngredientWeight = (groupIndex, index) => {
  const ingredient = ingredientsData.value[recipeVariantSelectedLanguage.value].children[groupIndex].children[index];
  if (ingredient) {
    ingredient.weightInGrams = 0;
    ingredient.volumeInMl = 0;
    isCampaignModified.value = true;
  }
};

const isShowAddQuantityMethod = (groupIndex, index, name, uom, quantity, note) => {
  tempingdata.value = { name, uom, quantity, notes: note };
  tempgroupIndex.value = groupIndex;
  tempindex.value = index;
  inputIngredientWeight.value = "";

  searchListData.value.forEach(item => {
    item.isChecked = item.name === "Gram";
  });

  isShowAddQuantity.value = true;
};

const ingredientKeywordsPopup = (groupIndex, index, name, uom, quantity, note, word, globalKeyword, btn) => {
  buttonString.value = btn;
  keywordData.value = [];
  recievedGlobalKeywords.value = [...globalKeyword];

  if (word) {
    word.forEach(item => {
      if (item) {
        keywordData.value.push(item);
      }
    });
  }

  tempingdata.value = { name, uom, quantity, notes: note };
  tempingdataname.value = name;
  tempingdatauom.value = uom;
  tempingdataquantity.value = quantity;
  tempingdatanotes.value = note;
  tempgroupIndex.value = groupIndex;
  tempindex.value = index;
  hasKeywordModified.value = false;
  isIngredientKeywordsPopupModal.value = true;
};

const setIngredientWeight = () => {
  tempUnit.value = "";

  searchListData.value.forEach((item) => {
    if (item.isChecked) {
      tempUnit.value = item.name;
    }
  });

  if (tempUnit.value !== "") {
    ingredientsData.value[recipeVariantSelectedLanguage.value].children.forEach((item, index) => {
      if (index === tempgroupIndex.value) {
        item.children.forEach((ing, insideIndex) => {
          if (insideIndex === tempindex.value) {
            if (tempUnit.value === "Gram") {
              ing.weightInGrams = inputIngredientWeight.value;
            } else if (tempUnit.value === "Milliliter") {
              ing.volumeInMl = inputIngredientWeight.value;
            }
          }
        });
      }
    });
  }

  isCampaignModified.value = true;
  isShowAddQuantity.value = false;
};

const debounceInput = debounce(() => {
  callSlugChangeAsync();
  isCampaignModified.value = true;
}, 2000);

const unableToLeaveEmptyAsync = async () => {
  if (!isSlugStatus.value) {
    resetSaveFlags();
    if (checkForErrors()) {
      handleSaveError();
    }
  }
};

const resetSaveFlags = () => {
  isSaveModalVisible.value = false;
  isUnableToSaveRecipeName.value = false;
  isUnableToSaveServings.value = false;
  isUnableToSaveAvailableServings.value = false;
  isUnableToSavePlateUp.value = false;
  isUnableToSaveTime.value = false;
  isUnableToSaveIngredients.value = false;
  isUnableToSaveSteps.value = false;
  isUnableToSaveTitleSteps.value = false;
  isUnableToSaveStepInstruction.value = false;
  isUnableToSaveGroupName.value = false;
};

const checkForErrors = () => {
  return [
    checkRecipeName(),
    checkServings(),
    checkAvailableServings(),
    checkPlateUp(),
    checkTime(),
    checkIngredients(),
    checkSteps(),
    checkStepInstructions()
  ].some(result => result);
};
const checkRecipeName = () => {
  let errorMsg = false;
  availableLang.value.forEach(lang => {
    if (!recipeData.value?.title?.[lang.language]) {
      errorMsg = true;
      isUnableToSaveRecipeName.value = true;
    }
  });
  return errorMsg;
};

const checkServings = () => {
  let errorMsg = false;
  if (!servings.value) {
    errorMsg = true;
    isUnableToSaveServings.value = true;
  }
  return errorMsg;
};

const checkAvailableServings = () => {
  let errorMsg = false;
  if (!availableServings.value) {
    errorMsg = true;
    isUnableToSaveAvailableServings.value = true;
  }
  return errorMsg;
};

const checkPlateUp = () => {
  let errorMsg = false;
  if (shouldShowError()) {
    errorMsg = true;
    isUnableToSavePlateUp.value = true;
  }
  return errorMsg;
};

const shouldShowError = () => {
  return !isImagePresent.value &&
    !isImageLinkPresent.value &&
    !isLinkPresent.value &&
    !isVideoPresent.value &&
    hasShowPublishButton.value;
};

const checkTime = () => {
  let errorMsg = false
  if (hasTimeError()) {
    errorMsg = true
    isUnableToSaveTime.value = true
  }
  return errorMsg
}

const hasTimeError = () => {
  return (
    (getRecipePreparationTime() !== 'PT' || getRecipeCookTime() !== 'PT') &&
    getTotalTime() === 'PT'
  )
}

const checkIngredients = () => {
  let errorMsg = false
  let emptyGroupCount = 0
  let checkGroupName = 1

  availableLang.value?.forEach((item) => {
    const children = ingredientsData.value?.[item.language]?.children
    if (children?.length) {
      children.forEach((data) => {
        const hasChildren = data?.children?.length < 1
        const isNameEmpty = !data?.name?.trim()
        const isSingleIngredient = children.length === 1

        if (checkGroupNameFn(isNameEmpty, hasChildren, isSingleIngredient)) {
          errorMsg = true
          isUnableToSaveIngredients.value = true
        }
      })
      emptyGroupCount += children?.filter((data) => !data?.name?.trim())?.length
      if (emptyGroupCount > checkGroupName) {
        errorMsg = true
        isUnableToSaveGroupName.value = true
      }
      checkGroupName++
      children?.forEach((data) => {
        data?.children?.forEach((item) => {
          if (!item?.name?.trim()) {
            errorMsg = true
            isUnableToSaveStepInstruction.value = true
          }
        })
      })
    }
  })
  return errorMsg
}

const checkGroupNameFn = (isNameEmpty, hasChildren, isSingleIngredient) => {
  return (isNameEmpty && hasChildren && isSingleIngredient) || (!isNameEmpty && hasChildren)
}

const checkSteps = () => {
  let errorMsg = false
  if (tasksData.value?.length < 1) {
    errorMsg = true
    isUnableToSaveSteps.value = true
  }
  if (tasksData.value?.length) {
    tasksData.value?.forEach((step) => {
      availableLang.value?.forEach((lang) => {
        if (step?.[lang.language]?.title === '') {
          errorMsg = true
          isUnableToSaveTitleSteps.value = true
        }
      })
    })
  }
  return errorMsg
}

const checkStepInstructions = () => {
  let errorMsg = false
  tasksData.value?.forEach((data) => {
    availableLang.value?.forEach((lang) => {
      const instructions = data?.[lang.language]?.instructions
      if (instructions?.length) {
        instructions.forEach((item) => {
          if (!item.text) {
            errorMsg = true
            isUnableToSaveStepInstruction.value = true
          }
        })
      }
    })
  })
  return errorMsg
}

const handleSaveError = () => {
  isUnableToLeaveVariant.value = true;
  isSaveModalVisible.value = false;
  isConfirmScheduleRecipePopupVisible.value = false;
};

const closeUomDropdownAsync = async (ingredient) => {
  await delayTimerRecipeUpdateAsync();
  if (!isIingredientUomAutocompleteOpen.value) return;
  ingredient.uomAutocomplete = false;
  isIingredientUomAutocompleteOpen.value = false;
  if (isEmptyOrWhitespace(searchedUomText.value)) return;
  const validUOM = ingredientsUomList.value.some(e => e.display === ingredient.UOM);
  if (!validUOM) {
    ingredient.UOM = "";
  }
};

const checkEditRecipeAuthor = () => {
  if (
    attributionAuthor?.value?.scrollWidth > attributionAuthor?.value?.clientWidth &&
    attributionAuthor.value !== document.activeElement &&
    recipeAttributionAuthor.value.trim()?.length > 0
  ) {
    isRecipeAuthorFocus.value = true;
  }
};

const hideRecipeAuthorTip = () => {
  isRecipeAuthorFocus.value = false;
};

const selectedRecipeStatuses = (info) => {
  searchListData.value.forEach(data => {
    data.isChecked = info.name === data.name;
    if (data.isChecked) {
      statusesCSV.value = data.name;
    }
  });
};

const checkEditRecipeExternalId = () => {
  const name = getRef("attributionExternal");
  if (
    name.scrollWidth > name.clientWidth &&
    name !== document.activeElement &&
    recipeAttributionExternalId.value.trim()?.length > 0
  ) {
    isRecipeExternalIdFocus.value = true;
  }
};

const hideRecipeExternalIdTip = () => {
  isRecipeExternalIdFocus.value = false;
};

const setIngredientGroupNameAsync = async (groupIndex) => {
  if (availableLang.value?.length === 0) return;

  const defaultLangData = ingredientsData.value[defaultLang.value];
  if (!defaultLangData || !defaultLangData.children || !defaultLangData.children[groupIndex]) return;

  const groupData = defaultLangData.children[groupIndex];
  if (!groupData.setGroupName || groupData.name.trim() === "") return;

  updateDefaultLang(groupIndex);
  await delayTimerRecipeUpdateAsync();
  updateItems();
};

const updateDefaultLang = (groupIndex) => {
  ingredientsData.value[defaultLang.value].children[groupIndex].setGroupName = false;
  const dataName = ingredientsData.value[defaultLang.value].children[groupIndex].name;

  availableLang.value.forEach((lang) => {
    if (groupIndex !== 0 && lang.language !== defaultLang.value) {
      const langData = ingredientsData.value[lang.language].children[groupIndex];
      if (langData) {
        langData.name = dataName;
        langData.setGroupName = false;
      }
    }
  });
};

const updateItems = () => {
  items.value = [];
  const selectedLangData = ingredientsData.value[recipeVariantSelectedLanguage.value];
  if (selectedLangData && selectedLangData.children) {
    items.value = JSON.parse(JSON.stringify(selectedLangData.children));
  }
};

const rearrangeIngredient = () => {
  if (availableLang.value && availableLang.value?.length > 1) {
    let copyIngredientsData = Object.assign({}, ingredientsData.value);

    if (
      copyIngredientsData &&
      copyIngredientsData[defaultLang.value] &&
      copyIngredientsData[defaultLang.value].children
    ) {
      copyIngredientsData[defaultLang.value].children.forEach((iGroup, iGroupIndex) => {
        let group_Index = 0;
        let ing_Index = 0;
        let ind_data = {};

        iGroup.children.forEach((ing, ingIndex) => {
          availableLang.value.forEach((lang) => {
            if (
              copyIngredientsData[lang.language] &&
              copyIngredientsData[lang.language].children
            ) {
              copyIngredientsData[lang.language].children.forEach((c_iGroup) => {
                c_iGroup.children.forEach((c_ing, c_ingIndex) => {
                  if (ing.id === c_ing.id) {
                    ind_data = c_ing;
                    group_Index = iGroupIndex;
                    ing_Index = ingIndex;
                    c_iGroup.children.splice(c_ingIndex, 1);
                  }
                  if (c_ing && !c_ing.id) {
                    c_iGroup.children.splice(c_ingIndex, 1);
                  }
                });
              });

              const dataCheck = {
                UOM: ind_data.UOM || "",
                UOMMirror: ind_data.UOMMirror || "",
                excludeFromNutrition: ind_data.excludeFromNutrition || false,
                foodItem: ind_data.foodItem || "",
                group: ind_data.group || "",
                campaignData: ind_data.campaignData || null,
                originalCampaignData: ind_data.originalCampaignData || null,
                hasOverridingCampaign: ind_data.hasOverridingCampaign || false,
                weightInGrams: ind_data.weightInGrams?.toString() || "0",
                volumeInMl: ind_data.volumeInMl?.toString() || "0",
                id: ind_data.id || "",
                isChecked: ind_data.isChecked || false,
                isDropDown: ind_data.isDropDown || false,
                keywordInput: ind_data.keywordInput || "",
                globalKeyword: ind_data.globalKeyword || [],
                keywords: ind_data.keywords || [],
                externalId: ind_data.externalId || "",
                productId: ind_data.productId || "",
                level: ind_data.level || "main",
                modifier: ind_data.modifier || "",
                name: ind_data.name || "",
                nameMirror: ind_data.nameMirror || "",
                note: ind_data.note || "",
                remark: ind_data.remark || "",
                quantity: ind_data.quantity || 0,
                quantityMirror: ind_data.quantityMirror || 0,
                rawText: ind_data.rawText || "",
                uomAutocomplete: ind_data.uomAutocomplete || false,
              };

              copyIngredientsData[lang.language].children[group_Index].children.splice(ing_Index, 0, dataCheck);
            }
          });
        });
      });
    }

    ingredientsData.value = copyIngredientsData;
  }
};

const getDifference = (availableLangs, availableLangAPI) => {
  return availableLangs.filter(lang =>
    !availableLangAPI.some(item => lang === item.language)
  );
};

const checkSlugLength = () => {
  const slugText = getRef("slugField");
  isSlugToolTip.value = slugText.scrollWidth > slugText.clientWidth;
};

const hideSlugToolTip = () => {
  isSlugToolTip.value = false;
};

const categoryInfoIconVisibility = () => {
  hasEnableCategoryInfoIcon.value = selectedCategories.value.every(item =>
    Object.keys(item.data).includes(recipeVariantSelectedLanguage.value)
  );
};

const tagInfoIconVisibility = () => {
  hasEnableTagInfoIcon.value = selectedTags.value.every(tag =>
    Object.keys(tag.data).includes(recipeVariantSelectedLanguage.value)
  );
};

const emptySlugGroup = () => ({
  [lang.value]: {
    slugName: "",
    slugStatus: false,
    isin: "",
  },
});

const emptyIngredientGroup = () => ({
  children: [
    {
      children: [],
      displayGroup: false,
      setGroupName: false,
      name: "",
    },
  ],
  name: "ChildrenArray",
});

const showKeywordTip = (index, position) => {
  const keywordCheck = getRef("keyword" + index + position);
  if (keywordCheck.scrollWidth > keywordCheck.clientWidth) {
    isKeywordTooltipVisible.value = true;
  }
};

const hideKeywordTip = (index, position) => {
  const keywordCheck = getRef("keyword" + index + position);
  if (keywordCheck.scrollWidth > keywordCheck.clientWidth) {
    isKeywordTooltipVisible.value = false;
  }
};

const editCategoryVariantName = (category) => {
  categoryVariantName.value = "";
  editCategoriesVariantName.value = category.data[lang.value].name;
  editCategoriesVariantImage.value = category.data[lang.value].image;
  editCategoriesVariantIsin.value = category.isin;
  isEditCategoryVariantName.value = true;
};

const editTagVariantName = (tag) => {
  tagVariantName.value = "";
  editTagsVariantName.value =
    tag && tag.data && tag.data[lang.value] ? tag.data[lang.value].name : "";
  editTagsVariantIsin.value = tag.isin;
  isEditTagVariantName.value = true;
};

const delayTimerRecipeUpdateAsync = async () => {
  await new Promise(resolve => setTimeout(resolve, 500));
};

const saveVariantName = async (type) => {
  if (type === "category") {
    isEditCategoryVariantName.value = false;
  } else if (type === "tag") {
    isEditTagVariantName.value = false;
  }
  await confirmEditVariantNameAsync(type);
};

const confirmEditVariantNameAsync = async (type) => {
  const isCategory = type === "category";
  const payload = {
    type,
    isin: isCategory ? editCategoriesVariantIsin.value : editTagsVariantIsin.value,
    data: {
      [recipeVariantSelectedLanguage.value]: {
        name: isCategory ? categoryVariantName.value.trim() : tagVariantName.value.trim(),
      },
    },
    ...(isCategory && {
      image: { [recipeVariantSelectedLanguage.value]: editCategoriesVariantImage.value },
    }),
  };

  try {
    const response = await store.dispatch('categories/postCategoryOrCategoryGroupAsync', {
      payload,
      lang: lang.value,
    });

    if (response?.entity) {
      const selectedList = isCategory ? selectedCategories.value : selectedTags.value;
      const indexToInsert = selectedList.findIndex(data => response.entity.isin === data.isin);

      if (indexToInsert !== -1) {
        selectedList.splice(indexToInsert, 1);
      }
      selectedList.splice(indexToInsert, 0, response.entity);
    }
  } catch (error) {
    console.error(error);
  }
};

const handleDrag = (event) => {
  if (event?.moved || event?.added) {
    isCampaignModified.value = true;
  }
};

const checkMoveIngredientAsync = async (data) => {
  if (availableLang.value?.length > 1 && !isFirstDrag.value) {
    isAddSortConfirm.value = true;
    isFirstDrag.value = true;
  }
};

const closeAddSortConfirmModal = () => {
  isCampaignModified.value = true;
  isAddSortConfirm.value = false;
};

const ingNameCheck = (value, groupIndex, index) => {
  const nameIng = getRef("foodItemName" + groupIndex + index);
  if (nameIng.scrollWidth > nameIng.clientWidth) {
    isIngredientNameVisible.value = true;
  }
};

const ingNameCheckHide = () => {
  isIngredientNameVisible.value = false;
};

const ingNoteCheck = (groupIndex, index, text) => {
  const noteIng = getRef("ingredientNote" + groupIndex + index);
  if (
    noteIng.scrollWidth > noteIng.clientWidth &&
    noteIng !== document.activeElement &&
    text.trim()?.length > 0
  ) {
    isNoteTooltipVisible.value = true;
  }
};

const ingredientNoteCheckHide = () => {
  isNoteTooltipVisible.value = false;
};

const hideFilterOption = () => {
  isShowFilterOption.value = !isShowFilterOption.value;
};

const closeSchedulePublish = () => {
  if (
    recipeSchedulePublishDate.value === "" &&
    recipeScheduleEndDate.value === ""
  ) {
    recipeButtonState.value = "Save";
    recipeButtonPublishState.value = "Save";
    isAddScheduleButtonVisible.value = true;
    isSchedulePublishVisible.value = false;
  }
  if (
    !isEmptyOrWhitespace(recipeSchedulePublishDate.value) &&
    !isEmptyOrWhitespace(recipeScheduleEndDate.value)
  ) {
    if (hasShowPublishButton.value || isShowSaveButton.value) {
      productionDescriptionRedText.value =
        "The scheduled dates will be removed. The recipe will remain published.";
    }
    if (!hasShowPublishButton.value || !isShowSaveButton.value) {
      productionDescriptionRedText.value =
        "The scheduled dates will be removed.";
    }
    isDeleteScheduleModalVisible.value = true;
    isAddScheduleButtonVisible.value = false;
    isSchedulePublishVisible.value = true;
  }
  checkScheduleDates();
  isSchedulingCalendarVisible.value = false;
};

const deleteSelectedScheduleRecipeAsync = async () => {
  isCampaignModified.value = true;
  isDeletingModalVisible.value = true;
  isScheduledRecipeDeleted.value = true;
  isDeleteScheduleModalVisible.value = false;
  publishDate.value = 0;
  endDate.value = 0;
  recipeButtonState.value = "Save";
  recipeButtonPublishState.value = "Publish";
  recipeSchedulePublishDate.value = "";
  recipeScheduleEndDate.value = "";
  isAddScheduleButtonVisible.value = true;
  isSchedulePublishVisible.value = false;

  if (isAddScheduleButtonVisible.value && !isSchedulePublishVisible.value) {
    await delay(1500);
    isDeletingModalVisible.value = false;
  }
};

const deleteScheduleRecipeData = async () => {
  try {
    await store.dispatch("recipe/deleteScheduleRecipeAsync", { isin:recipeIsin.value });
    $eventBus.emit('campaignModified', isCampaignModified.value);
    isCampaignModified.value = true;
    isRecipeScheduled.value = false;
    range.value = { start: null, end: null };
    publishDate.value = 0;
    endDate.value = 0;
    rangePopup.value = null;
    recipeSchedulePublishDate.value = '';
    recipeScheduleEndDate.value = '';
  } catch (error) {
    console.error("Error deleting scheduled recipe:", error);
  }
};

const closeScheduleModal = () => {
  isDeleteScheduleModalVisible.value = false
};

const openSchedulePublishNew = () => {
  if (isAllRequiredFieldFilled()) {
    isAddScheduleButtonVisible.value = false
    isSchedulePublishVisible.value = true
    hasShowPublishButton.value = false
    isShowSaveButton.value = false
  }
};

const openSchedulePublish = () => {
  isAddScheduleButtonVisible.value = false
  isSchedulePublishVisible.value = true
  hasShowPublishButton.value = false
  isShowSaveButton.value = false
  range.value = { start: null, end: null };
};

const closeSchedulePopupForVariant = (data) => {
  if (recipeSchedulePublishDate.value === '' && recipeScheduleEndDate.value === '') {
    isAddScheduleButtonVisible.value = true
    isSchedulePublishVisible.value = false
  }

  if (data !== 'en-US') {
    recipeButtonState.value = 'Save'
    recipeButtonPublishState.value = 'Publish'
  }

  if (data === 'en-US' && recipeSchedulePublishDate.value !== '' && recipeScheduleEndDate.value !== '' &&
    (!isSchedulePublishDateDisable() || !isSameScheduleDate())) {
    recipeButtonState.value = 'Schedule'
    recipeButtonPublishState.value = 'Schedule'
  }

  if (data === 'en-US' && recipeSchedulePublishDate.value !== '' && recipeScheduleEndDate.value !== '' &&
    (isSchedulePublishDateDisable() || isSameScheduleDate())) {
    recipeButtonState.value = 'Publish'
    recipeButtonPublishState.value = 'Publish'
  }
};

const checkScheduleDates = () => {
  if (!recipeSchedulePublishDate.value || !recipeScheduleEndDate.value) {
    recipeSchedulePublishDate.value = ''
    publishDate.value = 0
    range.value = null
  }

  if (recipeSchedulePublishDate.value && recipeSchedulePublishDate.value === recipeScheduleEndDate.value) {
    recipeSchedulePublishDate.value = ''
    recipeScheduleEndDate.value = ''
    publishDate.value = 0
    endDate.value = 0
    range.value = null
  }

  if (recipeSchedulePublishDate.value && recipeScheduleEndDate.value) {
    if (new Date(recipeSchedulePublishDate.value) > new Date(recipeScheduleEndDate.value)) {
      let temp = recipeSchedulePublishDate.value
      recipeSchedulePublishDate.value = recipeScheduleEndDate.value
      recipeScheduleEndDate.value = temp
      let publishDateObj = new Date(recipeSchedulePublishDate.value)
      publishDateObj.setHours(0, 0, 0, 0)
      let endDateObj = new Date(recipeScheduleEndDate.value)
      endDateObj.setHours(0, 0, 0, 0)
      publishDate.value = convertToTimestamp(publishDateObj)
      endDate.value = convertToTimestamp(endDateObj)
      range.value = { start: publishDateObj, end: endDateObj }
    }
  }
};

const isSchedulePublishDateDisable = () => {
  if (!recipeSchedulePublishDate.value) return false
  const givenDate = new Date(recipeSchedulePublishDate.value)
  givenDate.setHours(0, 0, 0, 0)
  const currentDate = new Date()
  currentDate.setHours(0, 0, 0, 0)
  return givenDate < currentDate
};

const isSameScheduleDate = () => {
  if (!recipeSchedulePublishDate.value) return false
  const currentDate = new Date()
  currentDate.setHours(0, 0, 0, 0)
  return convertToTimestamp(recipeSchedulePublishDate.value) <= convertToTimestamp(currentDate)
    || convertToTimestamp(recipeSchedulePublishDate.value) === convertToTimestamp(recipeScheduleEndDate.value)
};
const handleDateClick = (newValue) => {
  if (!Array.isArray(newValue) || newValue.length !== 2 || !newValue[0] || !newValue[1]) return;

  const [start, end] = newValue;

  recipeSchedulePublishDate.value = formatDateToReadableString(start);
  recipeScheduleEndDate.value = formatDateToReadableString(end);

  publishDate.value = convertToTimestamp(start).toString();
  endDate.value = convertToTimestamp(end).toString();

  isCampaignModified.value = !isSameScheduleDate();
  const currentDate = new Date()
  currentDate.setHours(0, 0, 0, 0)
  if (convertToTimestamp(recipeSchedulePublishDate.value) <= convertToTimestamp(currentDate)) {
    isCampaignModified.value = true;
  }

  updateScheduleVisibility();
};

const showPublisherData = () => {
  publisherDropdownResult.value = !publisherDropdownResult.value;
  let scroll = getRef("selectOneText");
  scroll.scrollTo(0, 0);
};

const selectedPublisherProduct = (item) => {
  if (selectedPublisherName.value === item.name) return;

  isCampaignModified.value = true;
  selectedPublisherName.value = item?.name ?? '';
  selectedPublisherImage.value =
    item?.image?.url ?? defaultOrganizationsImage.value;
  recipeAttributionOrganization.value = item.isin;
  publisherDropdownResult.value = false;
};

const resetPublisher = () => {
  if (selectedPublisherName.value === 'Select One') return;

  isCampaignModified.value = true;
  selectedPublisherName.value = 'Select One';
  selectedPublisherImage.value = '';
  recipeAttributionOrganization.value = '';
  publisherDropdownResult.value = false;
};

const recipeDropDown = () => {
  tasksData.value.forEach((item) => {
    item.isChecked = !isRecipeStepDropDown.value;
  });
  isRecipeStepDropDown.value = !isRecipeStepDropDown.value;
  tasksData.value = [...tasksData.value];
};

const showAvailableLanguage = () => {
  toggleDropdownOff();
  availableLanguageDropdown.value = !availableLanguageDropdown.value;
  if (availableLanguageDropdown.value) {
    rearrangeIngredient();
  }
};

const recipeTitleLanguageSet = (language) => {
  if (
    recipeData.value &&
    recipeData.value.title &&
    !recipeData.value.title[language]
  ) {
    recipeData.value.title[language] = recipeData.value.title[defaultLang.value] ?? '';
  } else if (recipeData.value.title?.[language]) {
    recipeData.value.title[language] = recipeData.value.title[language] ?? '';
  }
  return recipeData.value.title[language];
};


const setRecipeTitleInput = () => {
  recipeData.value.title[recipeVariantSelectedLanguage.value] = recipeName.value
    ? recipeName.value.trim()
    : '';
};

const recipeSlugLanguageSet = (language) => {
  if (slugData.value && !slugData.value[language]) {
    const dataOfSlug = {
      [language]: {
        slugName: slugData.value[defaultLang.value]?.slugName ?? '',
        slugStatus: slugData.value[defaultLang.value]?.slugStatus ?? false,
        isin: recipeID ? recipeID : '',
      },
    };
    slugData.value = {
      ...slugData.value,
      ...dataOfSlug,
    };
  } else if (slugData.value[language]) {
    slugData.value[language] = slugData.value[language] ?? {};
  }
  return slugData.value[language].slugName;
};

const setRecipeSlugInput = () => {
  const selectedLanguage = recipeVariantSelectedLanguage.value;
    if (!slugData[selectedLanguage]) {
    slugData[selectedLanguage] = {};
  }
  slugData[selectedLanguage].slugName = slugEdit?.value?.trim() ?? '';
  if (!slugEdit?.value?.trim()) {
    slugData[selectedLanguage].slugStatus = false;
  }
};

const recipeSubtitleLanguageSet = (language) => {
  if (!recipeData.value?.subtitle?.[language]) {
    recipeData.value.subtitle[language] = recipeData.value.subtitle?.[defaultLang.value] ?? '';
  } else if (recipeData.value.subtitle?.[language]) {
    recipeData.value.subtitle[language] = recipeData.value.subtitle[language] ?? '';
  }
  return recipeData.value.subtitle[language];
};

const setRecipeSubtitleInput = () => {
  recipeData.value.subtitle[recipeVariantSelectedLanguage.value] = recipeSubtitle.value
    ? recipeSubtitle.value.trim()
    : '';
};

const recipeAbstractLanguageSet = (language) => {
  if (
    recipeData.value &&
    recipeData.value.description &&
    !recipeData.value.description[language]
  ) {
    recipeData.value.description = {
      ...recipeData.value.description,
      [language]: { abstract: '' },
    };
    recipeData.value.description[language].abstract = recipeData.value.description[defaultLang.value]?.abstract ?? '';
  } else if (
    recipeData.value.description?.[language] &&
    !recipeData.value.description[language].abstract
  ) {
    recipeData.value.description[language].abstract = recipeData.value.description[defaultLang.value]?.abstract ?? '';
  }
  return recipeData.value.description[language]?.abstract;
};

const setRecipeAbstractInput = () => {
  if (!recipeData.value.description[recipeVariantSelectedLanguage.value]) {
    recipeData.value.description[recipeVariantSelectedLanguage.value] = { abstract: '' };
  }
  recipeData.value.description[recipeVariantSelectedLanguage.value].abstract = description.value?.trim() ?? '';
};

const recipeNotesLanguageSet = (language) => {
  if (!recipeData.value.description[language]) {
    recipeData.value.description = {
      ...recipeData.value.description,
      [language]: { notes: "" },
    };
    recipeData.value.description[language].notes =
      recipeData.value.description[defaultLang.value]?.notes ?? "";
  } else if (!recipeData.value.description[language].notes) {
    recipeData.value.description[language].notes =
      recipeData.value.description[defaultLang.value]?.notes ?? "";
  } else {
    recipeData.value.description[language].notes =
      recipeData.value.description[language].notes ?? "";
  }
  return recipeData.value.description[language].notes;
};

const setRecipeNotesInput = () => {
  if (!recipeData.value.description[recipeVariantSelectedLanguage.value]) {
    recipeData.value.description = {
      ...recipeData.value.description,
      [recipeVariantSelectedLanguage.value]: { notes: "" },
    };
  }
  recipeData.value.description[recipeVariantSelectedLanguage.value].notes =
    notes.value?.trim() || "";
};

const recipeIngredientLanguageSetAsync = async (language) => {
  if (!ingredientsData.value[language]) {
    const ingredientsCopyData = createIngredientsCopyData();
    ingredientsData.value = {
      ...ingredientsData.value,
      [recipeVariantSelectedLanguage.value]: {
        children: ingredientsCopyData,
      },
    };
    updateIngredient();
  } else {
    ingredientsData.value[language] = ingredientsData.value[language] || {};
  }
};

const createIngredientsCopyData = () => {
  const ingredientsCopyData = [];
  ingredientsData.value[defaultLang.value]?.children.forEach((group) => {
    const ingredients = group.children.map((ingredient) =>
      createCleanIngredient(ingredient)
    );

    ingredientsCopyData.push({
      children: ingredients,
      name: group.name,
      setGroupName: group.setGroupName,
      displayGroup: group.displayGroup,
    });
  });
  return ingredientsCopyData;
};

const createCleanIngredient = (ingredient) => {
  const {
    keywords = [],
    name = "",
    quantity = 0,
    UOM = "",
    rawText = "",
    foodItem = "",
    group = "",
    excludeFromNutrition = false,
    weightInGrams = 0,
    volumeInMl = 0,
    level = "main",
    modifier = "",
    note = "",
    remark = "",
    externalId = "",
    productId = "",
    globalKeyword = [],
    campaignData = null,
    originalCampaignData = null,
    hasOverridingCampaign = false,
  } = ingredient || {};

  return {
    name,
    quantity,
    UOM,
    rawText,
    foodItem,
    group,
    excludeFromNutrition,
    weightInGrams: weightInGrams.toString(),
    volumeInMl: volumeInMl.toString(),
    level,
    modifier,
    note,
    remark,
    keywords,
    externalId,
    productId,
    globalKeyword,
    keywordInput: "",
    campaignData,
    originalCampaignData,
    hasOverridingCampaign,
    uomAutocomplete: false,
    isDropDown: false,
  };
};

const recipeStepsLanguageSet = (language) => {
  tasksData.value = tasksData.value.map(data => {

    const ingredientLang = extractIngredients(data);
    const instructionLang = extractInstructions(data);

    const document = {
      [language]: {
        title: data?.[defaultLang.value]?.title ?? "",
        instructions: instructionLang,
        ingredients: ingredientLang,
        media: {
          image: data?.[defaultLang.value]?.media?.image ?? "",
          video: [
            {
              url: data?.[defaultLang.value]?.media?.video?.[0]?.url ?? "",
            },
          ],
        },
      },
    };

    return { ...data, ...document };
  });
};

const extractIngredients = (data) => {
  return data?.[defaultLang.value]?.ingredients?.map(item => {
    return mapIngredient(item);
  }) ?? [];
};

const mapIngredient = (item) => {
  const defaultValues = {
    UOM: "",
    UOMMirror: "",
    excludeFromNutrition: false,
    productId: "",
    foodItem: "",
    group: "",
    keywordInput: "",
    globalKeyword: [],
    keywords: [],
    level: "main",
    modifier: "",
    name: "",
    nameMirror: "",
    note: "",
    remark: "",
    weightInGrams: "0",
    volumeInMl: "0",
    quantity: 0,
    quantityMirror: 0,
    rawText: "",
    uomAutocomplete: false,
  };

  const convertToString = (value) => (value ?? "").toString();
  const getValue = (key) => item?.[key] ?? defaultValues[key];
  const getArrayValue = (key) => Array.isArray(item?.[key]) ? item[key] : defaultValues[key];

  return {
    UOM: getValue('UOM'),
    UOMMirror: getValue('UOMMirror'),
    excludeFromNutrition: getValue('excludeFromNutrition'),
    productId: getValue('productId'),
    foodItem: getValue('foodItem'),
    group: getValue('group'),
    keywordInput: getValue('keywordInput'),
    globalKeyword: getArrayValue('globalKeyword'),
    keywords: getArrayValue('keywords'),
    level: getValue('level'),
    modifier: getValue('modifier'),
    name: getValue('name'),
    nameMirror: getValue('nameMirror'),
    note: getValue('note'),
    remark: getValue('remark'),
    weightInGrams: convertToString(item?.weightInGrams),
    volumeInMl: convertToString(item?.volumeInMl),
    quantity: getValue('quantity'),
    quantityMirror: getValue('quantityMirror'),
    rawText: getValue('rawText'),
    uomAutocomplete: getValue('uomAutocomplete'),
  };
};

const extractInstructions = (data) => {
  return data?.[defaultLang.value]?.instructions?.map(item => ({
    text: item?.text ?? "",
    times: item?.times ?? [],
  })) ?? [];
};

const chooseLanguageAsync = async (language) => {
  closeSchedulePopupForVariant(language);
  await unableToLeaveEmptyAsync();
  if (!isUnableToLeaveVariant.value) {
    isUpdatingIngredient.value = false;
    availableLanguageDropdown.value = false;
    recipeVariantSelectedLanguage.value = language;
    recipeName.value = await recipeChooseTitleLanguageSet(language);
    recipeSubtitle.value = await recipeChooseSubtitleLanguageSet(language);
    description.value = await recipeChooseAbstractLanguageSet(language);
    notes.value = await recipeChooseNotesLanguageSet(language);
    slugEdit.value = await recipeChooseSlugLanguageSet(language);
    checkRecipeVariantImageSelection();
    await recipeIngredientLanguageSetAsync(language);
    recipeStepsLanguageSet(language);
    if (!isSlugStatus.value) {
      hasSlugCheckConfirm.value = true;
      await checkSlug();
      hasSlugCheckConfirm.value = false;
    }
    await delayTimerRecipeUpdateAsync();
    rearrangeIngredient();
    await delay(1000);
    isUpdatingIngredient.value = true;
    getIngredientNames();
  }
};

const checkRecipeVariantImageSelection = () => {
  config.isRecipeVariant = recipeVariantSelectedLanguage.value !== defaultLang.value;
};

const recipeChooseTitleLanguageSet = (language) => {
  if (recipeData.value.title && !recipeData.value.title[language]) {
    recipeData.value.title[language] = "";
  } else if (recipeData.value.title && recipeData.value.title[language]) {
    recipeData.value.title[language] = recipeData.value.title[language] ?? "";
  }
  return recipeData.value.title[language];
};

const recipeChooseSubtitleLanguageSet = (language) => {
  if (recipeData.value.subtitle && !recipeData.value.subtitle[language]) {
    recipeData.value.subtitle[language] = "";
  } else if (recipeData.value.subtitle && recipeData.value.subtitle[language]) {
    recipeData.value.subtitle[language] = recipeData.value.subtitle[language] ?? "";
  }
  return recipeData.value.subtitle[language];
};

const recipeChooseAbstractLanguageSet = (language) => {
  if (recipeData.value.description && !recipeData.value.description[language]) {
    recipeData.value.description = {
      ...recipeData.value.description,
      [language]: { abstract: "" },
    };
    recipeData.value.description[language].abstract = "";
  } else if (recipeData.value.description && recipeData.value.description[language] && !recipeData.value.description[language].abstract) {
    recipeData.value.description[language] = {
      ...recipeData.value.description[language],
      abstract: "",
    };
    recipeData.value.description[language].abstract = "";
  } else if (recipeData.value.description && recipeData.value.description[language] && recipeData.value.description[language].abstract) {
    recipeData.value.description[language].abstract = recipeData.value.description[language].abstract ?? "";
  }
  return recipeData.value.description[language].abstract;
};

const recipeChooseNotesLanguageSet = (language) => {
  if (recipeData.value.description && !recipeData.value.description[language]) {
    recipeData.value.description = {
      ...recipeData.value.description,
      [language]: { notes: "" },
    };
    recipeData.value.description[language].notes = "";
  } else if (recipeData.value.description && recipeData.value.description[language] && !recipeData.value.description[language].notes) {
    recipeData.value.description[language] = {
      ...recipeData.value.description[language],
      notes: "",
    };
    recipeData.value.description[language].notes = "";
  } else if (recipeData.value.description && recipeData.value.description[language] && recipeData.value.description[language].notes) {
    recipeData.value.description[language].notes = recipeData.value.description[language].notes ?? "";
  }
  return recipeData.value.description[language].notes;
};

const recipeChooseSlugLanguageSet = (language) => {
  if (slugData.value && !slugData.value[language]) {
    let dataOfSlug = {
      [language]: {
        slugName: "",
        slugStatus: false,
        isin: recipeID ? recipeID : "",
      },
    };
    slugData.value = {
      ...slugData.value,
      ...dataOfSlug,
    };
  } else if (slugData.value && slugData.value[language]) {
    slugData.value[language] = slugData.value[language] ?? {};
  }
  return slugData.value[language].slugName;
};

const addLanguageAsync = async (language) => {
  isUpdatingIngredient.value = false;
  recipeVariantSelectedLanguage.value = language;
  recipeName.value = await recipeTitleLanguageSet(language);
  recipeSubtitle.value = await recipeSubtitleLanguageSet(language);
  description.value = await recipeAbstractLanguageSet(language);
  notes.value = await recipeNotesLanguageSet(language);
  slugEdit.value = await recipeSlugLanguageSet(language);
  await recipeIngredientLanguageSetAsync(language);
  recipeStepsLanguageSet(language);
  if (!isSlugStatus.value) {
    hasSlugCheckConfirm.value = true;
    await checkSlug();
    hasSlugCheckConfirm.value = false;
  }
  await delay(500);
  rearrangeIngredient();
  await delay(1000);
  isUpdatingIngredient.value = true;
  getIngredientNames();
};

const updateIngredient = () => {
  const data = ingredientsData.value;
  ingredientsData.value = {};
  ingredientsData.value = data;
};

const checkSlug = async () => {
  let slugErrorArray = [];
  let promises = [];
  showLoader.value = true;

  availableLang.value.forEach(async (lang) => {
    const slug = slugData.value?.[lang.language]?.slugName || "";
    if (slug) {
      promises.push(
        await store.dispatch('recipeDetails/checkSlugExistAsync', { slug })
          .then(async (response) => {
            const copy = response.isin;
            slugData.value[lang.language].isin = recipeID.value;

            if (slugData.value[lang.language].isin === copy) {
              slugData.value[lang.language].slugStatus = false;
            } else {
              slugData.value[lang.language].slugStatus = true;
            }

            let data = 0;
            availableLang.value.forEach((checkExist) => {
              if (slugData.value[checkExist.language]?.slugStatus) {
                data++;
              }
            });

            if (data > 0) {
              isSlugStatus.value = true;
              hasSlugExist.value = true;
            } else {
              isSlugStatus.value = false;
              hasSlugExist.value = false;
            }
          })
          .catch(() => {
            slugData.value[lang.language].slugStatus = false;
            slugErrorArray.push(lang.language);
            slugErrorArray = removeDuplicatesSlugError(slugErrorArray);

            if (slugErrorArray.length === availableLang.value.length) {
              hasSlugExist.value = false;
              isSlugStatus.value = false;
            }
          })
      );
    } else if (
      recipeData?.value?.title?.[recipeVariantSelectedLanguage.value] &&
      slugEdit.value === "" &&
      !route.query[QUERY_PARAM_KEY.ISIN]
    ) {
      getRecipeSlugAsync();
    }
  });

  return Promise.all(promises);
};

const getRecipeSlugAsync = async () => {
  if (!recipeIsin.value) {
    await getIsins();
  }

  const promises = [];

  availableLang.value.forEach((lang) => {
    if (
      slugData[lang.language]?.slugStatus ||
      (slugEdit?.value === "" && !route.query.isin)
    ) {
      if (!slugData[lang.language]) {
        slugData[lang.language] = {};
      }

      promises.push(
        RecipeService.getRecipeSlug(
          project.value,
          recipeIsin.value,
          lang.value,
          recipeData.value?.title?.[recipeVariantSelectedLanguage.value]?.trim() || '',
          recipeData.value?.subtitle?.[recipeVariantSelectedLanguage.value]?.trim() || '',
          store,
          $auth
        )
          .then((response) => {
            slugData[lang.language].slugName = response?.slug ?? "";
            slugEdit.value = slugData[lang.language].slugName || "";
          })
          .catch((e) => {
            console.error(e);
          })
      );
    }
  });

  await Promise.all(promises);
};

const removeDuplicatesSlugError = (arr) => {
  return [...new Set(arr)];
};

const selectUploadMediaOption = (type, data) => {
  if (type === $keys.KEY_NAMES.IMAGE) {
    if (data.id === 1) {
      uploadMediaName.value = $keys.KEY_NAMES.IMAGE;
      uploadMediaPopup.value = true;
    } else {
      generateAIRecipeImageAsync();
    }
  }
  if (type === $keys.KEY_NAMES.VIDEO) {
    uploadMediaName.value = $keys.KEY_NAMES.VIDEO;
    uploadMediaPopup.value = true;
  }
};

const generateAIRecipeImageAsync = async () => {
  await saveRecipeSimpleAsync();
  isRecipeImageGeneratorVisible.value = true;
};

const closeUploadMediaPopup = () => {
  uploadMediaPopup.value = false;
};
const restoreButtonStateAfterImageGeneration = () => {
  isCampaignModified.value = buttonStateBeforeImageGeneration.value.isCampaignModified;
  hasShowPublishButton.value = buttonStateBeforeImageGeneration.value.hasShowPublishButton;
  isShowSaveButton.value = buttonStateBeforeImageGeneration.value.isShowSaveButton;
  buttonStateBeforeImageGeneration.value = null;
};

const closeRecipeImageGeneratorPopup = () => {
  isRecipeImageGeneratorVisible.value = false;
  isRecipeImageGenerating.value = false;
  if (buttonStateBeforeImageGeneration.value) {
    restoreButtonStateAfterImageGeneration();
  }
};

const addButtonURLlink = (link) => {
  if (!route.query.isin && !recipeIsin.value) {
    getIsins();
  }
  const url = new URL(link.trim());
  urlLink.value = url?.href || "";
  hostNameUrlLink.value = url?.hostname || "";
  isCampaignModified.value = true;
  urlLinkUpdate.value = urlLink.value;
  hostNameUrlLinkUpdate.value = hostNameUrlLink.value;
  hostNameUrlLinkUpdate.value = hostNameUrlLinkUpdate.value.toUpperCase();
  if (urlLinkUpdate.value) {
    let searchParamsURL = new URL(urlLinkUpdate.value);
    if (
      urlLinkUpdate.value.includes("youtube") &&
      searchParamsURL.searchParams.get("v")
    ) {
      linkURLImage.value = `http://img.youtube.com/vi/${searchParamsURL.searchParams.get("v")}/hqdefault.jpg`;
    } else {
      linkURLImage.value = emptyVideo;
    }
  }
  isVideoPresent.value = false;
  productVideo.value = "";
  isLinkPresent.value = true;
  uploadMediaPopup.value = false;
  resetMediaSelectionError();
  checkMediaPresent();
  closeModal();
};

const addButtonImageURLlink = (link) => {
  if (!route.query.isin && !recipeIsin.value) {
    getIsins();
  }
  const url = new URL(link.trim());
  imageUrlLink.value = url?.href || '';
  hostNameImageUrlLink.value = url?.hostname || '';
  isCampaignModified.value = true;

  const image = new Image();
  image.src = imageUrlLink.value;
  image.onerror = () => {
    imageUrlLinkUpdate.value = '';
    recipeImage.value = emptyImage;
  };
  isImagePresent.value = isImageIsMain.value;
  isImageLinkPresent.value = true;
  recipeImage.value = '';
  recipeImage.value = imageUrlLink.value;

  recipeImageList.value.unshift({
    url: recipeImage.value,
    source: $keys.KEY_NAMES.EXTERNAL_LINK,
    id: generateUUID(),
    isMainImage: !recipeImageList.value?.length,
    tooltip: showRecipeImageTooltip($keys.KEY_NAMES.EXTERNAL_LINK),
  });

  setRecipePreviewImage();
  uploadMediaPopup.value = false;
  checkMediaPresent();
  closeModal();
};

const openLinkPage = () => {
  window.open(urlLinkUpdate.value, '_blank');
};

const urlCheckingDataForImage = () => {
  isImageLoadingIcon.value = true;
  if (uploadImageLinkText.value.trim() !== '') {
    try {
      const url = new URL(uploadImageLinkText.value.trim());
      isAddButtonValidImageURL.value = true;
      isValidImageURLlink.value = true;
      imageUrlLink.value = url?.href || '';
      hostNameImageUrlLink.value = url?.hostname || '';
      isImageLoadingIcon.value = false;
      hasCorrectImageIcon.value = true;
    } catch (_) {
      isAddButtonValidImageURL.value = false;
      isValidImageURLlink.value = false;
      imageUrlLink.value = '';
      hostNameImageUrlLink.value = '';
      isImageLoadingIcon.value = false;
      hasCorrectImageIcon.value = false;
      return false;
    }
  } else {
    isAddButtonValidImageURL.value = false;
    isValidImageURLlink.value = false;
    imageUrlLink.value = '';
    hostNameImageUrlLink.value = '';
    isImageLoadingIcon.value = false;
    hasCorrectImageIcon.value = false;
  }
};

const urlCheckingData = () => {
  isVideoLoadingIcon.value = true;
  if (uploadLinkText.value !== '') {
    try {
      const url = new URL(uploadLinkText.value);
      isAddButtonValidURL.value = true;
      isValidURLlink.value = true;
      urlLink.value = url?.href || '';
      hostNameUrlLink.value = url?.hostname || '';
      isVideoLoadingIcon.value = false;
      hasCorrectVideoIcon.value = true;
    } catch (_) {
      isAddButtonValidURL.value = false;
      isValidURLlink.value = false;
      urlLink.value = '';
      hostNameUrlLink.value = '';
      hasCorrectVideoIcon.value = false;
      isVideoLoadingIcon.value = false;
      return false;
    }
  } else {
    isAddButtonValidURL.value = false;
    isValidURLlink.value = true;
    urlLink.value = '';
    hostNameUrlLink.value = '';
    hasCorrectVideoIcon.value = false;
    isVideoLoadingIcon.value = false;
  }
};

const openRecipeComponent = () => {
  if (recipePrice.value === '0') {
    recipePrice.value = '0';
  }
  if (ingredientsData.value && ingredientsData.value[defaultLang.value] && ingredientsData.value[defaultLang.value].children) {
    ingredientsData.value[defaultLang.value].children.forEach((data) => {
      if (data.children?.length && data.children?.length > 0) {
        data.children.forEach(() => {
          ingredientsCount.value++;
        });
      }
    });
  }
  recipePrice.value = recipePrice.value ? recipePrice.value.toString() : '';
  isOpenPreviewRecipe.value = true;
};

const backPreviewConfirm = () => {
  window.scrollTo(0, 0);
  router.push({
    path: 'recipe-preview-details',
    query: { isin: recipeIsin.value },
  });
  isCampaignModified.value = false;
  $eventBus.emit('campaignModified', isCampaignModified.value);
};

const showDeleteVideoPopup = () => {
  isDeleteVideoModal.value = true;
  deleteVideoInfoTitle.value = isLinkPresent.value ? 'Delete Link?' : 'Delete Video?';
  deleteVideoInfoName.value = isLinkPresent.value ? 'link?' : 'video?';
};

const deleteImage = (id) => {
  deleteRecipeImage(id);
  isMainImageNotSelected.value = false;
  checkMediaPresent();
  isImagePresent.value = false;
  isCampaignModified.value = true;
  isDeleteImageModal.value = false;
  recipeImage.value = '';
  imageResponseUrl.value = '';
  imageUrlLinkUpdate.value = '';
  isImageLinkPresent.value = false;
  hostNameImageUrlLinkUpdate.value = '';
  file.value = [];
  recipeImage.value = emptyImage;

  if (uploadImagePercentage.value !== 0 && uploadImagePercentage.value !== 100) {
    uploadImagePercentage.value = 0;
    cancelImage.cancel();
    cancelImage.value = {};
  }
  setRecipePreviewImage();
};

const deleteRecipeImage = (id) => {
  recipeImageList.value.forEach((data) => {
    if (data.id === id) {
      recipeImageList.value.splice(recipeImageList.value.indexOf(data), 1);
    }
  });
};

const deleteVideo = () => {
  isVideoPresent.value = false;
  isVideoModified.value = false;
  isCampaignModified.value = true;
  isDeleteVideoModal.value = false;
  videoResponseUrl.value = '';
  productVideo.value = '';
  urlLinkUpdate.value = '';
  linkURLImage.value = '@/assets/images/empty-video.png';
  hostNameUrlLinkUpdate.value = '';
  isLinkPresent.value = false;
  loadedVideoSize.value = 0;

  if (uploadPercentage.value !== 0 && uploadPercentage.value !== 100) {
    cancelVideo.cancel();
    uploadPercentage.value = 0;
    cancelVideo.value = {};
  }
  checkMediaPresent();
};

const openVideoPopup = () => {
  videoLink.value = productVideo.value;
  hasOpenVideo.value = true;
};

const openRecipeStepVideoPopup = (data) => {
  videoLink.value = data;
  hasOpenVideo.value = true;
};

const parseRecipeIngredient = async (id) => {
  toggleDropdownOff();

  const payload = {
    input: {
      [lang.value]: id,
    },
  };

  try {
    const response = await RecipeService.postRecipeIngredientParse(
      project.value,
      payload,
      store,
      $auth
    );

    let uomData = '', nameData = '', quantityData = 0;
    if (response?.[lang.value]?.quantities?.[0]?.unit) {
      ingredientsUomList.value.forEach(uom => {
        if (response[lang.value].quantities[0].unit === uom.key) {
          uomData = uom.display;
        }
      });
    }

    if (response?.[lang.value]?.ingredient) {
      nameData = response[lang.value].ingredient;
    }

    if (response?.[lang.value]?.quantities?.[0]?.amount) {
      quantityData = response[lang.value].quantities[0].amount;
    }

    if (ingredientsData.value?.[lang.value]?.children?.[0]?.children) {
      if (ingredientsData.value[lang.value].children[0].name === "") {
        availableLang.value.forEach(lang => {
          ingredientsData.value[lang.language].children[0].children.push({
            name: nameData,
            nameMirror: nameData,
            quantity: quantityData,
            quantityMirror: quantityData,
            UOM: uomData,
            UOMMirror: uomData,
            rawText: id,
            uomAutocomplete: false,
            id: "newId" + newIdForIngredient.value,
            ingredientId: "ingredientId" + newIdForIngredient.value,
            foodItem: "",
            group: "",
            excludeFromNutrition: false,
            hasOverridingCampaign: false,
            campaignData: null,
            originalCampaignData: null,
            weightInGrams: 0,
            volumeInMl: 0,
            level: "main",
            modifier: "",
            note: ingredientsDataNotes.value.trim(),
            keywords: [],
            keywordInput: "",
            globalKeyword: [],
            isChecked: false,
            isDropDown: false,
            externalId: "",
          });
        });
      }
    } else {
      availableLang.value.forEach(lang => {
        ingredientsData.value[lang.language].children.unshift({
          children: [],
          displayGroup: false,
          setGroupName: false,
          name: "",
        });
      });
      if (ingredientsData.value[lang.value].children[0].name === "") {
        availableLang.value.forEach(lang => {
          ingredientsData.value[lang.language].children[0].children.push({
            name: nameData,
            nameMirror: nameData,
            quantity: quantityData,
            quantityMirror: quantityData,
            UOM: uomData,
            UOMMirror: uomData,
            rawText: id,
            uomAutocomplete: false,
            id: "newId" + newIdForIngredient.value,
            ingredientId: "ingredientId" + newIdForIngredient.value,
            foodItem: "",
            group: "",
            excludeFromNutrition: false,
            hasOverridingCampaign: false,
            campaignData: null,
            originalCampaignData: null,
            level: "main",
            modifier: "",
            weightInGrams: 0,
            volumeInMl: 0,
            note: ingredientsDataNotes.value.trim(),
            keywords: [],
            keywordInput: "",
            globalKeyword: [],
            isChecked: false,
            isDropDown: false,
            externalId: "",
          });
        });
      }
    }

    ingredientsDataText.value = '';
    let storeData = ingredientsData.value;
    ingredientsData.value = {};
    ingredientsData.value = storeData;

    setTimeout(() => getIngredientNames(), 500);

    closeModal();

    setTimeout(() => {
      items.value = [];
      if (
        ingredientsData.value &&
        ingredientsData.value[recipeVariantSelectedLanguage.value] &&
        ingredientsData.value[recipeVariantSelectedLanguage.value].children
      ) {
        items.value = JSON.parse(
          JSON.stringify(
            ingredientsData.value[recipeVariantSelectedLanguage.value].children
          )
        );
      }
    }, 1000);

  } catch (error) {
    showLoader.value = false;
    ingredientsDataText.value = '';
    closeModal();
  }
};

const addGroup = () => {
  isAddGroupConfirm.value = false;
  toggleDropdownOff();
  isCampaignModified.value = true;

  availableLang.value.forEach((data) => {
    ingredientsData.value[data.language].children.push({
      name: '',
      children: [],
      displayGroup: true,
      setGroupName: true,
    });
  });

  setTimeout(() => {
    let items = [];
    if (ingredientsData.value && ingredientsData.value[recipeVariantSelectedLanguage.value]) {
      items = JSON.parse(
        JSON.stringify(ingredientsData.value[recipeVariantSelectedLanguage.value].children)
      );
    }
  }, 1000);
};

const addGroupConfirm = () => {
  if (availableLang.value?.length > 1 && !isAddGroupPopup.value) {
    isAddGroupConfirm.value = true;
    isAddGroupPopup.value = true;
  } else {
    addGroup();
  }
};

const displayRemovePopUp = (index) => {
  deleteGroupIndex.value = index;
  isDeleteGroupModal.value = true;
};

const deleteGroup = () => {
  isCampaignModified.value = true;
  availableLang.value.forEach((data) => {
    ingredientsData.value[data.language].children.splice(deleteGroupIndex.value, 1);
  });
  isDeleteGroupModal.value = false;
};

const backButton = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backButtonConfirm();
  }
};

const dragIngredient = () => {
  isCampaignModified.value = true;
  $eventBus.emit('campaignModified', isCampaignModified.value);
};

const backButtonConfirm = () => {
  const query = route.query;
  const hasEditRecipeQuery = query?.editRecipeQuery;
  const hasFilter = query?.filter;
  const hasFrom = query?.from !== '1';

  const buildQuery = () => {
    const result = {};
    if (hasEditRecipeQuery) result[QUERY_PARAM_KEY.SEARCH] = query.editRecipeQuery;
    if (hasFilter) result.data = query.filter;
    if (hasFrom) result.page = query.from;
    return result;
  };

  const navigateToRecipes = () => {
    router.push({
      path: '/recipes',
      query: buildQuery(),
    });
  };

  if (hasEditRecipeQuery || hasFilter) {
    navigateToRecipes();
  } else if (route.hash.includes('#stepsSection')) {
    router.go(-2);
  } else {
    router.go(-1);
  }
  isCampaignModified.value = false;
  $eventBus.emit('campaignModified', isCampaignModified.value);
};

const toggleSwitch = () => {
  if (!isSchedulePublishVisible.value) {
    if (!hasShowPublishButton.value || !isShowSaveButton.value) {
      hasShowPublishButton.value = true;
      isShowSaveButton.value = true;
      isUnpublishingText.value = false;
      recipeButtonState.value = 'Publish';
      recipeButtonPublishState.value = 'Publish';
    } else {
      hasShowPublishButton.value = false;
      isShowSaveButton.value = false;
      isUnpublishingText.value = true;
      recipeButtonState.value = 'Save';
      recipeButtonPublishState.value = 'Save';
    }
    isCampaignModified.value = true;
  }
  checkMediaPresent();
};

const setMainImageWarning = (data) => {
  if (data) {
    isCampaignModified.value = true;
  }
  isMainImageNotSelected.value = data;
  checkMediaPresent();
};

const checkMediaPresent = () => {
  let showNoMediaError = false;
  let showErrorMessage = false;
  let showMainErrorMessage = false;
  let isMainImage = false;
  if (recipeImageList.value?.length) {
    isMainImage = recipeImageList.value.some((item) => item?.isMainImage);
  }

  if (
    hasShowPublishButton.value &&
    !imageUrlLinkUpdate.value &&
    !recipeImage.value &&
    !isMainImage &&
    !productVideo.value &&
    !isLinkPresent.value
  ) {
    showNoMediaError = !recipeImageList.value?.length;
    showErrorMessage = !!recipeImageList.value?.length;
    showMainErrorMessage = !!recipeImageList.value?.length;
    setMediaErrorState(showNoMediaError, showErrorMessage, showMainErrorMessage);
    if (showNoMediaError || showErrorMessage || showMainErrorMessage) {
      scrollToMediaSectionAsync();
    }
  } else {
    resetMediaSelectionError();
  }
};

const resetMediaSelectionError = () => {
  setMediaErrorState(false, false, false);
};

const setMediaErrorState = (showNoMediaError, showErrorMessage, showMainErrorMessage) => {
  config.value.showNoMediaError = showNoMediaError;
  config.value.showErrorMessage = showErrorMessage;
  config.value.showMainErrorMessage = showMainErrorMessage;
};

const scrollToMediaSectionAsync = async () => {
  await delay(100);
  const mediaSection = getRef('media-section');
  if (mediaSection) {
    mediaSection.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });
  }
};

const closeInstructionPageAsyncAsync = async (stepData, stepIndex, deleteInstructionsData, addedIngredient, deletedIngredient, modified) => {
  let data = [];
  if (modified !== 'data_not_changed') {
    isCampaignModified.value = true;
  }
  displayInstructionsPage.value = false;
  data = tasksData.value;
  tasksData.value = [];
  tasksData.value = data;
  taskID.value = [];
  let firstTime = 0;

  if (tasksData.value && tasksData.value?.length > 0) {
    for (const tasksdata of tasksData.value) {
      if (tasksdata.isFirstTime) {
        await setRecipeStepFirstTime();
        firstTime++;
      }
    }
    await mediaResetForEachStep();
    tasksData.value.forEach((tasksdata) => {
      if (
        tasksdata &&
        tasksdata[recipeVariantSelectedLanguage.value] &&
        tasksdata[recipeVariantSelectedLanguage.value].ingredients
      ) {
        tasksdata[recipeVariantSelectedLanguage.value].ingredients.forEach(
          (item) => {
            if (item && item.id) {
              taskID.value.push(item.id);
            }
          }
        );
      }
    });
  }

  if (recipeVariantSelectedLanguage.value === defaultLang.value && firstTime <= 0) {
    await addInstructionForVariant(stepData, stepIndex, deleteInstructionsData);
    await addIngredientForVariant(stepIndex, addedIngredient, deletedIngredient);
  }

  const ele = getRef("stepCount" + stepIndex);
  if (ele) {
    ele.scrollIntoView({ block: "center" });
  }
};

const rearrangeStepIngredient = (stepIndex) => {
  if (
    availableLang.value &&
    availableLang.value?.length > 1 &&
    defaultLang.value === recipeVariantSelectedLanguage.value
  ) {
    let copyIngredientsData = [];
    copyIngredientsData = tasksData.value;

    if (
      tasksData.value &&
      tasksData.value[stepIndex] &&
      tasksData.value[stepIndex][defaultLang.value] &&
      tasksData.value[stepIndex][defaultLang.value].ingredients
    ) {
      tasksData.value[stepIndex][defaultLang.value].ingredients.forEach((ing, ingIndex) => {
        availableLang.value.forEach((lang) => {
          if (lang.language !== defaultLang.value) {
            if (
              copyIngredientsData &&
              copyIngredientsData[stepIndex] &&
              copyIngredientsData[stepIndex][lang.language]
            ) {
              copyIngredientsData[stepIndex][lang.language].ingredients.forEach((c_ing, c_ingIndex) => {
                if (ing.id === c_ing.id) {
                  const ind_data = c_ingIndex;
                  copyIngredientsData[stepIndex][lang.language].ingredients.splice(ind_data, 1);
                }
              });
            }
          }
        });
      });
    }
    tasksData.value = copyIngredientsData;
  }
};

const addIngredientForVariant = (stepIndex, addedIngredient, deletedIngredient) => {
  tasksData.value.forEach((data, taskIndex) => {
    if (taskIndex === stepIndex) {
      availableLang.value.forEach((lang) => {
        if (lang?.language !== defaultLang.value) {
          processIngredientsForLang(data, lang.language, addedIngredient, deletedIngredient);
        }
      });
    }
  });
  rearrangeStepIngredient(stepIndex);
};

const processIngredientsForLang = (data, lang, addedIngredient, deletedIngredient) => {
  const ingredients = ingredientsData[lang]?.children;
  ingredients?.forEach(dataGroup => {
    if (dataGroup?.children?.length) {
      addIngredients(data, lang, dataGroup.children, addedIngredient);
      removeIngredients(data, lang, deletedIngredient);
    }
  });
};

const addIngredients = (data, lang, ingredientsDataList, addedIngredient) => {
  ingredientsDataList?.forEach((ingredientsData) => {
    if (addedIngredient.includes(ingredientsData?.id)) {
      data[lang]?.ingredients?.push(createIngredientObject(ingredientsData));
    }
  });
};

const removeIngredients = (data, lang, deletedIngredient) => {
  data[lang].ingredients = data[lang]?.ingredients?.filter((ingredient) =>
    !deletedIngredient.includes(ingredient.id)
  );
};

const createIngredientObject = (ingredientsData) => {
  const getValue = (key, defaultValue) => ingredientsData[key] || defaultValue;
  return {
    name: getValue("name", ""),
    nameMirror: getValue("nameMirror", ""),
    id: getValue("id", ""),
    quantity: getValue("quantity", 0),
    quantityMirror: getValue("quantityMirror", 0),
    UOM: getValue("UOM", ""),
    UOMMirror: getValue("UOMMirror", ""),
    rawText: getValue("rawText", ""),
    uomAutocomplete: false,
    note: getValue("note", ""),
    remark: getValue("remark", ""),
    modifier: getValue("modifier", ""),
    keywords: getValue("keywords", []),
    externalId: getValue("externalId", ""),
    productId: getValue("productId", ""),
    isChecked: getValue("isChecked", false),
    isDropDown: getValue("isDropDown", false),
    group: getValue("group", ""),
    shoppable: getValue("shoppable", false),
  };
};

const addInstructionForVariant = (stepData, stepIndex, deleteInstructionsData) => {
  tasksData.value.forEach((data, taskIndex) => {
    if (taskIndex === stepIndex) {
      availableLang.value.forEach((lang) => {
        if (lang.language !== defaultLang.value) {
          if (stepData && stepData.instructions) {
            stepData.instructions.forEach((step) => {
              if (step.instructions_id === "new_instructions_id") {
                data[lang.language].instructions.push({
                  instructions_id: "new_instructions_id",
                  text: step.text ? step.text : "",
                  times: [],
                });
              }
            });
          }
          if (deleteInstructionsData && deleteInstructionsData?.length > 0) {
            deleteInstructionsData.forEach((deleteData) => {
              data[lang.language].instructions.forEach(
                (item, indexDelete) => {
                  if (item && item.instructions_id === deleteData) {
                    data[lang.language].instructions.splice(indexDelete, 1);
                  }
                }
              );
            });
          }
        }
      });
    }
  });

  tasksData.value.forEach((data) => {
    availableLang.value.forEach((lang) => {
      if (data && data[lang.language] && data[lang.language].instructions) {
        data[lang.language].instructions.forEach((item) => {
          if (item && item.instructions_id) {
            delete item.instructions_id;
          }
        });
      }
    });
  });
};

const mediaResetForEachStep = () => {
  if (tasksData.value && tasksData.value?.length > 0) {
    tasksData.value.map((data) => {
      availableLang.value.forEach((lang) => {
        if (data?.[lang.language]?.media) {
          data[lang.language].media = {
            image: data?.[defaultLang.value]?.media?.image ?? "",
            video: [
              {
                url: data?.[defaultLang.value]?.media?.video?.[0]?.url ?? "",
              },
            ],
          };
        }
      });
    });
  }
};

const setRecipeStepFirstTime = () => {
  if (!tasksData.value || !tasksData.value?.length) return;
  tasksData.value.forEach((data, indexTask) => {
    data.isFirstTime = false;
    availableLang.value.forEach((lang) => {
      if (lang.language !== defaultLang.value) {
        updateLanguageData(data, lang, indexTask);
      }
    });
  });
};

const updateLanguageData = (data, lang, indexTask) => {
  if (!data[defaultLang.value]) return;
  const ingredientLang = getIngredientLang(data, lang);
  const instructionLang = getInstructionLang(data);
  const document = {
    [lang.language]: {
      title: data[defaultLang.value].title || "",
      instructions: instructionLang,
      ingredients: ingredientLang,
      media: getMedia(data),
    },
  };
  tasksData.value[indexTask] = { ...data, ...document };
};

const getMedia = (data) => {
  return {
    image: data[defaultLang.value]?.media?.image || "",
    video: [
      {
        url: data[defaultLang.value]?.media?.video?.[0]?.url || "",
      },
    ],
  };
};

const getIngredientLang = (data, lang) => {
  const ingredientLang = [];
  if (data[defaultLang.value]?.ingredients?.length) {
    data[defaultLang.value].ingredients.forEach((item) => {
      ingredientsData[lang.language]?.children?.forEach((dataGroup) => {
        dataGroup.children.forEach((ingredientsData) => {
          if (item.id === ingredientsData.id) {
            ingredientLang.push(createIngredientDetail(ingredientsData));
          }
        });
      });
    });
  }
  return ingredientLang;
};

const getInstructionLang = (data) => {
  const instructionLang = [];
  if (data[defaultLang.value]?.instructions?.length) {
    data[defaultLang.value].instructions.forEach((item) => {
      instructionLang.push({
        text: item.text || "",
        times: item.times || [],
      });
    });
  }
  return instructionLang;
};

const createIngredientDetail = (ingredientsData) => {
  return {
    UOM: getValueOrDefault(ingredientsData.UOM, ""),
    UOMMirror: getValueOrDefault(ingredientsData.UOMMirror, ""),
    excludeFromNutrition: getBooleanValue(ingredientsData.excludeFromNutrition, false),
    foodItem: getValueOrDefault(ingredientsData.foodItem, ""),
    group: getValueOrDefault(ingredientsData.group, ""),
    keywordInput: getValueOrDefault(ingredientsData.keywordInput, ""),
    globalKeyword: getArrayOrDefault(ingredientsData.globalKeyword, []),
    keywords: [...ingredientsData.keywords],
    level: getValueOrDefault(ingredientsData.level, "main"),
    modifier: getValueOrDefault(ingredientsData.modifier, ""),
    name: getValueOrDefault(ingredientsData.name, ""),
    nameMirror: getValueOrDefault(ingredientsData.nameMirror, ""),
    note: getValueOrDefault(ingredientsData.note, ""),
    remark: getValueOrDefault(ingredientsData.remark, ""),
    quantity: getNumberOrDefault(ingredientsData.quantity, 0),
    id: getValueOrDefault(ingredientsData.id, ""),
    quantityMirror: getNumberOrDefault(ingredientsData.quantityMirror, 0),
    rawText: getValueOrDefault(ingredientsData.rawText, ""),
    uomAutocomplete: getBooleanValue(ingredientsData.uomAutocomplete, false),
  };
};

const getValueOrDefault = (value, defaultValue) => {
  return value || defaultValue;
};

const getBooleanValue = (value, defaultValue) => {
  return typeof value === 'boolean' ? value : defaultValue;
};

const getArrayOrDefault = (array, defaultValue) => {
  return Array.isArray(array) ? array : defaultValue;
};

const getNumberOrDefault = (value, defaultValue) => {
  return typeof value === 'number' ? value : defaultValue;
};

const getRecipe = async (id) => {
  const params = {
    country: lang.value.split("-")[1],
  };
  try {
    await store.dispatch("recipe/getRecipeAsync", {
      params,
      isin: id,
    });
    const response = store.getters["recipe/getRecipeData"];
    recipeData.value = mapRecipeData(response);
    recipeTxnId.value = getResponseValue(response, "txnId", "").toString();
    displayPublishButton(recipeData.value.state);
    await getEditRecipeDataAsync(recipeData.value);
    nutritionKey.value = response?.nutrientTable?.[lang.value] ?? {};
    checkRecipeEvent($keys.EVENT_KEY_NAMES.VIEW_EDIT_RECIPE);
  } catch (error) {
    showLoader.value = false;
    isDataLoading.value = false;
    $eventBus.emit("routeloading", isDataLoading.value);
  }
};

const getResponseValue = (response, key, defaultValue) => {
  return response?.[key] ?? defaultValue;
};

const mapRecipeData = (response) => {
  return {
    boostInSearch: !!response.boostInSearch,
    labels: getResponseValue(response, "labels", []),
    price: getResponseValue(response, "price", {}),
    countries: getResponseValue(response, "countries", []),
    tags: getResponseValue(response, "tags", {}),
    categories: getResponseValue(response, "categories", {}),
    diets: getResponseValue(response, "diets", []),
    ingredients: getResponseValue(response, "ingredients", {}),
    isin: getResponseValue(response, "isin", ""),
    langs: getResponseValue(response, "langs", []),
    lastPublishDate: getResponseValue(response, "lastPublishDate", ""),
    media: getResponseValue(response, "media", {}),
    scope: getResponseValue(response, "scope", ""),
    slug: getResponseValue(response, "slug", ""),
    state: getResponseValue(response, "state", ""),
    provider: getResponseValue(response, "provider", ""),
    status: getResponseValue(response, "status", {}),
    tasks: getResponseValue(response, "tasks", []),
    time: getResponseValue(response, "time", {}),
    title: getResponseValue(response, "title", {}),
    yield: getResponseValue(response, "yield", {}),
    subtitle: getResponseValue(response, "subtitle", {}),
    attribution: getResponseValue(response, "attribution", {}),
    description: getResponseValue(response, "description", {}),
    nutrientTable: getResponseValue(response, "nutrientTable", []),
    externalId: getResponseValue(response, "externalId", ""),
  };
};

const getEditRecipeDataAsync = async (recipeData) => {
  if (recipeData?.isin) {
    await getEditRecipeScheduleData(recipeData.isin);
  }
  if (recipeData?.langs?.length > 0) {
    recipeData.langs.forEach((lang) => {
      let langData = {
        language: lang,
        default_check: lang === defaultLang.value,
        language_name: lang === "es-US" ? "Spanish" :
                       lang === "fr-FR" ? "French" :
                       lang === "en-US" ? "English" : "",
        languageFlag: lang === "es-US" ? "/images/flags/spain-flag.png" :
                      lang === "fr-FR" ? "/images/flags/france-flag.png" :
                      lang === "en-US" ? "/images/flags/us-flag.png" : "",
      };
      availableLang.value.push(langData);
    });
  }

  isBoostInSearch.value = !!recipeData.boostInSearch;
  initiallyVariantSupported.value = [...recipeData.langs];
  recipeName.value = recipeData?.title?.[lang.value] ?? "";
  recipeSubtitle.value = recipeData?.subtitle?.[lang.value] ?? "";
  servings.value = recipeData?.yield?.["serving"] ?? "";
  nutritionServingSize.value = recipeData?.yield?.["servingSize"] ?? "";
  nutritionServingSizePerContainer.value = recipeData?.yield?.["servingsPerContainer"] ?? "";
  yieldData.value = recipeData?.yield?.["raw"] ?? "";
  if (recipeData?.time) {
    if (recipeData.time?.total) {
      getTime(recipeData.time.total);
    }
    if (recipeData.time?.prep) {
      getPreparationTime(recipeData.time.prep);
    }
    if (recipeData.time?.cook) {
      getCookTime(recipeData.time.cook);
    }
  }

  recipeAttribution.value = recipeData?.attribution?.[lang.value] ?? null;
  recipeAttributionAuthor.value = recipeData?.attribution?.[lang.value]?.author?.name ?? "";
  recipeAttributionExternalId.value = recipeData.externalId ?? "";
  recipeAttributionOrganization.value = recipeData?.attribution?.[lang.value]?.organization ?? "";

  const mediaData = recipeData?.media?.[lang.value] ?? {};
  recipeImage.value = mediaData?.image ?? mediaData?.externalImageUrl;
  isImagePresent.value = !!recipeImage.value;
  showRecipeCameraRoll(mediaData.imageList);

  description.value = recipeData?.description?.[lang.value]?.abstract ?? "";
  const videoData = mediaData.video?.[0] || {};
  productVideo.value = videoData.url ?? "";
  videoDimension.value = videoData.size ?? "";
  urlLinkUpdate.value = mediaData.externalVideoUrl ?? "";
  imageUrlLinkUpdate.value = mediaData.externalImageUrl ?? "";
  imageUrlThumbnailUpdate.value = mediaData.thumbnailImageUrl ?? "";
  if (publisherDataList.value?.length) {
    publisherDataList.value.forEach((data) => {
      if (data?.isin === recipeAttributionOrganization.value) {
        selectedPublisherName.value = data.name ?? "";
        selectedPublisherImage.value = data.image?.url ?? defaultOrganizationsImage;
      }
    });
  }
  if (imageUrlLinkUpdate.value) {
    try {
      const url = new URL(imageUrlLinkUpdate.value);
      imageUrlLink.value = url?.href ?? "";
      hostNameImageUrlLink.value = url?.hostname ?? "";
      if (imageUrlLinkUpdate.value) {
        hostNameUrlLinkUpdate.value = url.hostname.toUpperCase();
        const image = new Image();
        image.src = imageUrlLink.value;
        image.onerror = function () {
          recipeImage.value = emptyImage;
        };
      }
      recipeImage.value = imageUrlLink.value;
      isImageLinkPresent.value = true;
    } catch (_) {
      urlLinkUpdate.value = "";
      return false;
    }
  }

  if (urlLinkUpdate.value) {
    try {
      const url = new URL(urlLinkUpdate.value);
      urlLink.value = url.href ?? "";
      hostNameUrlLink.value = url.hostname ?? "";
      if (urlLinkUpdate.value) {
        hostNameUrlLinkUpdate.value = url.hostname.toUpperCase();
        if (urlLinkUpdate.value.includes("youtube") && url.searchParams.get("v")) {
          linkURLImage.value = `http://img.youtube.com/vi/${url.searchParams.get("v")}/hqdefault.jpg`;
        } else {
        linkURLImage.value = emptyVideo;
        }
      }
      isLinkPresent.value = true;
    } catch (_) {
      urlLinkUpdate.value = "";
      return false;
    }
  }
  await getTags(recipeData);
  await getDiets(recipeData);
  await getCategories(recipeData);
  await getAllergens(recipeData);
  if (recipeData?.provider === "fims") {
    recipeData.tasks.forEach((item) => {
      item.isChecked = true;
      item.isDropDown = true;
    });
  } else {
    recipeData.tasks.forEach((item) => {
      item.isChecked = false;
      item.isDropDown = false;
    });
  }
  selectedCurrency.value = recipeData?.price?.currency ?? "";
  recipePrice.value = recipeData?.price?.value >= 0 ? recipeData.price.value : "";
  if (recipePrice.value === "0") {
    recipePrice.value = "0";
  }
  if (typeof recipePrice.value === "string" && recipePrice.value.includes(".")) {
    recipePrice.value = parseFloat(recipePrice.value).toFixed(2);
  }
  if (recipeData?.slug) {
    if (availableLang.value?.length > 0) {
      const copyObjectData = availableLang.value.map(lang => ({
        [lang.language]: {
          slugName: recipeData?.slug?.[lang.language] ?? "",
          slugStatus: false,
          isin: recipeID ? recipeID : "",
        }
      }));
      slugData.value = { ...copyObjectData.reduce((acc, obj) => ({ ...acc, ...obj }), {}) };
      slugEdit.value = slugData.value?.[lang.value]?.slugName ?? "";

    }
  }
  const ingredientsList = ref([]);
  recipeData.tasks.map((data) => {
    if (availableLang.value && availableLang.value.length > 0) {
      availableLang.value.forEach((lang) => {
        if (data?.[lang.language]?.ingredients) {
          ingredientsList.value = [];
          data[lang.language].ingredients.map((ingredientsData) => {
            const ingredient = {
              name: ingredientsData.name ?? "",
              nameMirror: ingredientsData?.name || "",
              quantity: ingredientsData?.amount?.value ?? 0,
              quantityMirror: ingredientsData?.amount?.value ?? 0,
              UOM: ingredientsData?.amount?.unit ?? "",
              UOMMirror: ingredientsData?.amount?.unit ?? "",
              rawText: ingredientsData.rawText || "",
              productId: ingredientsData.productId || "",
              uomAutocomplete: false,
              foodItem: ingredientsData?.foodItem || "",
              group: ingredientsData.group || "",
              excludeFromNutrition: ingredientsData.excludeFromNutrition || false,
              level: ingredientsData.level || "main",
              modifier: ingredientsData.modifier || "",
              note: ingredientsData.note || "",
              remark: ingredientsData.remark || "",
              keywords: ingredientsData.keywords || [],
              keywordInput: ingredientsData.keywordInput || "",
              globalKeyword: ingredientsData.globalKeyword || [],
            };
            ingredientsList.value.push(ingredient);
          });
          data[lang.language].ingredients = [...ingredientsList.value];
        } else {
          data[lang.language].ingredients = [];
        }
      });
    }
  });
  recipeData.tasks.map((data) => {
    if (availableLang.value && availableLang.value.length > 0) {
      availableLang.value.forEach((lang) => {
        if (data && data[lang.language] && data[lang.language].ingredients) {
          data[lang.language].ingredients.map((ingredientsData) => {
            const foundElement = ingredientsUomList.value.find(
              (element) => ingredientsData.UOMMirror === element.key
            );
            ingredientsData.UOMMirror = foundElement ? foundElement.display : "";
          });
        }
      });
    }
  });
  recipeData.tasks.forEach((data) => {
    if (availableLang.value && availableLang.value.length > 0) {
      let objectCheck = [];
      availableLang.value.forEach((lang) => {
        const document = {
          [lang.language]: {
            title: data?.[lang.language]?.title || "",
            instructions: data?.[lang.language]?.instructions || [],
            ingredients: data?.[lang.language]?.ingredients || [],
            media: {
              image: data?.[lang.language]?.media?.image || "",
              video: [
                {
                  url:
                    data?.[lang.language]?.media?.video?.[0]?.url || "",
                },
              ],
            },
          },
        };
        objectCheck.push(document);
      });
      const updatedVariantList = Object.assign(
        { isChecked: false, isDropDown: false, isFirstTime: false },
        ...objectCheck
      );
      tasksData.value.push(updatedVariantList);
    }
  });
  notes.value = recipeData?.description?.[lang.value]?.notes ?? "";
  availableServings.value =recipeData?.yield?.availableServings || [];
  isVideoPresent.value = recipeData?.media?.[lang.value]?.video?.[0]?.url !== undefined;
  if (isVideoPresent.value && isLinkPresent.value) {
    isVideoPresent.value = false;
    isLinkPresent.value = false;
  }
  if (recipeData?.ingredients) {
    await getUpdatedIngredientDataAsync(recipeData.ingredients);
  }
  slugEdit.value = slugData.value?.[lang.value]?.slugName ?? "";
  await getNutritionTable(recipeData);
  await getIngredientNames();
  isDataLoading.value = false;
  $eventBus.emit("routeloading", isDataLoading.value);
};

const showRecipeCameraRoll = (imageList) => {
  if (imageList?.length) {
    const updatedImageList = imageList.map(data => ({
      ...data,
      id: generateUUID(),
    }));
    const mainImageEntry = updatedImageList.find(image => image.url === recipeImage.value);
    const mainImageId = mainImageEntry ? mainImageEntry.id : null;

    recipeImageList.value = updatedImageList.map((data) => ({
      ...data,
      isMainImage: mainImageId ? data.id === mainImageId : false,
      tooltip: showRecipeImageTooltip(data.source),
    }));
  }

  if (recipeImage.value) {
    const recipeImageExists = recipeImageList.value.some((data) => data.url === recipeImage.value);
    if (!recipeImageExists) {
      recipeImageList.value.push({
        url: recipeImage.value,
        id: generateUUID(),
        isMainImage: true,
        source: $keys.KEY_NAMES.UNKNOWN,
        tooltip: showRecipeImageTooltip($keys.KEY_NAMES.UNKNOWN),
      });
    }
  }
};

const showRecipeImageTooltip = (text) => {
  const keyNames = $keys.KEY_NAMES;
  const tooltipTexts = {
    [keyNames.UNKNOWN]: t("UNKNOWN"),
    [keyNames.AI_GENERATED]: t("AI_GENERATED"),
    [keyNames.MANUAL_UPLOAD]: t("UPLOADED_IMAGE"),
    [keyNames.EXTERNAL_LINK]: t("URL_IMAGE"),
  };
  return tooltipTexts[text] || text;
};

const displayNutritionData = (num, index, array) => {
  if (num >= 10000) {
    array[index].valueAmount = 9999.9999;
  }
  let totalPassNumber = 0;
  if (num < 10000) {
    let totalCountNumber = num.toString()?.length;
    if (totalCountNumber > 9) {
      totalPassNumber = num.toString().slice(0, 9);
      array[index].valueAmount = totalPassNumber;
    }
  }
};

const checkNutritionalData = (data, value, nIndex) => {
  data.forEach((item, dIndex) => {
    if (nIndex === dIndex) {
      item.valueAmount = value === 0 || value < 0 ? 0 : item.valueAmount;
    }
  });
};
function compareNutritionData(mainNutritionResponseData) {
  displayNutritionalDataList.value = [];
  nutritionDataList.value.forEach((value) => {
    displayNutritionalDataList.value.push({
      nutrientName: value?.name,
      subNutrientName: !!value?.isSubNutrient,
      unitKey: value?.unit?.key || "",
      valueUnit: value?.unit?.abbreviation || "",
      hasDvp: !!value?.hasDvp,
      isHeader: !!value?.isHeader,
      valueAmount: "",
      unit: value?.key || "",
      itemName: value?.key || "",
      nutrientUnit: "",
      dvpValue: "",
    });
  });
  displayNutritionalDataList.value.forEach((data) => {
    if (mainNutritionResponseData.length) {
      mainNutritionResponseData.forEach((item) => {
        if (data.unit === item.name) {
          const amountValue = item?.amount?.value ?? "";
          data.valueAmount = amountValue && String(amountValue).length >= 9
            ? String(amountValue).slice(0, 9)
            : amountValue;
          data.unitKey = item?.amount?.unit || "";
          data.nutrientUnit = item?.amount?.unit || "";
          data.itemName = item?.name || "";
          data.dvpValue = item?.dvp?.value || "";
        }
      });
    }
  });

  displayNutritionalDataList.value.forEach((data) => {
    nutrientUnitsConstants.value.forEach((item) => {
      if (data.unitKey === item.key) {
        data.valueUnit = item?.abbreviation || "";
      }
    });
  });
}

const removeZero = (number) => {
  const numericValue = parseFloat(number);
  return numericValue;
};

const debounceNutritionalDVP = debounce((e) => {
  e.valueAmount = e.valueAmount
    ? removeZero(e.valueAmount)
    : e.valueAmount;
  calculateNutritionalDVPAsync(e);
  isCampaignModified.value = true;
}, 850);

const calculateNutritionalDVPAsync = async (item) => {
  nutritionDataList.value.forEach((data) => {
    if (item.itemName === data.key) {
      if (item.unitKey === "") {
        item.unitKey = data?.unit?.key || "";
      }
    }
  });

  let array = [];
  if (item && item.hasDvp && item.itemName && item.unitKey) {
    let nutrientsData = {
      key: item.itemName,
      unit: item.unitKey,
      value: item.valueAmount,
    };
    array.push(nutrientsData);
  }

  let payload = {
    nutrients: array,
  };

  if (array?.length) {
    await store.dispatch("recipeDetails/calculateNutritionalDVPAsync", {
      payload,
      lang: lang.value,
    })
      .then((response) => {
        let result = response ? [...response] : "";
        result.forEach((data) => {
          if (item.unitKey === data.amount.unit && item.unit === data.name) {
            if (data.amount && data.amount.value === 0 && data.dvp && data.dvp.value === 0) {
              item.dvpValue = "";
            } else if (data && data.amount && data.dvp) {
              item.dvpValue = data.dvp.value;
            }
          }
        });
      })
      .catch(() => {});
  }
};
const getNutritionTable = async (recipeData) => {
  const dataNutrient = [recipeData.nutrientTable];
  dataNutrient?.forEach((data) => {
    if (availableLang.value && availableLang.value.length) {
      availableLang.value.forEach((lang) => {
        if (data?.[lang.language]) {
          if (data[lang.language]?.perServing && data[lang.language]?.per100g) {
            isNutritionDropDownIcon.value = true;
          } else {
            isNutritionDropDownIcon.value = false;
          }

          if (data[lang.language]?.['per100g']) {
            selectedNutritionType.value = "per100g";
            compareNutritionData(data[lang.language]?.['per100g']);
            nutrientTableData.value['per100g'] = displayNutritionalDataList.value;
          }

          if (data[lang.language]?.['perServing']) {
            selectedNutritionType.value = "perServing";
            compareNutritionData(data[lang.language]?.['perServing']);
            nutrientTableData.value['perServing'] = displayNutritionalDataList.value;
          }
        }
      });
    }
  });
};

const getOrganizationsList = async () => {
  const payload = {
    from: 0,
    size: 100,
  };

  try {
    await store.dispatch("organizations/getSearchOrganizationsAsync", {
      payload,
      lang: lang.value,
    });

    const response = store.getters["organizations/getOrganizationsData"];
    publisherDataList.value = response?.data?.results || [];
  } catch (error) {
    showLoader.value = false;
    console.error("Error fetching organizations list", error);
  }
};

const updateTimeProperties = (time, hourKey, minuteKey, secondKey) => {
  if (!time) return;
  const { hour: h, minute: m, second: s } = extractTimeParts(time);

  if (hourKey === 'hour') hour.value = h;
  if (minuteKey === 'minute') minute.value = m;
  if (secondKey === 'second') second.value = s;

  if (hourKey === 'prepHour') prepHour.value = h;
  if (minuteKey === 'prepMinute') prepMinute.value = m;
  if (secondKey === 'prepSecond') prepSecond.value = s;

  if (hourKey === 'cookHour') cookHour.value = h;
  if (minuteKey === 'cookMinute') cookMinute.value = m;
  if (secondKey === 'cookSecond') cookSecond.value = s;
};

const getTime = (time) => {
  updateTimeProperties(time, 'hour', 'minute', 'second');
  isTotalTimeAvailable.value = hour.value || minute.value || second.value;
};

const getPreparationTime = (time) => {
  updateTimeProperties(time, 'prepHour', 'prepMinute', 'prepSecond');
};

const getCookTime = (time) => {
  updateTimeProperties(time, 'cookHour', 'cookMinute', 'cookSecond');
};

const getTags = async (recipeData) => {
  if (!recipeData?.tags?.[lang.value]) return;
  const tags = recipeData.tags[lang.value] || [];

  if (tags?.length) {
    selectedTags.value = [];
    const payload = {
      from: 0,
      size: 15,
      type: $keys.KEY_NAMES.TAG,
      isins: tags.join(","),
    };
    try {
      await store.dispatch(
        "categoriesGroup/getCategoryForCategoryGroupListAsync",
        { payload }
      );
      const response =
        store.getters["categoriesGroup/getCategoryForCategoryGroupList"];
      response.results.forEach((data) => {
        tagsList.value.forEach((item) => {
          if (data.isin === item.isin) {
            item.isAlreadyInSelectedTag = true;
          }
        });
      });
      selectedTags.value = response.results;
    } catch {
      showLoader.value = false;
    }
  }
};

const getDiets = (recipeData) => {
  const dietsData = recipeData?.diets || [];
  if (dietsData?.length) {
    dietsData.forEach((data) => {
      dietsList.value.forEach((item) => {
        if (data === item.key) {
          item.isAlreadyInSelectedDiet = true;
          selectedDiets.value.push(item);
        }
      });
    });
  }
};

const getAllergens = (recipeData) => {
  const allergensData = recipeData?.labels || [];
  if (allergensData?.length) {
    allergensData.forEach((data) => {
      allergensList.value.forEach((item) => {
        if (data === item.key) {
          item.isAlreadyInSelectedAllergens = true;
          selectedAllergens.value.push(item);
        }
      });
    });
  }
};

const getCategories = async (recipeData) => {
  const categoriesData = recipeData?.categories?.[lang.value] || [];
  if (categoriesData?.length) {
    selectedCategories.value = [];
    const payload = {
      from: 0,
      size: 15,
      type: t('COMMON.CATEGORY'),
      isins: categoriesData.join(","),
    };
    try {
      await store.dispatch(
        "categoriesGroup/getCategoryForCategoryGroupListAsync",
        { payload }
      );
      const response =
        store.getters["categoriesGroup/getCategoryForCategoryGroupList"];
      if (response?.results) {
        response.results.forEach((data) => {
          categoriesList.value.forEach((item) => {
            if (data.isin === item.isin) {
              item.isAlreadyInSelectedCategory = true;
            }
          });
        });
        selectedCategories.value = response.results;
      }
    } catch {
      showLoader.value = false;
    }
  }
};

const getUpdatedIngredientDataAsync = async (data) => {
  let totalIngredients = [];
  const ingredientsIsinList = [];

  availableLang.value.forEach((item) => {
    data[item.language]?.forEach((ingredientsData) => {
      const ingredient = {
        name: ingredientsData?.name ?? "",
        nameMirror: ingredientsData?.name ?? "",
        quantity: ingredientsData?.amount?.value ?? 0,
        quantityMirror: ingredientsData?.amount?.value ?? 0,
        UOM: ingredientsData?.amount?.unit ?? "",
        UOMMirror: ingredientsData?.amount?.unit ?? "",
        weightInGrams: ingredientsData?.amount?.weightInGrams?.toString() ?? 0,
        volumeInMl: ingredientsData?.amount?.volumeInMl?.toString() ?? 0,
        rawText: ingredientsData?.rawText ?? "",
        productId: ingredientsData?.productId ?? "",
        foodItem: ingredientsData?.foodItem ?? "",
        uomAutocomplete: false,
        group: ingredientsData?.group ?? "",
        excludeFromNutrition: ingredientsData?.excludeFromNutrition ?? false,
        level: ingredientsData?.level ?? "main",
        modifier: ingredientsData?.modifier ?? "",
        note: ingredientsData?.note ?? "",
        remark: ingredientsData?.remark ?? "",
        keywords: ingredientsData?.keywords ?? [],
        externalId: ingredientsData?.externalId ?? "",
        keywordInput: ingredientsData?.keywordInput ?? "",
        globalKeyword: ingredientsData?.globalKeyword ?? [],
        campaignData: ingredientsData?.campaignData ?? null,
        originalCampaignData: ingredientsData?.originalCampaignData ?? null,
        hasOverridingCampaign: false,
      };
      totalIngredients.push(ingredient);
      if (ingredient.foodItem) ingredientsIsinList.push(ingredient.foodItem);
    });
    ingredientsData.value[item.language] = groupData(totalIngredients);
    totalIngredients = [];
  });
  availableLang.value.forEach((item) => {
    const children = ingredientsData.value[item.language]?.children ?? [];
    children.forEach((data) => {
      data.setGroupName = false;
      data.displayGroup = data.name.trim() !== "";
    });
    children.forEach((data, index) => {
      if (data.name.trim() === "") {
        children.unshift(children.splice(index, 1)[0]);
      }
    });
    if (children[0]?.name !== "") {
      children.unshift({
        children: [],
        displayGroup: false,
        setGroupName: false,
        name: ""
      });
    }
    children.forEach((group, groupIndex) => {
      group.children?.forEach((item, index) => {
        item.id = `oldId${groupIndex}${index}`;
        item.isChecked = false;
        item.isDropDown = false;
        const foundElement = ingredientsUomList.value.find((e) => e.key === item.UOM);
        item.UOM = foundElement?.display ?? "";
      });
    });
  });
  tasksData.value?.forEach((tasksDataItem) => {
    tasksDataItem?.[defaultLang.value]?.ingredients?.forEach((item) => {
      ingredientsData.value[defaultLang.value]?.children.forEach((group) => {
        group.children?.forEach((itemTask, stepIndex) => {
          if (item.rawText === itemTask.rawText) {
            item.id = itemTask.id;
            taskID.value.push(item.id);
          }
          if (item.foodItem && !ingredientsIsinList.includes(item.foodItem)) {
            ingredientsIsinList.push(item.foodItem);
          }
          if (!item.id) {
            item.id = `itemTask${stepIndex}`;
          }
        });
      });
    });
  });
  if (availableLang.value.length > 1) {
    tasksData.value.forEach((task) => {
      availableLang.value.forEach((lang) => {
        task?.[lang.language]?.ingredients?.forEach((itemLang, indexLang) => {
          task?.[defaultLang.value]?.ingredients?.forEach((itemDefault, indexDefault) => {
            if (indexLang === indexDefault) {
              itemLang.id = itemDefault?.id ?? "";
            }
          });
        });
      });
    });
  }
  if (ingredientsIsinList.length && recipeData.value.provider === $keys.KEY_NAMES.FIMS) {
    await foodItemNameAsync(ingredientsIsinList);
  }
  await store.dispatch("recipe/setRecipe", recipeData.value);
};

const foodItemNameAsync = async (names) => {
  try {
    const nameDetails = await fetchFoodItemsAsync(names);
    updateIngredientsData(nameDetails);
    updateTasksData(nameDetails);
  } catch (error) {
    showLoader.value = false;
    console.error($keys.KEY_NAMES.ERROR_IN + 'foodItemNameAsync:', error);
  }
};

const fetchFoodItemsAsync = async (dataArray) => {
  const response = await FoodItemService.getAllFoodItemIsin(
    project.value,
    dataArray,
    lang.value,
    store,
    $auth
  );
  return Object.values(response.results);
};

const updateIngredientsData = (nameList) => {
  availableLang.value?.forEach((data) => {
    ingredientsData.value[data.language]?.children?.forEach((child) => {
      child?.children?.forEach((item) => {
        updateItemName(item, nameList);
      });
    });
  });
};

const updateTasksData = (nameList) => {
  tasksData.value?.forEach((tasksdata) => {
    availableLang.value?.forEach((lang) => {
      const ingredients = tasksdata[lang.language]?.ingredients || [];
      ingredients?.forEach((item) => {
        updateItemName(item, nameList, 'nameMirror');
      });
    });
  });
};

const updateItemName = (item, nameList, nameType = 'name') => {
  nameList?.forEach((namedata) => {
    if (namedata?.isin === item?.foodItem) {
      item.name = namedata?.[nameType]?.singular || '';
    }
  });
};

const groupData = (d) => {
  const grouped = d.reduce((accumulator, current) => {
    if (!accumulator[current.group]) {
      accumulator[current.group] = [];
    }
    accumulator[current.group].push(current);
    return accumulator;
  }, {});

  const result = Object.entries(grouped).map(([groupName, items]) => ({
    name: groupName,
    children: items,
  }));

  return {
    name: 'ChildrenArray',
    children: result,
  };
};

const getIsins = () => {
  return OrganizationsService.getNewIsins(
    project.value,
    'recipe',
    lang.value,
    $auth?.user?.value?.email || '',
    store,
    $auth
  )
    .then((response) => {
      recipeIsin.value = response?.isin || '';
    })
    .catch((e) => {
      nuxtApp.$loading.finish();
      isRecipeSaving.value = false;
      isSaveModalVisible.value = false;
      isPublishModalVisible.value = false;
      isConfirmScheduleRecipePopupVisible.value = false;
      console.error(e);
    });
};

const publishRecipe = (isin) => {
  RecipeService.publishRecipeAsync(project.value, isin.value, store, $auth)
    .then(() => {
    $eventBus.emit('campaignModified', isCampaignModified.value);
    })
    .catch((e) => {
      console.error(e);
    });
};

const unPublishRecipe = (isin) => {
  RecipeService.unPublishRecipeAsync(project.value, isin.value, store, $auth)
    .then(() => {
      $eventBus.emit('campaignModified', isCampaignModified.value);
    })
    .catch((e) => {
      console.error(e);
    });
};

const savePublishRecipeAsync = async () => {
  hasSlugCheckConfirm.value = true;
  if (isSlugStatus.value) {
    isSlugInputWarning.value = true;
    await saveRecipeSlugAsync();
  }
  hasSlugCheckConfirm.value = false;
  resetErrorFlags();
  const hasErrors = performValidations();
  handlePublishAction(hasErrors);
};

const resetErrorFlags = () => {
  isUnableToSaveRecipeName.value = false;
  isUnableToSaveServings.value = false;
  isUnableToSaveAvailableServings.value = false;
  isUnableToSavePlateUp.value = false;
  isUnableToSaveTime.value = false;
  isUnableToSaveIngredients.value = false;
  isUnableToSaveSteps.value = false;
  isUnableToSaveTitleSteps.value = false;
  isUnableToSaveStepInstruction.value = false;
  isUnableToSaveStepIngredientsRawText.value = false;
  isUnableToSaveGroupName.value = false;
};

const performValidations = () => {
  const hasErrors = [
    validateRecipeName(),
    validateServings(),
    validateAvailableServings(),
    validateMediaPresence(),
    validateTime(),
    validateIngredients(),
    validateSteps(),
    validateStepInstructions(),
  ].some(validationResult => validationResult);

  return hasErrors;
};

const validateRecipeName = () => {
  const hasMissingTitle = availableLang.value.some(lang =>
    recipeData.value?.title?.[lang.language] === undefined
  );
  if (hasMissingTitle) {
    isUnableToSaveRecipeName.value = true;
  }
  return hasMissingTitle;
};

const validateServings = () => {
  const isServingsInvalid = !servings.value;
  isUnableToSaveServings.value = isServingsInvalid;
  return isServingsInvalid;
};

const validateAvailableServings = () => {
  const isAvailableServingsInvalid = !availableServings.value;
  isUnableToSaveAvailableServings.value = isAvailableServingsInvalid;
  return isAvailableServingsInvalid;
};

const validateMediaPresence = () => {
  const isAllMediaOptionsEmpty =
    !isImagePresent.value &&
    !isLinkPresent.value &&
    !isVideoPresent.value &&
    !productVideo.value;

  isUnableToSavePlateUp.value = isAllMediaOptionsEmpty;
  return isAllMediaOptionsEmpty;
};

const validateTime = () => {
  if ((getRecipePreparationTime() !== 'PT' || getRecipeCookTime() !== 'PT') && getTotalTime() === 'PT') {
    isUnableToSaveTime.value = true;
    return true;
  }
  return false;
};

const validateIngredients = () => {
  let hasError = false
  let emptyGroupCount = 0
  let checkGroupName = 1

  availableLang.value?.forEach(lang => {
    const ingredients = ingredientsData.value[lang.language]?.children || []
    ingredients?.forEach(data => {
      const nameIsEmpty = !(data?.name?.trim())
      const noChildren = !(data?.children?.length)
      const isSingleIngredient = ingredients?.length === 1
      if ((nameIsEmpty && noChildren && isSingleIngredient) || (!nameIsEmpty && noChildren)) {
        isUnableToSaveIngredients.value = true
        hasError = true
      }
    })
    ingredients?.forEach(data => {
      if (!data?.name?.trim()) {
        emptyGroupCount++
      }
    })
    if (emptyGroupCount > checkGroupName) {
      isUnableToSaveGroupName.value = true
      hasError = true
    }
    checkGroupName++
    ingredients?.forEach(data => {
      data?.children?.forEach(item => {
        if (!item?.name?.trim()) {
          isUnableToSaveStepInstruction.value = true
          hasError = true
        }
      })
    })
  })
  return hasError
};

const validateSteps = () => {
  if (!tasksData.value?.length) {
    isUnableToSaveSteps.value = true
    return true
  }
  const hasEmptyTitles = tasksData.value?.some(step =>
    availableLang.value?.some(lang => !step?.[lang.language]?.title)
  )
  if (hasEmptyTitles) {
    isUnableToSaveTitleSteps.value = true
  }
  return hasEmptyTitles
};

const validateStepInstructions = () => {
  const hasEmptyInstruction = tasksData.value?.some(data =>
    availableLang.value?.some(lang =>
      data?.[lang.language]?.instructions?.some(item => !item.text)
    )
  )
  if (hasEmptyInstruction) {
    isUnableToSaveStepInstruction.value = true
  }
  return hasEmptyInstruction
};

const handlePublishAction = (hasErrors) => {
  if (route?.query?.isin) {
    if (hasErrors || !servings.value || !recipeName.value) {
      isPublishModalVisible.value = false;
      isConfirmScheduleRecipePopupVisible.value = false;
      isErrorOccuredModal.value = true;
    } else {
      publishRecipeClickedAsync()
      isRecipeSaving.value = true;
      isCampaignModified.value = false;
    }
  } else {
    isErrorOccuredModal.value = true;
    isPublishModalVisible.value = false;
    isConfirmScheduleRecipePopupVisible.value = false;
  }
};

const publishRecipeClickedAsync = async () => {
  isRecipeSaving.value = true
  await saveRecipeAsync();
  isCampaignModified.value = false
};

const displayPublishButton = (state) => {
  if (
    (state === "published" || recipeData?.value?.lastPublishDate) &&
    recipeData?.value?.status === "active"
  ) {
    isShowSaveButton.value = true;
    hasShowPublishButton.value = true;
  } else {
    isShowSaveButton.value = false;
    hasShowPublishButton.value = false;
  }
};

const updateQuantityMirror = (ingredient) => {
  if (!taskID.value.includes(ingredient.id)) {
    ingredient.quantityMirror = ingredient.quantity;
  }
};

const updateUOMMirror = (ingredient) => {
  if (!taskID.value.includes(ingredient.id)) {
    ingredient.UOMMirror = ingredient.UOM
  }
};

const updateNameMirror = (ingredient) => {
  if (!taskID.value.includes(ingredient.id)) {
    ingredient.nameMirror = ingredient.name
  }
};

const getRecipeUnitConfig = async (ingredient) => {
  let UOMingredient = ingredient;
  checkInputPresence(ingredient);


  try {
    const params = {
      lang: lang.value,
    };
    await store.dispatch("ingredient/getRecipeUnitConfigAsync", {
      params
    });

    const response = store.getters["ingredient/getRecipeUnit"];

    if (response && response.units) {
      ingredientsUomList.value = response.units.sort((a, b) => {
        if (a?.display && b?.display) {
          return a.display.localeCompare(b.display);
        }
        return 0;
      });
    }
    nutrientUnitsConstants.value =
      response && response.units ? response.units : [];
    if (ingredientsUomList.value.length > 0 && ingredient) {
      ingredient.uomAutocomplete = true;
      ingredientUomAutocompleteArrowCounter.value = -1;
    }
    ingredientsUomList.value.forEach((data, index) => {
      if (data && !data.display) {
        ingredientsUomList.value.splice(index, 1);
      }
    });

    if (UOMingredient) {
      searchUOMList(UOMingredient);
    }

    checkInputPresence(ingredient);
  } catch (error) {
    console.error("Error fetching recipe unit config", error);
  }
};

const setDietResult = (result) => {
  isCampaignModified.value = true
  if (result.isAlreadyInSelectedDiet) {
    result.isAlreadyInSelectedDiet = false
    selectedDiets.value = selectedDiets.value.filter(item => item.key !== result.key)
  } else {
    result.isAlreadyInSelectedDiet = true
    selectedDiets.value.push(result)
  }
};

const setAllergensResult = (result) => {
  isCampaignModified.value = true
  if (result.isAlreadyInSelectedAllergens) {
    result.isAlreadyInSelectedAllergens = false
    selectedAllergens.value = selectedAllergens.value.filter(item => item.key !== result.key)
  } else {
    result.isAlreadyInSelectedAllergens = true
    selectedAllergens.value.push(result)
  }
};

const setResult = (result) => {
  isCampaignModified.value = true
  if (result.isAlreadyInSelectedCategory) {
    result.isAlreadyInSelectedCategory = false
    selectedCategories.value = selectedCategories.value.filter(item => item.isin !== result.isin)
  } else {
    result.isAlreadyInSelectedCategory = true
    selectedCategories.value.push(result)
  }
};

const setTagsResult = (result) => {
  isCampaignModified.value = true
  if (result.isAlreadyInSelectedTag) {
    result.isAlreadyInSelectedTag = false
    selectedTags.value = selectedTags.value.filter(item => item.isin !== result.isin)
  } else {
    result.isAlreadyInSelectedTag = true
    selectedTags.value.push(result)
  }
};

const setIngredientUomResult = (result, ingredient) => {
  if (result.display == ingredient.UOM) {
    return
  }
  ingredient.UOM = result.display
  isCampaignModified.value = true
  selectedUom.value.push(result)
  searchedUomText.value = ""
  ingredient.uomAutocomplete = false
  checkInputPresence(ingredient)
};

const checkInputPresence = (ingredient) => {
  handleCategoryInput()
  handleTagsInput()
  handleDietInput()
  if (ingredient) {
    handleIngredientMatchInput(ingredient)
    handleIngredientUOMInput(ingredient)
  }
};

const handleCategoryInput = () => {
  if (!categoryQuery.value && !clickedLoadMore.value) {
    isCategoryAutocompleteOpen.value = false
    resetScrollPosition(categoriesList.value, "categoryList")
    categoryAutocompleteArrowCounter.value = -1
  }
};

const handleTagsInput = () => {
  if (!tagsQuery.value && !clickedLoadMore.value) {
    isTagsAutocompleteOpen.value = false
    resetScrollPosition(tagsList.value, "tagsList")
    tagsAutocompleteArrowCounter.value = -1
  }
};

const handleDietInput = () => {
  if (!dietQuery.value && !clickedLoadMore.value) {
    isDietAutocompleteOpen.value = false
    resetScrollPosition(dietsList.value, "dietsList")
    dietAutocompleteArrowCounter.value = -1
  }
};

const resetScrollPosition = (list, refName) => {
  if (list?.length) {
    const scroll = getRef(refName)
    if (scroll) {
      scroll.scrollTo(0, 0)
    }
  }
};

const handleIngredientMatchInput = (ingredient) => {
  if (!ingredient.name) {
    isIngredientMatchAutocompleteOpen.value = false
    ingredientMatchAutocompleteArrowCounter.value = -1
  }
};

const handleIngredientUOMInput = (ingredient) => {
  if (!ingredient.UOM) {
    isIngredientUomAutocompleteOpen.value = false
    ingredientUomAutocompleteArrowCounter.value = -1
  }
};

const getRecipeDiets = async () => {
  if (!isDietAutocompleteOpen.value) {
    dietQuery.value = "";
    fromPopUpDiet.value = 0;
  }

  try {
    await store.dispatch("recipe/getRecipeDietsAsync", {
      query: dietQuery.value ? dietQuery.value.toLowerCase() : "",
      recipeCount: false,
      lang: lang.value,
    });

    const response = store.getters["recipe/getRecipeDiets"];

    if (response && response.results) {
      response.results.forEach((data) => {
        data.isAlreadyInSelectedDiet = false;
      });

      if (selectedDiets.value?.length) {
        selectedDiets.value.forEach((data) => {
          response.results.forEach((item) => {
            if (data.id === item.id) {
              item.isAlreadyInSelectedDiet = true;
            }
          });
        });
      }

      dietsList.value = response.results.sort((a, b) =>
        a.name.localeCompare(b.name)
      );

      dietAutocompleteArrowCounter.value = -1;
    }
  } catch (error) {
    console.error("Error fetching recipe diets", error);
  }
};

const getRecipeAllergens = async (dataAllergens) => {
  if (!isAllergensAutocompleteOpen.value) {
    allergensQuery.value = "";
  }

  try {
    await store.dispatch("recipeDetails/getRecipeAllergensAsync", {
      query: allergensQuery.value ? allergensQuery.value.toLowerCase() : "",
      lang: lang.value,
    });

    const response = store.getters["recipeDetails/getRecipeAllergens"];

    if (response && response.results) {
      response.results.forEach((data) => {
        data.isAlreadyInSelectedAllergens = false;
      });

      if (selectedAllergens.value?.length) {
        selectedAllergens.value.forEach((data) => {
          response.results.forEach((item) => {
            if (data.key === item.key) {
              item.isAlreadyInSelectedAllergens = true;
            }
          });
        });
      }

      allergensList.value = response.results.sort((a, b) =>
        a.name.localeCompare(b.name)
      );

      if (
        dataAllergens === "checkAllergens" &&
        allergensList.value?.length > 0
      ) {
        isDisplayAllergens.value = true;
      }

      allergensAutocompleteArrowCounter.value = -1;
    }
  } catch (error) {
    console.error("Error fetching recipe allergens", error);
  }
};

const getRecipeCategories = (data) => {
  if (!isCategoryAutocompleteOpen.value) {
    categoryQuery.value = "";
    fromPopUpCategory.value = 0;
  }
  if (data == "input") {
    fromPopUpCategory.value = 0;
    sizePopUpCategory.value = 10;
  }
  return RecipeService.getRecipeCategories(
    project.value,
    categoryQuery.value ? categoryQuery.value.toLowerCase() : "",
    lang.value,
    fromPopUpCategory.value,
    sizePopUpCategory.value,
    store,
    $auth
  )
    .then((response) => {
      categoriesTotal.value = response.total;
      response.results.forEach((data) => {
        data.isAlreadyInSelectedCategory = false;
      });
      if (selectedCategories.value?.length) {
        selectedCategories.value.forEach((data) => {
          response.results.forEach((item) => {
            if (data.isin == item.isin) {
              item.isAlreadyInSelectedCategory = true;
            }
          });
        });
      }
      if (fromPopUpCategory.value >= 10) {
        return response.results.sort((a, b) =>
          a.data[recipeVariantSelectedLanguage.value].name.localeCompare(
            b.data[recipeVariantSelectedLanguage.value].name
          )
        );
      } else {
        categoriesList.value = response.results.sort((a, b) =>
          a.data[recipeVariantSelectedLanguage.value].name.localeCompare(
            b.data[recipeVariantSelectedLanguage.value].name
          )
        );
      }
      categoryAutocompleteArrowCounter.value = -1;
    })
    .catch(() => {
      showLoader.value = false;
    });
};

const getRecipeTags = (data) => {
  if (!isTagsAutocompleteOpen.value) {
    fromPopUpTags.value = 0;
    tagsQuery.value = "";
  }
  if (data == "input") {
    fromPopUpTags.value = 0;
    sizePopUpTags.value = 10;
  }
  return RecipeService.getRecipeTags(
    project.value,
    tagsQuery.value ? tagsQuery.value.toLowerCase() : "",
    lang.value,
    fromPopUpTags.value,
    sizePopUpTags.value,
    store,
    $auth
  )
    .then((response) => {
      tagsTotal.value = response.total;
      response.results.forEach((data) => {
        data.isAlreadyInSelectedTag = false;
      });
      if (selectedTags.value?.length) {
        selectedTags.value.forEach((data) => {
          response.results.forEach((item) => {
            if (data.isin == item.isin) {
              item.isAlreadyInSelectedTag = true;
            }
          });
        });
      }
      if (fromPopUpTags.value >= 10) {
        return response.results.sort((a, b) =>
          a.data[recipeVariantSelectedLanguage.value].name.localeCompare(
            b.data[recipeVariantSelectedLanguage.value].name
          )
        );
      } else {
        tagsList.value = response.results.sort((a, b) =>
          a.data[recipeVariantSelectedLanguage.value].name.localeCompare(
            b.data[recipeVariantSelectedLanguage.value].name
          )
        );
      }
      tagsAutocompleteArrowCounter.value = -1;
    })
    .catch(() => {
      showLoader.value = false;
    });
};

const dietAutocompleteArrowDown = () => {
  if (dietAutocompleteArrowCounter.value + 1 < dietsList.value?.length) {
    dietAutocompleteArrowCounter.value += 1;
  }
  if (dietsList.value?.length > 0) {
    let scroll = getRef("dietsList");
    if (scroll) {
      scroll.scrollTop += 25;
    }
  }
};

const dietAutocompleteArrowUp = () => {
  if (dietAutocompleteArrowCounter.value > 0) {
    dietAutocompleteArrowCounter.value -= 1;
  }
  if (dietsList.value?.length > 0) {
    let scroll = getRef("dietsList");
    if (scroll) {
      scroll.scrollTop -= 25;
    }
  }
};

const dietAutocompleteEnter = () => {
  if (dietAutocompleteArrowCounter.value >= 0) {
    const result = dietsList.value[dietAutocompleteArrowCounter.value];
    setDietResult(result);
  }
};

const allergensAutocompleteArrowDown = () => {
  if (allergensAutocompleteArrowCounter.value + 1 < allergensList.value?.length) {
    allergensAutocompleteArrowCounter.value += 1;
  }
  const scroll = getRef('allergensList');
  if (scroll) {
    scroll.scrollTop += 25;
  }
};

const allergensAutocompleteArrowUp = () => {
  if (allergensAutocompleteArrowCounter.value > 0) {
    allergensAutocompleteArrowCounter.value -= 1;
  }
  const scroll = getRef('allergensList');
  if (scroll) {
    scroll.scrollTop -= 25;
  }
};

const allergensAutocompleteEnter = () => {
  if (allergensAutocompleteArrowCounter.value >= 0) {
    const result = allergensList.value[allergensAutocompleteArrowCounter.value];
    setAllergensResult(result);
  }
};

const autocompleteArrowDown = () => {
  if (categoryAutocompleteArrowCounter.value + 1 < categoriesList.value?.length) {
    categoryAutocompleteArrowCounter.value += 1;
    const scroll = getRef('categoryList');
    if (scroll) {
      scroll.scrollTop += 33;
    }
  }
};

const autocompleteArrowUp = () => {
  if (categoryAutocompleteArrowCounter.value > 0) {
    categoryAutocompleteArrowCounter.value -= 1;
    const scroll = getRef('categoryList');
    if (scroll) {
      scroll.scrollTop -= 33;
    }
  }
};

const autocompleteEnter = () => {
  if (categoryAutocompleteArrowCounter.value >= 0) {
    const result = categoriesList.value[categoryAutocompleteArrowCounter.value];
    setResult(result);
  }
};

const tagsAutocompleteArrowDown = () => {
  if (tagsAutocompleteArrowCounter.value + 1 < tagsList.value?.length) {
    tagsAutocompleteArrowCounter.value += 1;
    const scroll = getRef('tagsList');
    if (scroll) {
      scroll.scrollTop += 24;
    }
  }
};

const tagsAutocompleteArrowUp = () => {
  if (tagsAutocompleteArrowCounter.value >= 0) {
    tagsAutocompleteArrowCounter.value -= 1;
    const scroll = getRef('tagsList');
    if (scroll) {
      scroll.scrollTop -= 33;
    }
  }
};

const tagsAutocompleteEnter = () => {
  if (tagsAutocompleteArrowCounter.value >= 0) {
    const result = tagsList.value[tagsAutocompleteArrowCounter.value];
    setTagsResult(result);
  }
};

const ingredientUomAutocompleteArrowDown = (ingredient) => {
  if (searchedUomText.value === '') {
    if (ingredientUomAutocompleteArrowCounter.value + 1 < ingredientsUomList.value?.length) {
      ingredientUomAutocompleteArrowCounter.value += 1;
      const scroll = getRef('ingredientsUomList');
      if (scroll) {
        scroll.scrollTop += 33;
      }
    }
    ingredient.UOM = ingredientsUomList.value[ingredientUomAutocompleteArrowCounter.value]?.display;
  }
  if (searchedUomText.value !== '' && searchedUomList.value?.length > 0) {
    if (ingredientUomAutocompleteArrowCounter.value + 1 < searchedUomList.value?.length) {
      ingredientUomAutocompleteArrowCounter.value += 1;
    }
    ingredient.UOM = searchedUomList.value[ingredientUomAutocompleteArrowCounter.value]?.display;
  }
};

const ingredientUomAutocompleteArrowUp = (ingredient) => {
  if (searchedUomText.value === '') {
    if (ingredientUomAutocompleteArrowCounter.value > 0) {
      ingredientUomAutocompleteArrowCounter.value -= 1;
      const scroll = getRef('ingredientsUomList');
      if (scroll) {
        scroll.scrollTop -= 33;
      }
    }
    ingredient.UOM = ingredientsUomList.value[ingredientUomAutocompleteArrowCounter.value]?.display;
  }
  if (searchedUomText.value !== '' && searchedUomList.value?.length > 0) {
    if (ingredientUomAutocompleteArrowCounter.value > 0) {
      ingredientUomAutocompleteArrowCounter.value -= 1;
    }
    ingredient.UOM = searchedUomList.value[ingredientUomAutocompleteArrowCounter.value]?.display;
  }
};

const ingredientUomAutocompleteEnter = (ingredient) => {
  if (ingredientUomAutocompleteArrowCounter.value > 0) {
    if (searchedUomText.value === '') {
      const result = ingredientsUomList.value[ingredientUomAutocompleteArrowCounter.value];
      ingredient.UOM = result?.display;
      selectedUom.value.push(result);
    }
    if (searchedUomText.value !== '') {
      const result = searchedUomList.value[ingredientUomAutocompleteArrowCounter.value];
      ingredient.UOM = result?.display;
      selectedUom.value.push(result);
    }
  }
  ingredientUomAutocompleteArrowCounter.value = -1;
  ingredient.uomAutocomplete = false;
  ingredientUomQuery.value = '';
};

const deleteModalVisible = (modalType, index, data, groupIndex) => {
  toggleDropdownOff();
  deleteIngredientIndex.value = null;
  deleteRecipeStepIndex.value = null;
  groupIndexForIngredient.value = null;
  if (modalType === 'ingredientModal') {
    isDeleteModalVisible.value = true;
    isDeleteIngredient.value = true;
    deleteModalInfoTitle.value = 'Delete Ingredient?';
    deleteModalInfoName.value = 'ingredient?';
    deleteIngredientIndex.value = index;
    groupIndexForIngredient.value = groupIndex;
  } else if (modalType === 'recipeStepModal') {
    isDeleteModalVisible.value = true;
    isDeleteRecipeStep.value = true;
    deleteModalInfoTitle.value = 'Delete Step?';
    deleteModalInfoName.value = 'step?';
    indexOfStep.value = index;
  } else {
    isDeleteModalVisible.value = true;
    deleteModalInfoTitle.value = 'Delete Recipe?';
    deleteModalInfoName.value = 'recipe?';
  }
};

const deleteIngredient = () => {
  isCampaignModified.value = true;
  availableLang.value.forEach((data) => {
    if (
      ingredientsData.value[data.language]?.children
    ) {
      ingredientsData.value[data.language]?.children.forEach(
        (data, groupIndex) => {
          if (groupIndexForIngredient.value === groupIndex) {
            data.children.forEach((item, index) => {
              if (deleteIngredientIndex.value === index) {
                data.children.splice(index, 1);
              }
            });
          }
        }
      );
    }
  });
  isDeleteModalVisible.value = false;
  isDeleteIngredient.value = false;
};

const mainImageSelected = (id) => {
  isImagePresent.value = true;
  isCampaignModified.value = true;
  recipeImageList.value.forEach((data) => {
    data.isMainImage = data.id === id;
  });
  recipeImage.value = recipeImageList.value.find(image => image.isMainImage)?.url || '';
  setRecipePreviewImage();
  checkMediaPresent();
};

const uploadImageFile = async (uploadedFile) => {
  imageUrlLinkUpdate.value = "";
  uploadMediaPopup.value = false;
  isImageLinkPresent.value = false;
  checkMediaPresent();
  if (uploadedFile instanceof FileList && uploadedFile.length) {
    file.value = [uploadedFile[0]];
  } else if (uploadedFile instanceof File) {
    file.value = [uploadedFile];
  } else if (typeof uploadedFile === "string") {
    recipeImage.value = uploadedFile;
    isCampaignModified.value = true;
    return;
  }
  uploadImageSize.value = file.value[0].size;
  const reader = new FileReader();
  reader.addEventListener(
    "load",
    async () => {
      recipeImage.value = reader.result;
      isImageModified.value = true;
      isCampaignModified.value = true;
      if (recipeImage.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
        isImagePresent.value = isImageIsMain.value;
      }
    },
    false
  );

  reader.readAsDataURL(file.value[0]);
};
const uploadImageAsync = async () => {
  if (!route.query.isin && !recipeIsin.value) {
    await getIsins();
  }
  if (file.value?.length > 0 && recipeImage.value) {
    const reader = new FileReader();
    reader.onload = async () => {
      const extension = file.value[0].type.split("/")[1];
      const params = {
        entity: "recipe",
        content: "image",
        lang: lang.value,
        extension: extension,
        public: true,
      };
      if (recipeIsin.value) {
        await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
          isin: recipeIsin.value,
          params,
        });
        const response = store.getters['preSignedUrl/getPreSignedUrl'];
        await uploadImageFilePercentage(response?.data?.url, file.value[0]);
        await uploadImageFile(response?.data?.url);
        await RecipeService.upload(response?.data?.url, file.value[0]);
        imageResponseUrl.value = response?.data?.url;
        const mainImage = !recipeImageList.value?.length;
        recipeImageList.value.unshift({
          url: imageResponseUrl.value.replace(/\?.*/, ""),
          source: $keys.KEY_NAMES.MANUAL_UPLOAD,
          id: generateUUID(),
          isMainImage: mainImage,
          tooltip: showRecipeImageTooltip($keys.KEY_NAMES.MANUAL_UPLOAD),
        });
        recipeImage.value = null;
        imageResponseUrl.value = null;
        setRecipePreviewImage();
      }
    };
    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
    isImageModified.value = false;
  }
};

const uploadImageFilePercentage = (url, file) => {
  cancelImage.value = axios.CancelToken.source();
axios
  .put(url, file, {
    headers: {
      "Content-Type": file.type,
      "x-amz-acl": "public-read",
    },
    cancelToken: cancelImage.value.token,
    onUploadProgress: (progressEvent) => {
      uploadImagePercentage.value = Math.round(
        (progressEvent.loaded / progressEvent.total) * 100
      );
      uploadedImageFunctionAsync(uploadImagePercentage.value);
      loadedImageSize.value = progressEvent.loaded;
    },
  })
  .then(() => {
    // Handle success if needed
  })
  .catch((e) => {
    if (axios.isCancel(e)) {
      console.error("Image request canceled.");
    } else {
      console.error(e);
    }
  });
};

const uploadedImageFunctionAsync = async (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    await delay(2000); // by making the transition to 100% more noticeable
    uploadImagePercentage.value = 100;
  }
};

const uploadVideoFiles = (selectedFile) => {
  isCampaignModified.value = true;
  uploadMediaPopup.value = false;
  if (selectedFile?.length) {
    videoFile.value = selectedFile;
    uploadVideoSize.value = videoFile.value[0]?.size;
    resetMediaSelectionError();
    uploadVideoAsync();
    uploadPercentage.value = 1;
  }
};

const uploadVideoAsync = async () => {
  if (!route.query.isin && !recipeIsin.value) {
    await getIsins();
  }
  if (videoFile.value?.length) {
    isVideoPresent.value = true;
    isVideoModified.value = true;
    videoDimension.value = "";
    const extension = videoFile.value[0].type.split("/")[1];
    const params = {
      entity: "recipe",
      content: "video",
      lang: lang.value,
      extension,
      public: true,
    };
    if (recipeIsin.value) {
      await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
        isin: recipeIsin.value,
        params,
      });
      const response = store.getters['preSignedUrl/getPreSignedUrl'];
      await upload(response?.data?.url, videoFile.value[0]);
      videoResponseUrl.value = response?.data?.url.replace(/\?.*/, "");
      isLinkPresent.value = false;
    }
    isVideoModified.value = false;
  }
};

const openIngredientPopUp = () => {
  isAddIngredientConfirm.value = false;
  isAddIngredientModalVisible.value = true;
  toggleDropdownOff();
};

const upload = ( url, file) => {
  cancelVideo.value = axios.CancelToken.source();

axios
  .put(url, file, {
    headers: {
      "Content-Type": file.type,
      "x-amz-acl": "public-read",
    },
    cancelToken: cancelVideo.value.token,
    onUploadProgress: (progressEvent) => {
      uploadPercentage.value = Math.round(
        (progressEvent.loaded / progressEvent.total) * 100
      );
      uploadedVideoFunction(uploadPercentage.value);
      loadedVideoSize.value = progressEvent.loaded;
    },
  })
  .then((result) => {
    return result;
  })
  .catch((error) => {
    if (axios.isCancel(error)) {
      console.error("Image request canceled.");
    } else if (error.response.status === 503) {
      triggerLoading($keys.KEY_NAMES.IMAGE_UPLOAD_ISSUE);
    } else {
      console.error(error);
    }
  });
};


const uploadedVideoFunction = async (data) => {
  if (data === 100) {
    uploadPercentage.value = 99;
    await delay(2000).then(() => {
      uploadPercentage.value = 100;
      isVideoLoaded.value = false;
      productVideo.value = videoResponseUrl.value;
    });
  }
};

const setVideoDimensions = (data) => {
  const videoActualWidth = data.videoWidth;
  const videoActualHeight = data.videoHeight;

  if (videoActualWidth === 0 || videoActualHeight === 0) {
    hasDisableButtonDueToDimension.value = true;
  } else {
    videoDimension.value = `${videoActualWidth}x${videoActualHeight}`;
    hasDisableButtonDueToDimension.value = false;
  }
};

const addIngredientConfirm = () => {
  if (availableLang.value?.length > 1 && !isDisplayAddIngredientPopup) {
    isAddIngredientModalVisible.value = false;
    isAddIngredientConfirm.value = true;
    isDisplayAddIngredientPopup.value = true;
  } else {
    openIngredientPopUp();
  }
};
const addIngredientRow = () => {
  if (getRef("addButton")) {
    getRef("addButton").className = "disable-add-button";
  }
  toggleDropdownOff();
  newIdForIngredient.value++;
  isCampaignModified.value = true;
  parseRecipeIngredient(ingredientsDataText.value.trim());
};

const shopPreview = () => {
  window.scrollTo(0, 0);
  isShowShopPreview.value = true;
};

const addStepRow = () => {
  isAddStepConfirm.value = false;
  let recipeStepIndex;
  toggleDropdownOff();
  isCampaignModified.value = true;

  const step = {
    isChecked: false,
    isDropDown: false,
    isFirstTime: true,
    [lang.value]: {
      title: "",
      instructions: [],
      ingredients: [],
      media: {
        image: "",
        video: [{ url: "" }],
      },
    },
  };

  tasksData.value.push(step);
  recipeStepIndex = tasksData.value.length - 1;

  let data = [];
  let expandCount = 0;

  tasksData.value.forEach((item) => {
    if (item && item.isChecked) {
      expandCount++;
    }
  });

  isRecipeStepDropDown.value = tasksData.value.length === expandCount;

  data = tasksData.value;
  tasksData.value = [];
  tasksData.value = data;

  editRecipeStep(recipeStepIndex);
};

const addStepConfirm = () => {
  if (availableLang.value.length > 1 && !isShowAddStepsPopup.value) {
    isAddStepConfirm.value = true;
    isShowAddStepsPopup.value = true;
  } else {
    addStepRow();
  }
};

const closeAddConfirmModal = () => {
  isAddGroupConfirm.value = false;
  isAddGroupPopup.value = false;
  isAddIngredientConfirm.value = false;
  isDisplayAddIngredientPopup.value = false;
  isAddStepConfirm.value = false;
  isShowAddStepsPopup.value = false;
};


const closeModal = () => {
  hasDisableButtonDueToDimension.value = false;
  isInvalidImageModalVisible.value = false;
  isInvalidVideoModalVisible.value = false;
  isAddButtonValidURL.value = false;
  isAddButtonValidImageURL.value = false;
  urlLink.value = "";
  imageUrlLink.value = "";
  ingredientsDataText.value = "";
  isDeleteScheduleModalVisible.value = false;
  ingredientsDataNotes.value = "";
  isDeleteImageModal.value = false;
  hostNameUrlLink.value = "";
  hostNameImageUrlLink.value = "";
  isImageLoadingIcon.value = false;
  hasCorrectImageIcon.value = false;
  hasCorrectVideoIcon.value = false;
  isVideoLoadingIcon.value = false;
  isValidURLlink.value = true;
  isValidImageURLlink.value = true;
  uploadLinkText.value = "";
  uploadImageLinkText.value = "";
  ingredientsCount.value = 0;
  isOpenPreviewRecipe.value = false;
  clickedOnPublish.value = "";
  errorMessage.value = "";
  isVideoLinkPopup.value = false;
  isImageLinkPopup.value = false;
  isAddIngredientModalVisible.value = false;
  isDeleteGroupModal.value = false;
  isConfirmModalVisible.value = false;
  isErrorOccuredModal.value = false;
  isPreviewModalVisible.value = false;
  isDeleteModalVisible.value = false;
  isProductMatchesModalVisible.value = false;
  isDeleteRecipeStep.value = false;
  isDeleteIngredient.value = false;
  isPublishModalVisible.value = false;
  isSaveModalVisible.value = false;
  hasOpenVideo.value = false;
  isOpenRecipeStepVideo.value = false;
  isDeleteVideoModal.value = false;
  includedProducts.value = [];
  ingredientMatchIsinData.value = "";
  isDeleteRecipeVariant.value = false;
  hasRecipeVariantLanguagePopup.value = false;
  isAddIngredientConfirm.value = false;
  isAddStepConfirm.value = false;
  isAddGroupConfirm.value = false;
  isEditCategoryVariantName.value = false;
  isEditTagVariantName.value = false;
  isMaxImagePopupVisible.value = false;
  isMaxVideoPopupVisible.value = false;
  recipeVariantLanguage.value = "";
  isDeletingModalVisible.value = false;
  isUnableToLeaveVariant.value = false;
  isShowAddQuantity.value = false;
  isIngredientKeywordsPopupModal.value = false;
  isConfirmScheduleRecipePopupVisible.value = false;
  isScheduleWarningPopupVisible.value = false;
};

const deleteRecipe = async () => {
  deleteScheduleRecipeData();
  isDeleteModalVisible.value = false;
  isCampaignModified.value = false;
  isDeletingModalVisible.value = true;
  try {
    await store.dispatch("recipe/deleteRecipeAsync", { isin: recipeIsin.value });
    $eventBus.emit('campaignModified', isCampaignModified.value);
    router.push('/recipes');
    triggerLoading('newDeletedSuccess');

  } catch (e) {
    console.error(e);
    isDeletingModalVisible.value = false;
    $eventBus.emit('somethingWentWrong');
  }
};

const deleteRecipeStep = () => {
  isDeleteModalVisible.value = false;
  isDeleteRecipeStep.value = false;
  isCampaignModified.value = true;
  tasksData.value.splice(indexOfStep.value, 1);
  deletedInstruction.value.push(deleteInstruction.value);
  let expandCount = 0;
  tasksData.value.forEach((item) => {
    if (item && item.isChecked) {
      expandCount++;
    }
  });
  isRecipeStepDropDown.value = tasksData.value.length === expandCount;
  const data = tasksData.value;
  tasksData.value = [];
  tasksData.value = data;
};

function editRecipeStep(index) {
  window.scrollTo(0, 0);
  toggleDropdownOff();
  stepsIngredientPopUp.value = [];
  let ingredientData = [];
  tasksData.value.map((data) => {
    availableLang.value.forEach((lang) => {
      if (data?.[lang.language]?.instructions?.length) {
        data[lang.language].instructions.forEach((item, idx) => {
          item["instructions_id"] = "instructions_" + idx;
        });
      }
    });
  });
  if (ingredientsData.value?.[recipeVariantSelectedLanguage.value]?.children) {
    ingredientsData.value[recipeVariantSelectedLanguage.value].children.forEach((ingredientList) => {
      if (ingredientList.children.length) {
        ingredientList.children.forEach((child) => {
          ingredientData.push(child);
        });
      }
    });
  }
  ingredientData.forEach((data) => {
    stepsIngredientPopUp.value.push(data);
  });
  indexOfStep.value = index;
  instructionIndex.value = index;
  editInstructionClicked.value = tasksData.value?.[index];
  instructionIdx.value = index + 1;
  displayInstructionsPage.value = true;
}
const removeDiets = (diet, index) => {
  isCampaignModified.value = true;
  dietsList.value.forEach((data) => {
    if (data.key === diet.key) {
      data.isAlreadyInSelectedDiet = false;
    }
  });
  selectedDiets.value.splice(index, 1);
};

const removeAllergens = (allergens, index) => {
  isCampaignModified.value = true;
  allergensList.value.forEach((data) => {
    if (data.key === allergens.key) {
      data.isAlreadyInSelectedAllergens = false;
    }
  });
  selectedAllergens.value.splice(index, 1);
};

const removeCategory = (category, index) => {
  isCampaignModified.value = true;
  categoriesList.value.forEach((data) => {
    if (data.isin === category.isin) {
      data.isAlreadyInSelectedCategory = false;
    }
  });
  selectedCategories.value.splice(index, 1);
};

const removeTag = (tag, index) => {
  isCampaignModified.value = true;
  tagsList.value.forEach((data) => {
    if (tag.isin === data.isin) {
      data.isAlreadyInSelectedTag = false;
    }
  });
  selectedTags.value.splice(index, 1);
};

const removeIngredientKeyword = (groupIndex, index, keyIndex) => {
  isCampaignModified.value = true;
  ingredientsData.value[recipeVariantSelectedLanguage.value].children.forEach(
    (data, gIndex) => {
      if (gIndex === groupIndex) {
        data.children.forEach((item, iIndex) => {
          if (iIndex === index) {
            item.keywords.splice(keyIndex, 1);
          }
        });
      }
    }
  );
  toggleDropdownOff();
};

const ingredientClickDropDown = (groupIndex, index, clickisChecked) => {
  if (
    ingredientsData.value &&
    ingredientsData.value[recipeVariantSelectedLanguage.value]?.children
  ) {
    const children = ingredientsData.value[recipeVariantSelectedLanguage.value].children;

    if (clickisChecked) {
      children.forEach((data, gIndex) => {
        if (gIndex === groupIndex) {
          data.children.forEach((item, iIndex) => {
            if (iIndex === index) {
              item.isDropDown = false;
            }
          });
        }
      });
    } else {
      children.forEach((data, gIndex) => {
        if (gIndex === groupIndex) {
          data.children.forEach((item, iIndex) => {
            if (iIndex === index) {
              item.isDropDown = true;
            }
          });
        }
      });
    }
  }
};

const recipeStepFormDropDown = (stepIndex, clickisChecked) => {
  let expandCount = 0;

  tasksData.value.forEach((item, num) => {
    if (num === stepIndex) {
      item.isChecked = !clickisChecked;
    }
    if (item && item.isChecked) {
      expandCount++;
    }
  });

  isRecipeStepDropDown.value = tasksData.value?.length === expandCount;
};

const saveRecipeButtonAsync = async () => {
  hasSlugCheckConfirm.value = true;

  if (isSlugStatus.value) {
    isSlugInputWarning.value = true;
    await saveRecipeSlugAsync();
  }

  resetRecipeSaveFlags();

  const hasErrorMsg = checkForRecipeErrors();

  if (!hasErrorMsg) {
    isRecipeSaving.value = true;
    await saveRecipeAsync();
  } else {
    handleRecipeSaveError();
  }
};

const resetRecipeSaveFlags = () => {
  hasSlugCheckConfirm.value = false;
  isSaveModalVisible.value = false;
  isConfirmScheduleRecipePopupVisible.value = false;
  isUnableToSaveRecipeName.value = false;
  isUnableToSaveServings.value = false;
  isUnableToSaveAvailableServings.value = false;
  isUnableToSavePlateUp.value = false;
  isUnableToSaveTime.value = false;
  isUnableToSaveIngredients.value = false;
  isUnableToSaveSteps.value = false;
  isUnableToSaveTitleSteps.value = false;
  isUnableToSaveStepInstruction.value = false;
  isUnableToSaveStepIngredientsRawText.value = false;
  isUnableToSaveGroupName.value = false;
};

// Function: check for recipe errors
const checkForRecipeErrors = () => {
  let hasErrorMsg = false;
  let emptyGroupCount = 0;
  let checkGroupName = 1;

  availableLang.value.forEach((lang) => {
    if (!recipeData.value?.title?.[lang.language]) {
      hasErrorMsg = true;
      isUnableToSaveRecipeName.value = true;
    }
  });

  if (!servings.value || servings.value === 0) {
    hasErrorMsg = true;
    isUnableToSaveServings.value = true;
  }

  if (!availableServings.value?.length) {
    hasErrorMsg = true;
    isUnableToSaveAvailableServings.value = true;
  }

  if (isSchedulePublishVisible.value && !isMediaPresent()) {
    hasErrorMsg = true;
    isUnableToSavePlateUp.value = true;
  }

  if (isTimeInvalid()) {
    hasErrorMsg = true;
    isUnableToSaveTime.value = true;
  }

  availableLang.value.forEach((item) => {
    const ingredients = ingredientsData?.value?.[item.language]?.children || [];

    ingredients.forEach((data) => {
      if (isInvalidGroup(data, ingredients?.length === 1)) {
        hasErrorMsg = true;
        isUnableToSaveIngredients.value = true;
      }
      if (!data.name.trim()) {
        emptyGroupCount++;
      }
      if (hasEmptyStepInstruction(data)) {
        hasErrorMsg = true;
        isUnableToSaveStepInstruction.value = true;
      }
    });

    if (emptyGroupCount > checkGroupName) {
      hasErrorMsg = true;
      isUnableToSaveGroupName.value = true;
    }
    checkGroupName++;
  });

  if (!tasksData.value?.length) {
    hasErrorMsg = true;
    isUnableToSaveSteps.value = true;
  } else {
    tasksData.value.forEach((step) => {
      availableLang.value.forEach((lang) => {
        if (!step?.[lang.language]?.title) {
          hasErrorMsg = true;
          isUnableToSaveTitleSteps.value = true;
        }
        if (hasEmptyInstructions(step, lang.language)) {
          hasErrorMsg = true;
          isUnableToSaveStepInstruction.value = true;
        }
      });
    });
  }

  return hasErrorMsg;
};

const isMediaPresent = () => {
  return isImagePresent.value || isImageLinkPresent.value || isLinkPresent.value || isVideoPresent.value || recipeImage.value;
};

const isTimeInvalid = () => {
  return (getRecipePreparationTime() !== "PT" || getRecipeCookTime() !== "PT") && getTotalTime() === "PT";
};

const isInvalidGroup = (data, hasSingleChild) => {
  const isNameNotEmpty = !!data.name.trim();
  const hasNoChildren = !(data.children?.length);
  return (isNameNotEmpty && hasNoChildren) || (!isNameNotEmpty && hasNoChildren && hasSingleChild);
};

const hasEmptyStepInstruction = (data) => {
  return data?.children?.some(item => !item.name.trim());
};

const hasEmptyInstructions = (step, language) => {
  return step?.[language]?.instructions?.some(item => !item.text);
};

const handleRecipeSaveError = () => {
  isErrorOccuredModal.value = true;
  isSaveModalVisible.value = false;
  isConfirmScheduleRecipePopupVisible.value = false;
};

const saveRecipeAsync = async () => {
  if (!slugEdit.value) {
    await checkSlug();
  }
  if (isAddScheduleButtonVisible.value && !isSchedulePublishVisible.value && isScheduledRecipeDeleted.value) {
    deleteScheduleRecipeData();
  }
  if (saveRemovedRecipeVariants.value?.length && route.query.isin) {
    await deleteVariant();
  } else {
    if (isRecipeScheduled.value && publishDate.value !== 0 && endDate.value !== 0) {
      if (dummyPublishDate.value !== publishDate.value || dummyEndDate.value !== endDate.value) {
        await patchScheduleSelectedRecipes(recipeData.value.isin);
      }
    } else if (!isRecipeScheduled.value && publishDate.value !== 0 && endDate.value !== 0) {
      await postRecipeSchedule();
    }

    await postIngredientRawData();
    await saveRecipeDataAsync();
  }
};

const postIngredientRawData = () => {
  let promises = [];

  availableLang.value.forEach((lang) => {
    if (ingredientsData.value?.[lang.language]?.children) {
      ingredientsData.value[lang.language].children.forEach((data) => {
        if (data.children?.length > 0) {
          data.children.forEach((item) => {
            ingredientsUomList.value.forEach((uom) => {
              if (item.UOM === uom.display) {
                item.UOM = uom.key;
              }
            });
          });
        }
      });

      ingredientsData.value[lang.language].children.forEach((data) => {
        if (data.children?.length > 0) {
          data.children.forEach(async (item) => {
            let payload = {
              name: item.name || "",
              note: item.note || "",
              amount: {
                value: item.quantity ? Number(item.quantity) : 0,
                unit: item.UOM || "",
              },
            };
            promises.push(
              RecipeService.postIngredientRawText(
                project.value,
                payload,
                store,
                $auth,
                lang.language
              )
                .then((response) => {
                  if (response) {
                    item.rawText = response.rawText;
                  }
                })
                .catch((e) => {
                  console.error(e);
                })
            );
          });
        }
      });
    }
  });

  if (tasksData.value?.length > 0) {
    tasksData.value.forEach((data) => {
      availableLang.value.forEach((lang) => {
        if (data[lang.language]?.ingredients?.length > 0) {
          data[lang.language].ingredients.forEach((item) => {
            ingredientsUomList.value.forEach((uom) => {
              if (item.UOMMirror === uom.display) {
                item.UOMMirror = uom.key;
              }
            });
          });
        }
      });
    });

    tasksData.value.forEach((data) => {
      availableLang.value.forEach((lang) => {
        if (data[lang.language]?.ingredients?.length > 0) {
          data[lang.language].ingredients.forEach((item) => {
            let payload = {
              name: item.nameMirror || "",
              note: item.note || "",
              amount: {
                value: item.quantityMirror ? Number(item.quantityMirror) : 0,
                unit: item.UOMMirror || "",
              },
            };
            promises.push(
              RecipeService.postIngredientRawText(
                project.value,
                payload,
                store,
                $auth,
                lang.language
              )
                .then((response) => {
                  if (response) {
                    item.rawText = response.rawText;
                  }
                })
                .catch((e) => {
                  console.error(e);
                })
            );
          });
        }
      });
    });
  }

  return Promise.all(promises);
};

const saveRecipeSlugAsync = async () => {
  await getRecipeSlugAsync();
  await checkSlug();
  await setRecipeSlugInput();
};

const setTimer = () => {
  imagesGenerationTimeout.value = setTimeout(() => imagesGenerationAbortTime.value * 1000);
};

const getRecipePayloadAsync = async () => {
  const postNutritionData = getPostNutritionData();
  const payload = buildPayload(postNutritionData);
  cleanUpPayload(payload, postNutritionData);
  return await returnFilterPayloadAsync(payload);
};

const getPostNutritionData = () => {
  let postNutritionData = {};
  if (nutritionKey.value) {
    handleNutritionData();
    postNutritionData = buildPostNutritionData();
  }
  return postNutritionData;
};

const handleNutritionData = () => {
  if (nutritionKey.value.perServing) {
    perServingNutrientData.value = formatNutritionData(nutrientTableData.value?.['perServing'], "perServing");
  }
  if (nutritionKey.value.per100g) {
    per100gNutrientData.value = formatNutritionData(nutrientTableData.value?.['per100g'], "per100g");
  }
  if (!nutritionKey.value.per100g || !nutritionKey.value.perServing) {
    addRecipeNutrientData.value = formatNutritionData(
      nutrientTableData.value?.[addRecipeNutrientType.value],
      addRecipeNutrientType.value
    );
  }
};

const buildPostNutritionData = () => {
  if (!nutritionKey.value.per100g && !nutritionKey.value.perServing) {
    return {
      [lang.value]: {
        [addRecipeNutrientType.value]: addRecipeNutrientData.value?.length ? addRecipeNutrientData.value : "",
      },
    };
  } else {
    return {
      [lang.value]: {
        per100g: per100gNutrientData.value || [],
        perServing: perServingNutrientData.value || [],
      },
    };
  }
};

const buildPayload = (postNutritionData) => {
  return {
    boostInSearch: isBoostInSearch.value,
    labels: getSelectedAllergens(),
    price: getPrice(),
    isin: recipeIsin.value,
    provider: recipeData.value.provider || "cmsManual",
    title: {},
    subtitle: {},
    ingredients: {},
    tasks: {},
    slug: {},
    time: getTimeData(),
    externalId: [recipeAttributionExternalId.value] || [],
    description: {},
    nutrientTable: postNutritionData,
    media: getMediaData(),
    yield: getYield(),
    attribution: getAttribution(),
    diets: getSelectedDiets(),
    categories: getCategoriesData(),
    tags: getTagsData(),
    langs: getLangs(),
    countries: getCountries(),
  };
};

const getSelectedAllergens = () => {
  return selectedAllergens.value?.map(allergen => allergen.key) || [];
};

const getPrice = () => {
  return {
    currency: selectedCurrency.value?.trim() || "usd",
    value: recipePrice.value >= 0 ? Number(recipePrice.value) : "",
  };
};

const getTimeData = () => {
  return {
    total: getTotalTime(),
    prep: getRecipePreparationTime(),
    cook: getRecipeCookTime(),
  };
};

const getAttribution = () => ({
  [lang.value]: {
    author: {
      name: recipeAttributionAuthor.value?.trim() || "",
    },
    organization: recipeAttributionOrganization.value?.trim() || "",
  },
});

function getMediaData() {
      return {
        [lang.value]: {
          image: imageResponseUrl.value?.replace(/\?.*/, '') || recipeImage.value,
          video: [
            {
              url: videoResponseUrl.value?.replace(/\?.*/, '') || productVideo.value,
              size: videoDimension.value || '',
            },
          ],
          externalVideoUrl: urlLinkUpdate.value || '',
          externalImageUrl: imageUrlLinkUpdate.value || '',
          thumbnailImageUrl: imageUrlThumbnailUpdate.value || '',
        },
      };
    }

    function getYield() {
      return {
        availableServings: availableServings.value,
        serving: Number(servings.value),
        raw: yieldData.value.trim() || '',
        servingSize: nutritionServingSize.value.trim(),
        servingsPerContainer: nutritionServingSizePerContainer.value.trim(),
      };
    }
const getSelectedDiets = () => selectedDiets.value?.map(diet => diet.key) || [];

const getCategoriesData = () => ({
  [lang.value]: selectedCategories.value?.map(category => category.isin) || [],
});

const getTagsData = () => ({
  [lang.value]: selectedTags.value?.map(tag => tag.isin) || [],
});

const getLangs = () => availableLang.value.map(data => data.language).filter(Boolean);

const getCountries = () => availableLang.value.map(data => data.language?.split("-")[1]).filter(Boolean);


const cleanUpPayload = (payload, postNutritionData) => {
  if (!postNutritionData[lang.value]?.perServing || !postNutritionData[lang.value]?.per100g) {
    delete payload.nutrientTable;
  }
};

const returnFilterPayloadAsync = async (payload) => {
  const filteredPayload = await setPayLoadWithVariantAsync(payload, true);

  if (filteredPayload?.tasks) {
    filteredPayload.tasks = filteredPayload.tasks.map((data) => {
      const { isFirstTime, isChecked, isDropDown, ...filteredData } = data;
      return filteredData;
    });
  }
  return filteredPayload;
};

const saveRecipeSimpleAsync = async () => {
  store.dispatch('recipeDetails/resetImageList');
  buttonStateBeforeImageGeneration.value = {
    isCampaignModified: isCampaignModified.value,
    hasShowPublishButton: hasShowPublishButton.value,
    isShowSaveButton: isShowSaveButton.value
  };
  isRecipeImageGenerating.value = true;
  isCampaignModified.value = true;

  const payload = await getRecipePayloadAsync();
  try {
    await store.dispatch("recipeDetails/getRecipeSimpleDataAsync", { payload });
    const response = store.getters['recipeDetails/getRecipeSimplifyData'];

    if (response) getRecipeGenerationImagesAsync(response);
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN, error);
    simpleRecipeError();
  }
};

const simpleRecipeError = () => {
  triggerLoading($keys.KEY_NAMES.IMAGE_GENERATION_FAILED);
  isRecipeImageGenerating.value = false;
  if (buttonStateBeforeImageGeneration.value) {
    restoreButtonStateAfterImageGeneration();
  } else {
    isCampaignModified.value = false;
  }
  closeRecipeImageGeneratorPopup();
};

const getRecipeGenerationImagesAsync = async (recipe) => {
  const imagesGenerationCtrl = new AbortController();

  const onMessage = (event) => {
    clearTimeout(imagesGenerationTimeout);
    setTimer();

    const { type, step, result, isData } = parseEventData(event.data);
    if (!isData) return;

    if (type !== CONTENT_GENERATION_TYPE.RESULT) {
      return;
    }

    if (step === CONTENT_GENERATION_STEP.GENERATE_RECIPE_IMAGES) {
      store.dispatch('recipeDetails/setImageList', {
        imageList: result?.model?.images,
      });
    }
  };

  const onClose = () => {
    isRecipeImageGenerating.value = false;
    isCampaignModified.value = false;
  };

  const onError = (error) => {
    if (buttonStateBeforeImageGeneration.value) {
      restoreButtonStateAfterImageGeneration();
    } else {
      isCampaignModified.value = false;
    }
    triggerLoading($keys.KEY_NAMES.IMAGE_GENERATION_FAILED);
    isRecipeImageGenerating.value = false;
    closeRecipeImageGeneratorPopup();
    console.error(error);
  };

  const onOpen = async () => {
    isRecipeImageGenerating.value = true;
  };

  await AIService.getGenerationStreamAsync(
    store,
    $auth,
    { recipe },
    onOpen,
    onMessage,
    onClose,
    onError,
    imagesGenerationCtrl,
    "imageGeneration"
  );
};

const addSelectedGeneratedRecipeImageAsync = async (imageList) => {
  const uploadedImages = await uploadImagesAsync(imageList);
  const newRecipeImages = uploadedImages?.map((selectedImage) => ({
    url: selectedImage.url,
    source: $keys.KEY_NAMES.AI_GENERATED,
    id: generateUUID(),
    isMainImage: false,
    tooltip: showRecipeImageTooltip($keys.KEY_NAMES.AI_GENERATED),
  }));
  recipeImageList.value.unshift(...newRecipeImages);
  isCampaignModified.value = true;
  buttonStateBeforeImageGeneration.value = null;
  closeRecipeImageGeneratorPopup();
  checkMediaPresent();
};

const uploadImagesAsync = async (imageList) => {
  const urls = imageList.map(image => image.key);
  const payload = {
    isin: recipeIsin.value,
    externalUrls: urls,
    entityType: $keys.KEY_NAMES.RECIPE,
    contentType: $keys.KEY_NAMES.IMAGE,
    extension: $keys.KEY_NAMES.JPEG,
  };

  try {
    await store.dispatch("recipeDetails/batchUploadImagesAsync", { payload });
    return store.getters["recipeDetails/getBatchUploadImagesList"].results;
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "uploadImagesAsync", error);
  }
};

const formatNutritionData = (nutritionData, type) => {
  let tempNutritionData = [];
  if (Array.isArray(nutritionData)) {
    nutritionData.forEach((item) => {
      if (!item.isHeader && (item.valueAmount !== "" || item.dvpValue !== "")) {
        let localNutTable = {
          name: item.itemName,
          amount: {
            value: item.valueAmount !== "" ? Number(item.valueAmount) : "",
            unit: item.unitKey,
          },
        };

        if (item.hasDvp && item.dvpValue !== "") {
          localNutTable.dvp = { value: Number(item.dvpValue) };
        }
        tempNutritionData.push(localNutTable);
      }
    });
  }
  if (Array.isArray(nutritionDataList)) {
    nutritionDataList.forEach((data) => {
      tempNutritionData.forEach((item) => {
        if (data.key === item.name && item.amount && item.amount.unit === "") {
          item.amount.unit = data.unit.key;
        }
      });
    });
  }
  if (recipeData.value?.nutrientTable?.[lang.value]?.[type]) {
    recipeData.value.nutrientTable[lang.value][type].forEach((data) => {
      tempNutritionData.forEach((item) => {
        if (item.name === data.name) {
          if (data.dvp && data.dvp.value === 0 && item.dvp && item.dvp.value === 0) {
            item.dvp.value = 0;
          } else if (!data.dvp && item.dvp && item.dvp.value === 0) {
            delete item.dvp;
          }
          if (item.amount && item.amount.value === "" && item.amount.value !== 0) {
            delete item.amount;
          }
        }
      });
    });
  }
  return tempNutritionData;
};

const recipeRecentActivityDataAsync = async () => {
  const initialPayload = {
    type: $keys.RECENT_ACTIVITIES.ADD_RECIPES,
    data: {
      recipeIsin: recipeIsin.value,
      recipeName: recipeData.value.title[lang.value],
      recipeImage: recipeImage.value || imageUrlLinkUpdate.value,
    },
  };

  try {
    await store.dispatch("recipe/postRecentActivityAsync", {
      lang: lang.value,
      payload: initialPayload,
    });
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "postRecipeExportActivityAsync:", error);
  }
};


const setLanguageVariant = (variantList) => {
  let copyObjectData = [];
  if (availableLang.value && availableLang.value?.length > 0 && variantList && variantList[defaultLang.value]) {
    availableLang.value.forEach((item) => {
      if (item && item.language) {
        let object = {
          [item.language]: variantList[defaultLang.value],
        };
        copyObjectData.push(object);
      }
    });
  }
  return Object.assign({}, ...copyObjectData);
};

const setLanguageVariantTitle = () => {
  let copyObjectData = [];
  if (availableLang.value && availableLang.value?.length > 0) {
    availableLang.value.forEach((item) => {
      if (item && item.language) {
        if (recipeData.value && recipeData.value.title && recipeData.value.title[item.language]) {
          let object = {
            [item.language]: recipeData.value.title[item.language],
          };
          copyObjectData.push(object);
        }
      }
    });
  }
  return Object.assign({}, ...copyObjectData);
};

const setLanguageVariantSlug = () => {
  let copyObjectData = [];
  if (availableLang.value && availableLang.value?.length > 0) {
    availableLang.value.forEach((item) => {
      if (item && item.language) {
        if (slugData?.value?.[item.language] || slugEdit.value) {
          let object = {
            [item.language]: slugData.value[item.language].slugName || slugEdit.value,
          };
          copyObjectData.push(object);
        }
      }
    });
  }
  return Object.assign({}, ...copyObjectData);
};

const setLanguageVariantSubtitle = () => {
  let copyObjectData = [];
  if (availableLang.value && availableLang.value?.length > 0) {
    availableLang.value.forEach((item) => {
      if (item && item.language) {
        if (recipeData.value && recipeData.value.subtitle && recipeData.value.subtitle[item.language]) {
          let object = {
            [item.language]: recipeData.value.subtitle[item.language],
          };
          copyObjectData.push(object);
        }
      }
    });
  }
  return Object.assign({}, ...copyObjectData);
};

const setLanguageVariantDescription = () => {
  let copyObjectData = [];
  if (availableLang.value && availableLang.value?.length > 0) {
    availableLang.value.forEach((item) => {
      if (item && item.language) {
        let object = {
          [item.language]: {
            abstract:
              recipeData.value &&
              recipeData.value.description &&
              recipeData.value.description[item.language] &&
              recipeData.value.description[item.language].abstract
                ? recipeData.value.description[item.language].abstract.trim()
                : '',
            notes:
              recipeData.value &&
              recipeData.value.description &&
              recipeData.value.description[item.language] &&
              recipeData.value.description[item.language].notes
                ? recipeData.value.description[item.language].notes.trim()
                : '',
          },
        };

        let objectCheck = false;
        if (
          object[item.language] === '' ||
          object[item.language].abstract === '' ||
          object[item.language].notes === ''
        ) {
          if (
            object[item.language].notes === '' &&
            object[item.language].abstract === ''
          ) {
            objectCheck = true;
            delete object[item.language];
          }
          if (
            object &&
            object[item.language] &&
            object[item.language].abstract === ''
          ) {
            delete object[item.language].abstract;
          }
          if (
            object &&
            object[item.language] &&
            object[item.language].notes === ''
          ) {
            delete object[item.language].notes;
          }
        }
        if (!objectCheck) {
          copyObjectData.push(object);
        }
      }
    });
  }
  return Object.assign({}, ...copyObjectData);
};

const setLanguageVariantIngredients = () => {
  let updatedData = [];
  let pay = [];
  let copyObjectData = [];

  availableLang.value.forEach((lang) => {
    if (
      ingredientsData.value &&
      ingredientsData.value[lang.language] &&
      ingredientsData.value[lang.language].children
    ) {
      ingredientsData.value[lang.language].children.forEach((data) => {
        if (data.children?.length && data.children?.length > 0) {
          data.children.forEach((item) => {
            ingredientsUomList.value.forEach((uom) => {
              if (item.UOM === uom.display) {
                item.UOM = uom.key;
              }
            });
          });
        }
      });

      ingredientsData.value[lang.language].children.forEach((item) => {
        if (item.children) {
          let groupName = item.name;
          item.children.forEach((data) => {
            data.group = groupName;
            pay.push(data);
          });
        }
      });
    }

    updatedData = pay.map((data) => ({
      amount: {
        value: data.quantity ? Number(data.quantity) : 0,
        unit: data.UOM ? data.UOM : '',
        weightInGrams: data.weightInGrams ? Number(data.weightInGrams) : 0,
        volumeInMl: data.volumeInMl ? Number(data.volumeInMl) : 0,
      },
      name: data.name && data.name.trim() ? data.name.trim() : '',
      foodItem: data.foodItem ? data.foodItem : '',
      rawText: data.rawText && data.rawText.trim() ? data.rawText.trim() : '',
      group: data.group && data.group.trim() ? data.group.trim() : '',
      excludeFromNutrition: data.excludeFromNutrition ? data.excludeFromNutrition : false,
      level: data.level ? data.level : 'main',
      modifier: data.modifier ? data.modifier : '',
      note: data.note && data.note.trim() ? data.note.trim() : '',
      remark: data.remark && data.remark.trim() ? data.remark.trim() : '',
      keywords: data.keywords ? data.keywords : [],
      externalId: data.externalId ? data.externalId : '',
      productId: data.productId ? data.productId : '',
    })) || [];

    if (recipeData.value.provider === 'fims') {
      if (updatedData?.length) {
        updatedData.forEach((data) => {
          delete data.name;
          delete data.rawText;
          if (data && data.amount && data.amount.volumeInMl === 0) {
            delete data.amount.volumeInMl;
          }
          if (data && data.amount && data.amount.weightInGrams === 0) {
            delete data.amount.weightInGrams;
          }
          if (data.modifier === '') {
            delete data.modifier;
          }
          if (data.note === '') {
            delete data.note;
          }
          if (data.remark === '') {
            delete data.remark;
          }
          if (data && data.keywords && data.keywords?.length === 0) {
            delete data.keywords;
          }
          if (data && data.externalId === '') {
            delete data.externalId;
          }
          if (data && data.productId === '') {
            delete data.productId;
          }
        });
      }
    } else if (updatedData?.length) {
      updatedData.forEach((data) => {
        delete data.foodItem;
        delete data.modifier;
        if (data && data.amount && data.amount.volumeInMl === 0) {
          delete data.amount.volumeInMl;
        }
        if (data && data.amount && data.amount.weightInGrams === 0) {
          delete data.amount.weightInGrams;
        }
        if (data && data.keywords && data.keywords?.length === 0) {
          delete data.keywords;
        }
        if (data && data.externalId === '') {
          delete data.externalId;
        }
        if (data && data.productId === '') {
          delete data.productId;
        }
      });
    }

    if (updatedData?.length) {
      updatedData.forEach((data) => {
        if (data.group === '') {
          delete data.group;
        }
        if (data.rawText === '') {
          delete data.rawText;
        }
        if (data.note === '') {
          delete data.note;
        }
        if (data.remark === '') {
          delete data.remark;
        }
      });
    }

    pay = [];
    let object = {
      [lang.language]: updatedData,
    };
    copyObjectData.push(object);
  });

  return Object.assign({}, ...copyObjectData);
};
const setLanguageVariantRecipeStep = (isRecipeImageGeneratorPayload) => {
  let stepData = tasksData.value;
  if (stepData?.length) {
    if (!isRecipeImageGeneratorPayload) {
      cleanIngredients(stepData);
      cleanStepData(stepData);
    }
    processProviderSpecificData(stepData);
  }
  return stepData;
};

const cleanIngredients = (stepData) => {
  stepData.forEach((data) => {
    availableLang.value.forEach((lang) => {
      const ingredients = data?.[lang.language]?.ingredients;
      if (data?.[lang.language]?.duplicateIngredients) {
        delete data[lang.language].duplicateIngredients;
      }
      if (ingredients?.length) {
        cleanIngredientItems(ingredients);
      }
    });
  });
};

const cleanIngredientItems = (ingredients) => {
  ingredients.forEach((item) => {
    removeUnnecessaryProperties(item);
    replaceUOMMirror(item);
  });
};

const removeUnnecessaryProperties = (item) => {
  delete item.isChecked;
  delete item.isDropDown;
  delete item.id;
};

const replaceUOMMirror = (item) => {
  ingredientsUomList.value.forEach((uom) => {
    if (item.UOMMirror === uom.display) {
      item.UOMMirror = uom.key;
    }
  });
};

const cleanStepData = (stepData) => {
  stepData.forEach((data) => {
    cleanDataAttributes(data);
    cleanMedia(data);
    formatIngredients(data);
  });
};

const cleanDataAttributes = (data) => {
  delete data.isChecked;
  delete data.isDropDown;
  delete data.isFirstTime;
};

const cleanMedia = (data) => {
  availableLang.value.forEach((lang) => {
    if (!data?.[lang.language]?.media?.image && !data?.[lang.language]?.media?.video?.[0]?.url) {
      delete data[lang.language].media;
    } else if (!data?.[lang.language]?.media?.image) {
      delete data[lang.language].media.image;
    } else if (!data?.[lang.language]?.media?.video?.[0]?.url) {
      delete data[lang.language].media.video;
    }
  });
};

const formatIngredients = (data) => {
  availableLang.value.forEach((lang) => {
    if (data?.[lang.language]?.ingredients?.length) {
      data[lang.language]["ingredients"] = data[lang.language].ingredients.map((item) => ({
        amount: {
          value: Number(item?.quantityMirror) || item?.amount?.value || 0,
          unit: item?.UOMMirror || item?.amount?.unit || "",
        },
        name: (item?.nameMirror ?? item?.name)?.trim() || "",
        foodItem: item?.foodItem ?? "",
        rawText: item?.rawText?.trim() ?? "",
        excludeFromNutrition: item?.excludeFromNutrition ?? false,
        level: item?.level ?? "main",
        modifier: item?.modifier ?? "",
        note: item?.note?.trim() ?? "",
        remark: item?.remark?.trim() ?? "",
        keywords: item?.keywords ?? [],
      }));
    } else {
      data[lang.language]["ingredients"] = [];
    }
    cleanEmptyIngredientFields(data, lang);
  });
};

const cleanEmptyIngredientFields = (data, lang) => {
  if (data?.[lang.language]?.ingredients?.length) {
    data[lang.language].ingredients.forEach((item) => {
      if (!item?.note) {
        delete item.note;
      }
      if (!item?.remark) {
        delete item.remark;
      }
      if (!item?.keywords?.length) {
        delete item.keywords;
      }
    });
  }
};

const processProviderSpecificData = (stepData) => {
  if (recipeData.value?.provider === "fims") {
    cleanFimsProviderData(stepData);
  } else {
    cleanOtherProvidersData(stepData);
  }
};

const cleanFimsProviderData = (stepData) => {
  if (stepData) {
    stepData.forEach((data) => {
      availableLang.value.forEach((lang) => {
        data?.[lang.language]?.ingredients?.forEach((ing) => {
          delete ing.rawText;
          delete ing.name;
          if (!ing?.modifier) {
            delete ing.modifier;
          }
        });
      });
    });
    stepData.forEach((data) => {
      availableLang.value.forEach((lang) => {
        data[lang.language].ingredients.forEach((ing, index) => {
          if (!ing?.foodItem) {
            data[lang.language].ingredients.splice(index, 1);
          }
        });
      });
    });
    stepData.forEach((data) => {
      availableLang.value.forEach((lang) => {
        data?.[lang.language]?.instructions?.forEach((ins, insIndex) => {
          if (!ins?.text) {
            data?.[lang.language]?.instructions?.splice(insIndex, 1);
          }
          if (!ins?.times) {
            delete ins.times;
          }
          delete ins?.instructions_id;
        });
      });
    });
  }
};

const cleanOtherProvidersData = (stepData) => {
  if (stepData) {
    stepData.forEach((data) => {
      availableLang.value.forEach((lang) => {
        const ingredients = data[lang.language]?.ingredients;
        const instructions = data[lang.language]?.instructions;
        if (ingredients?.length) {
          ingredients.forEach((ing) => {
            if (!ing?.rawText) delete ing.rawText;
            delete ing.foodItem;
            delete ing.modifier;
          });
          data[lang.language].ingredients = ingredients.filter((ing) => !ing.foodItem);
        } else {
          data[lang.language].ingredients = [];
        }
        if (instructions?.length) {
          data[lang.language].instructions = instructions.filter((ins) => {
            if (!ins?.text) return false;
            if (!ins?.times?.length) delete ins.times;
            if (ins?.instructions_id) delete ins.instructions_id;
            return true;
          });
        } else {
          data[lang.language].instructions = [];
        }
      });
    });
  }
};
const setPayLoadWithVariantAsync = async (payload, isRecipeImageGeneratorPayload = false) => {
  payload.title = await setLanguageVariantTitle();
  payload.subtitle = await setLanguageVariantSubtitle();
  payload.description = await setLanguageVariantDescription();
  payload.ingredients = await setLanguageVariantIngredients();
  payload.tasks = await setLanguageVariantRecipeStep(isRecipeImageGeneratorPayload);
  payload.slug = await setLanguageVariantSlug();

  if (payload.media && payload.media[lang.value]) {
    payload.media = await setLanguageVariant(payload.media);
  }
  if (payload.categories && payload.categories[lang.value]) {
    payload.categories = await setLanguageVariant(payload.categories);
  }
  if (payload.tags && payload.tags[lang.value]) {
    payload.tags = await setLanguageVariant(payload.tags);
  }
  if (payload.nutrientTable && payload.nutrientTable[lang.value]) {
    payload.nutrientTable = await setLanguageVariant(payload.nutrientTable);
  }
  return payload;
};

const saveRecipeCampaigns = () => {
  let campaignsToCreate = [];

  if (ingredientsData?.value?.[lang.value]?.children) {
    const getCampaignData = (item) => {
      return item.campaignData || {};
    };

    ingredientsData.value[lang.value]?.children?.forEach((data) => {
      data.children?.forEach((item) => {
        if (item.hasOverridingCampaign) {
          const campaignData = getCampaignData(item);
          const payload = {
            recipe: recipeID.value,
            ingredient: item.name,
            promotedProducts: campaignData.promotedProducts || [],
            hasLabels: campaignData.hasLabels,
            filteredProducts: campaignData.filteredProducts || [],
            includedProducts: campaignData.includedProducts || [],
            onlyPromoted: campaignData.onlyPromoted || false,
            onlyIncluded: campaignData.onlyIncluded || false,
            shoppableFlag: campaignData.shoppableFlag || $keys.KEY_NAMES.SHOPPABLE,
          };
          campaignsToCreate.push(payload);
        }
      });
    });
  }

 const createCampaignPromises = campaignsToCreate.map((payload) => {
  const params = { email: $auth.user?.value?.email || "", applyToRecipeCampaigns: true, };
  const clonedParams = params;
  const clonedPayload = payload;
  return store
    .dispatch("ingredient/saveIngredientCampaignDataAsync", { params: clonedParams, payload: clonedPayload })
    .then(() => {
      closeModal();
    })
    .catch((e) => {
      console.error(e);
    });
  });
return Promise.all(createCampaignPromises);
};

const saveRouter = () => {
  routeToPage("recipes");
  if (recipeButtonPublishState === "Schedule") {
    triggerLoading("scheduledSuccess", 1);
    return;
  }
  const action = isShowSaveButton.value ? "isPublishedData" : "savedSuccess";
  triggerLoading(action);
};

const getTotalTime = () => {
  let totalTime = `PT${hour.value ? hour.value + 'H' : ''}${minute.value ? minute.value + 'M' : ''}${second.value ? second.value + 'S' : ''}`;
  return totalTime;
};

const getRecipePreparationTime = () => {
  let prepTime = `PT${prepHour.value ? prepHour.value + 'H' : ''}${prepMinute.value ? prepMinute.value + 'M' : ''}${prepSecond.value ? prepSecond.value + 'S' : ''}`;
  return prepTime;
};

const getRecipeCookTime = () => {
  let cookTime = `PT${cookHour.value ? cookHour.value + 'H' : ''}${cookMinute.value ? cookMinute.value + 'M' : ''}${cookSecond.value ? cookSecond.value + 'S' : ''}`;
  return cookTime;
};

const toggleDropdownUOM = (section, index, ingredient, groupIndex) => {
  if (section === "ingredientUom") {
    searchedUomText.value = "";
    if (ingredientsUomList.value?.length > 0) {
      ingredient.uomAutocomplete = true;
      isIingredientUomAutocompleteOpen.value = true;
      resetAllDropdowns();
      availableLang.value.forEach((lang) => {
        if (ingredientsData.value?.[lang.language]?.children) {
          ingredientsData.value[lang.language].children.forEach((item, gIdx) => {
            if (item.children && gIdx === groupIndex) {
              item.children.forEach((data) => {
                if (item.children.indexOf(data) !== index) {
                  data.uomAutocomplete = false;
                }
              });
            } else if (item.children && gIdx !== groupIndex) {
              item.children.forEach((data) => {
                data.uomAutocomplete = false;
              });
            }
          });
        }
      });
      const tempData = { ...ingredientsData.value };
      ingredientsData.value = {};
      ingredientsData.value = tempData;
      getRef(`ingredientUom${groupIndex}${index}`).focus();
    }
  }
};

const toggleDropdownOff = () => {
  availableLang?.value?.forEach((lang) => {
    if (isLanguageDataAvailable(lang.language)) {
      processChildren(ingredientsData[lang.language]?.children);
    }
  });
};

const listMap = {
  tagsList: { state: isTagsAutocompleteOpen, query: tagsQuery, list: tagsList.value },
  categoryList: { state: isCategoryAutocompleteOpen, query: categoryQuery, list: categoriesList.value },
  dietsList: { state: isDietAutocompleteOpen, query: dietQuery, list: dietsList.value },
  allergensList: { state: isAllergensAutocompleteOpen, query: allergensQuery, list: allergensList.value },
};

const scrollListToTop = (refName) => {
  nextTick(() => {
    const refElement = getRef(refName);
    if (refElement) {
      refElement?.scrollTo(0, 0);
    }
  });
};

const resetDropdown = (refName) => {
  const item = listMap[refName];
  if (item) {
    item.state.value = false;
    item.query.value = "";
    scrollListToTop(refName);
  }
};

const resetAllDropdowns = () => {
  Object.keys(listMap).forEach(resetDropdown);
};

const isLanguageDataAvailable = (language) => {
  return !!ingredientsData?.[language]?.children;
};

const processChildren = (children) => {
  children?.forEach((data) => {
    if (data?.children) {
      processIngredients(data.children);
    }
  });
};

const processIngredients = (ingredients) => {
  ingredients?.forEach((ingredient) => {
    ingredient.uomAutocomplete = false;
    updateUOM(ingredient);
  });
};

const updateUOM = (ingredient) => {
  if (!searchedUomText.value) {
    return;
  }
  const isUOMValid = ingredientsUomList?.some(e => e.display === ingredient.UOM);
  if (!isUOMValid) {
    ingredient.UOM = "";
  }
};

const toggleDropdownAsync = async (section, inputClick, index, ingredient, groupIndex) => {
  if (ingredient) {
    uomIngredient.value = ingredient;
  }

  if (section === "category") {
    let scrolling = false;
    let listElm = document.querySelector("#categoryList");
    listElm.addEventListener("scroll", () => {
      scrolling = true;
      setInterval(() => {
        if (scrolling) {
          scrolling = false;
          if (
            listElm.offsetHeight + listElm.scrollTop + 1 >=
            listElm.scrollHeight
          ) {
            clickCategoriesLoadMoreAsync("clickedLoadMore");
          }
        }
      }, 1500);
    });
    await getRecipeCategories();
    if (inputClick) {
      isCategoryAutocompleteOpen.value = true;
    } else {
      isCategoryAutocompleteOpen.value = !isCategoryAutocompleteOpen.value;
    }
    if (!isCategoryAutocompleteOpen.value) {
      await getRecipeCategories();
    }
    scrollListToTop("categoryList");
    availableLang.value.forEach((lang) => {
      if (
        ingredientsData.value &&
        ingredientsData.value[lang.language] &&
        ingredientsData.value[lang.language].children
      ) {
        ingredientsData.value[lang.language].children.forEach((data) => {
          if (data.children) {
            data.children.forEach((ingredient) => {
              ingredient.uomAutocomplete = false;
            });
          }
        });
      }
    });
    if (isCategoryAutocompleteOpen.value) {
      getRef("categoryInput").focus();
    }
  } else if (section === "diet") {
    await getRecipeDiets();
    if (inputClick) {
      isDietAutocompleteOpen.value = true;
    } else {
      isDietAutocompleteOpen.value = !isDietAutocompleteOpen.value;
    }
    if (!isDietAutocompleteOpen.value) {
      await getRecipeDiets();
    }
    scrollListToTop("dietsList");
    availableLang.value.forEach((lang) => {
      if (
        ingredientsData.value &&
        ingredientsData.value[lang.language] &&
        ingredientsData.value[lang.language].children
      ) {
        ingredientsData.value[lang.language].children.forEach((data) => {
          if (data.children) {
            data.children.forEach((ingredient) => {
              ingredient.uomAutocomplete = false;
            });
          }
        });
      }
    });
    if (isDietAutocompleteOpen.value) {
      getRef("dietInput").focus();
    }
  } else if (section === "allergens") {
    await getRecipeAllergens("");
    if (inputClick) {
      isAllergensAutocompleteOpen.value = true;
    } else {
      isAllergensAutocompleteOpen.value = !isAllergensAutocompleteOpen.value;
    }
    if (!isAllergensAutocompleteOpen.value) {
      await getRecipeAllergens("");
    }
    scrollListToTop("allergensList");
    availableLang.value.forEach((lang) => {
      if (
        ingredientsData.value &&
        ingredientsData.value[lang.language] &&
        ingredientsData.value[lang.language].children
      ) {
        ingredientsData.value[lang.language].children.forEach((data) => {
          if (data.children) {
            data.children.forEach((ingredient) => {
              ingredient.uomAutocomplete = false;
            });
          }
        });
      }
    });
    if (isAllergensAutocompleteOpen.value) {
      getRef("allergensInput").focus();
    }
  } else if (section === "ingredientUom") {
    ingredientsUomList.value.forEach((item, intex) => {
      if (item.display == ingredient.UOM) {
        ingredientUomAutocompleteArrowCounter.value = intex;
      }
      if (ingredient.UOM?.length === 0) {
        ingredientUomAutocompleteArrowCounter.value = 0;
      }
    });
    if (searchedUomText.value !== "") {
      if (ingredientsUomList.value && ingredientsUomList.value?.length) {
        if (
          ingredientsUomList.value.filter((e) => e.display === ingredient.UOM)
            ?.length === 0
        ) {
          ingredient.UOM = "";
        }
      } else {
        ingredient.UOM = "";
      }
    }
    if (ingredientsUomList.value?.length) {
      searchedUomText.value = "";
      ingredient.uomAutocomplete = !ingredient.uomAutocomplete;
      isIingredientUomAutocompleteOpen.value = !isIingredientUomAutocompleteOpen.value;
      setTimeout(() => {
        if (isIingredientUomAutocompleteOpen.value == 1) {
          let ingUom = getRef("ingredientUom" + groupIndex + index);
          ingUom.focus();
        }
      }, 1000);
      resetAllDropdowns();
      availableLang.value.forEach((lang) => {
        if (ingredientsData.value?.[lang.language]?.children) {
          ingredientsData.value[lang.language].children.forEach((item, gIdx) => {
            if (item.children && gIdx === groupIndex) {
              item.children.forEach((data) => {
                if (item.children.indexOf(data) !== index) {
                  data.uomAutocomplete = false;
                }
              });
            } else if (item.children && gIdx !== groupIndex) {
              item.children.forEach((data) => {
                data.uomAutocomplete = false;
              });
            }
          });
        }
      });
      const tempData = { ...ingredientsData.value };
      ingredientsData.value = {};
      ingredientsData.value = tempData;
    }
  }
  else {
    await getRecipeTags();

    let scrolling = false;
    const tagListElm = document.querySelector("#tagsList");

    if (tagListElm) {
      tagListElm.addEventListener("scroll", () => {
        scrolling = true;
        setInterval(() => {
          if (scrolling) {
            scrolling = false;
            if (tagListElm.offsetHeight + tagListElm.scrollTop + 1 >= tagListElm.scrollHeight) {
              clickTagsLoadMoreAsync("clickedTagsLoadMore");
            }
          }
        }, 1500);
      });
    }

    if (inputClick) {
      isTagsAutocompleteOpen.value = true;
    } else {
      isTagsAutocompleteOpen.value = !isTagsAutocompleteOpen.value;
    }

    if (!isTagsAutocompleteOpen.value) {
      await getRecipeTags();
    }
    scrollListToTop("tagsList");

    availableLang.value.forEach((lang) => {
      if (
        ingredientsData?.value?.[lang.language]?.children
      ) {
        ingredientsData.value[lang.language].children.forEach((data) => {
          if (data.children) {
            data.children.forEach((ingredient) => {
              ingredient.uomAutocomplete = false;
            });
          }
        });
      }
    });
    if (isTagsAutocompleteOpen.value) {
      getRef("tagInput").focus();
    }
  }
};

const handleClickOutsideDiets = (event) => {
  if (!displayInstructionsPage.value) {
    if (!document.querySelector(".filter-diets-container")?.contains(event.target)) {
      isDietAutocompleteOpen.value = false;
      dietQuery.value = "";
      resetDropdown("dietsList");
      clickedLoadMore.value = "";
    }
  }
};

const handleClickOutsideAllergens = (event) => {
  if (!displayInstructionsPage.value && isDisplayAllergens.value) {
    if (!document.querySelector(".filter-allergens-container")?.contains(event.target)) {
      isAllergensAutocompleteOpen.value = false;
      allergensQuery.value = "";
      resetDropdown("allergensList");
      clickedLoadMore.value = "";
    }
  }
};

const handleClickOutsideCategories = (event) => {
  if (!displayInstructionsPage.value) {
    if (!document.querySelector(".filter-categories-container")?.contains(event.target)) {
      isCategoryAutocompleteOpen.value = false;
      categoryQuery.value = "";
      resetDropdown("categoryList");
      clickedLoadMore.value = "";
    }
  }
};

const handleClickOutsideVariant = (event) => {
  if (availableLanguageDropdown.value) {
    if (!document.querySelector(".recipe-variant-language-container")?.contains(event.target)) {
      availableLanguageDropdown.value = false;
    }
  }
};

const handleClickSelectLanguagePopup = (event) => {
  if (!document.querySelector(".recipe-variant-language-dropdown")?.contains(event.target)) {
    hasRecipeVariantLanguageResult.value = false;
  }
};

const handleClickOutsideTags = (event) => {
  if (!displayInstructionsPage.value) {
    if (!document.querySelector(".filter-tags-container")?.contains(event.target)) {
      isTagsAutocompleteOpen.value = false;
      tagsQuery.value = "";
      resetDropdown("tagsList");
      clickedTagsLoadMore.value = "";
    }
  }
};

const handleClickOutside = (event) => {
  const checkOutsideClick = (selector, callback) => {
    if (document.querySelector(selector) && !document.querySelector(selector)?.contains(event.target)) {
      callback();
    }
  };

  if (publisherDropdownResult.value) {
    checkOutsideClick(".publisher-drop-down", () => {
      publisherDropdownResult.value = false;
      let scroll = getRef("selectOneText");
      scroll.scrollTo(0, 0);
    });
  }

  if (isNutritionDropdownResult.value) {
    checkOutsideClick("#nutrition-drop-down", () => {
      isNutritionDropdownResult.value = false;
    });
  }

  if (currencyDropdownResult.value) {
    checkOutsideClick(".currency-dropdown", () => {
      currencyDropdownResult.value = false;
    });
  }

  if (isSchedulingCalendarVisible.value) {
    checkOutsideClick("#recipe-calendar-section", () => {
      checkScheduleDates();
      isSchedulingCalendarVisible.value = false;
    });
  }
};
const emptyAutoSlugAsync = async () => {
  if (!slugData.value?.[recipeVariantSelectedLanguage.value]?.slugStatus && slugEdit.value === "") {
    slugData.value[recipeVariantSelectedLanguage.value].slugStatus = true;
    await getRecipeSlugAsync();
  }
  hasSlugCheckConfirm.value = true;
  rearrangeIngredient();
  setRecipeSlugInput();
  await checkSlug();
  hasSlugCheckConfirm.value = false;
};

const displaySavePopupAsync = async () => {
  toggleDropdownOff();
  if (!isCampaignModified.value) {
    return;
  }
  await emptyAutoSlugAsync();
  checkSelectedImageLeaveEmpty();
  if (isSchedulePublishVisible.value && recipeVariantSelectedLanguage.value === defaultLang.value) {
    if (recipeSchedulePublishDate.value === "" && recipeScheduleEndDate.value === "") {
      isScheduleWarningPopupVisible.value = true;
    } else {
      isConfirmScheduleRecipePopupVisible.value = true;
    }
  } else {
    isSaveModalVisible.value = true;
  }
  if (route.query.isin) checkRecipeEvent($keys.EVENT_KEY_NAMES.CLICK_EDIT_RECIPE_SAVE);
};

const checkSelectedImageLeaveEmpty = () => {
  if (isMainImageNotSelected.value && !hasShowPublishButton.value && !productVideo.value && !isLinkPresent.value) {
    config.showMainErrorMessage = true;
  }
};

const checkRecipeEvent = (description) => {
  const recipeNameValue = recipeName.value || (recipeData.value?.title ? recipeData.value.title[lang.value] : "");
  const eventProperties = {
    [t('EVENT_NAMES.RECIPE_ID_ISIN')]: recipeID.value || $route.query.isin,
    [t('EVENT_NAMES.RECIPE_NAME')]: recipeNameValue
  };
  $tracker.sendEvent(description, eventProperties, { ...LOCAL_TRACKER_CONFIG });
};

async function callSlugChangeAsync() {
  isSlugInputWarning.value = false;
  hasSlugCheckConfirm.value = true;
  await setRecipeSlugInput();
  await checkSlug();
  hasSlugCheckConfirm.value = false;
};

const displayPublishPopupAsync = async (data) => {
  toggleDropdownOff();
  if (!isCampaignModified.value) {
    return;
  }
  await emptyAutoSlugAsync();
  clickedOnPublish.value = data;
  if (isRecipeScheduled.value && isSchedulePublishVisible.value && recipeVariantSelectedLanguage.value === defaultLang.value) {
    if (recipeSchedulePublishDate.value === "" && recipeScheduleEndDate.value === "" && publishDate.value === 0 && endDate.value === 0) {
      isScheduleWarningPopupVisible.value = true;
    } else {
      isConfirmScheduleRecipePopupVisible.value = true;
    }
  } else {
    isPublishModalVisible.value = true;
  }
};

const getRecipePrice = (value) => {
  if (value === "recipesPrice" && recipePrice < 0) {
    recipePrice.value = "";
    return;
  }
  if (recipePrice.value !== "") {
    recipePrice.value = parseFloat(recipePrice.value);
  }
};

const getAvailableServings = (servingList, value) => {
  if (typeof servingList === "string" && servingList.includes("-") && value === "availableServing") {
    availableServings.value = "";
    return;
  }
  if (value === "Serving" && servings.value < 0) {
    servings.value = "";
    return;
  }
  if (servings.value === 0) {
    servings.value = "";
  }
  if (servingList !== "" || servings.value !== "") {
    let pushData = [];
    let servingData = Number(servings.value);
    let splitData = servingList.toString().split(",").map(item => parseInt(item)).filter(item => !isNaN(item));
    if (!splitData.includes(servingData)) {
      splitData.push(servingData);
    }
    splitData.forEach(data => {
      if (data >= 100) {
        data = 100;
      }
      pushData.push(data);
    });
    pushData = [...new Set(pushData)];
    pushData.sort((a, b) => a - b);
    availableServings.value = pushData.filter(data => data !== 0);
  } else {
    availableServings.value = [];
  }
};

const removeLeadingZeros = (event) => {
  const inputValue = event.target.value;
  const sanitizedValue = inputValue?.replace(/^0+/, "");
  event.target.value = sanitizedValue;
  servings.value = sanitizedValue;
};

const searchUOMList = (ingredient) => {
  searchedUomText.value = ingredient.UOM;
  searchedUomList.value = [];
  if (searchedUomText.value !== "") {
    let input = searchedUomText.value;
    let filter = input?.toUpperCase();
    for (let i = 0; i < ingredientsUomList.value?.length; i++) {
      let text = ingredientsUomList.value?.[i]?.display ? ingredientsUomList.value[i].display?.toUpperCase() : "";
      if (text.startsWith(filter)) {
        searchedUomList.value.push(ingredientsUomList.value[i]);
      }
    }
  }
};

const handleTypeInput = (event) => {
  if (getRef("slugField").contains(event.target)) {
    isCampaignModified.value = true;
    hasSlugCheckConfirm.value = true;
  }
  if (event && event.path && event.path[0] && event.path[0].id) {
    isCampaignModified.value = true;
  }
  if (event.target.id.includes("ingredientName")) {
    isIngredientNameTyped.value = true;
  }
};

const clickTagsLoadMoreAsync = async (data) => {
  clickedTagsLoadMore.value = data;
  let from = parseInt(fromPopUpTags.value) + sizePopUpTags.value;
  if (from < tagsTotal.value) {
    fromPopUpTags.value = from;
    const loadMoreTagsList = await getRecipeTags();
    if (loadMoreTagsList && tagsList.value) {
      tagsList.value = [...tagsList.value, ...loadMoreTagsList];
    }
  }
  clickedTagsLoadMore.value = "";
};

const clickCategoriesLoadMoreAsync = async (data) => {
  clickedLoadMore.value = data;
  let from = parseInt(fromPopUpCategory.value) + sizePopUpCategory.value;
  if (from < categoriesTotal.value) {
    fromPopUpCategory.value = from;
    let loadMoreCategoriesList = await getRecipeCategories();
    if (loadMoreCategoriesList && categoriesList.value) {
      categoriesList.value = [
        ...categoriesList.value,
        ...loadMoreCategoriesList,
      ];
    }
  }
};

const disableAddButton = (inputIngredientWeight) => {
  let numbers = /^\d+$/;
  return !!inputIngredientWeight.match(numbers);
};

const getQuantity = (ingredient) => {
  ingredient.quantity = parseFloat(ingredient.quantity);
};

const disableScroll = () => {
  document.addEventListener("wheel", function () {
    if (
      document.activeElement.type === "number" &&
      document.activeElement.classList.contains("no-scroll")
    ) {
      document.activeElement.blur();
    }
  });
};

const recipeVariantLanguageModal = () => {
  recipeVariantLanguage.value = "";
  hasRecipeVariantLanguagePopup.value = true;
};

const deleteRecipeVariantPopUp = () => {
  isDeleteRecipeVariant.value = true;
};

const removeRecipeTitleLanguage = (language) => {
  if (recipeData.value?.title?.[language]) {
    delete recipeData.value.title[language];
  }
};

const removeRecipeSubtitleLanguage = (language) => {
  if (recipeData.value?.subtitle?.[language]) {
    delete recipeData.value.subtitle[language];
  }
};

const removeRecipeNotesAbstractLanguage = (language) => {
  if (recipeData.value?.description?.[language]) {
    delete recipeData.value.description[language];
  }
};

const removeRecipeIngredientLanguage = (language) => {
  if (ingredientsData?.[language]) {
    delete ingredientsData[language];
  }
};

const removeRecipeStepsLanguage = (language) => {
  if (tasksData.value?.length) {
    tasksData.value.forEach((data, indexTask) => {
      if (tasksData.value?.[indexTask][language]) {
        delete tasksData.value[indexTask][language];
      }
    });
  }
};

const removeLanguage = (language) => {
  removeRecipeTitleLanguage(language);
  removeRecipeSubtitleLanguage(language);
  removeRecipeNotesAbstractLanguage(language);
  removeRecipeIngredientLanguage(language);
  removeRecipeStepsLanguage(language);
};

const deleteRecipeVariantAsync = async () => {
  isDeleteRecipeVariant.value = false;
  if (recipeSchedulePublishDate.value !== "" && recipeScheduleEndDate.value !== "") {
    recipeButtonState.value = "Schedule";
    recipeButtonPublishState.value = "Schedule";
  }

  let isRecipeVariant = recipeVariantSelectedLanguage.value;
  availableLang.value.forEach((data, index) => {
    if (data?.language === recipeVariantSelectedLanguage.value) {
      availableLang.value.splice(index, 1);
      if (recipeData.value?.langs?.includes(data.language)) {
        saveRemovedRecipeVariants.value.push(data);
      }
    }
  });

  let langData = {};
  if (recipeVariantSelectedLanguage.value === "es-US") {
    langData = {
      language: recipeVariantSelectedLanguage.value,
      default_check: recipeVariantSelectedLanguage.value === defaultLang.value,
      language_name: "Spanish",
      languageFlag: "/images/flags/spain-flag.png",
    };
  } else if (recipeVariantSelectedLanguage.value === "fr-FR") {
    langData = {
      language: recipeVariantSelectedLanguage.value,
      default_check: recipeVariantSelectedLanguage.value === defaultLang.value,
      language_name: "French",
      languageFlag: "/images/flags/france-flag.png",
    };
  } else if (recipeVariantSelectedLanguage.value === "en-US") {
    langData = {
      language: recipeVariantSelectedLanguage.value,
      default_check: recipeVariantSelectedLanguage.value === defaultLang.value,
      language_name: "English",
      languageFlag: "/images/flags/us-flag.png",
    };
  }

  removeLanguage(isRecipeVariant);
  recipeVariantLanguageList.value.push(langData);
  let copyTaskData = tasksData.value;
  saveRemovedRecipeVariants.value.forEach((lang) => {
    copyTaskData.forEach((data) => {
      if (data && data[lang.language]) {
        delete data[lang.language];
      }
    });
  });
  await addLanguageAsync(defaultLang.value);
  tasksData.value = [];
  tasksData.value = copyTaskData;
  isCampaignModified.value = true;
};

const deleteVariant = async () => {
  let langData = [];
  let langString = "";
  if (saveRemovedRecipeVariants.value?.length > 0) {
    saveRemovedRecipeVariants.value.forEach((lang) => {
      langData.push(lang.language);
    });
    langString = langData.join(",");
    try {
      await store.dispatch("recipeDetails/deleteLanguageVariantAsync", {
        isin: route.query.isin,
        lang: langString
      })
      await postIngredientRawData();
      await saveRecipeDataAsync();
    } catch (e) {
      console.error(e);
    }
  }
};

async function saveRecipeDataAsync() {
  if (nutritionKey.value?.['perServing']) {
    perServingNutrientData.value = [];
    perServingNutrientData.value = formatNutritionData(
      nutrientTableData.value?.['perServing'],
      "perServing"
    );
  }
  if (nutritionKey.value?.['per100g']) {
    per100gNutrientData.value = [];
    per100gNutrientData.value = formatNutritionData(
      nutrientTableData.value?.['per100g'],
      "per100g"
    );
  }
  if (
    addRecipeNutrientType.value != "" &&
    nutritionKey.value &&
    (!nutritionKey.value?.['per100g'] || !nutritionKey.value?.['perServing'])
  ) {
    addRecipeNutrientData.value = [];
    addRecipeNutrientData.value = formatNutritionData(
      nutrientTableData?.value?.[addRecipeNutrientType.value],
      addRecipeNutrientType.value
    );
  }
  let postNutritionData = {};
  let per100g = [];
  let perServing = [];
  if (
    nutritionKey.value &&
    !nutritionKey.value.per100g &&
    !nutritionKey.value.perServing
  ) {
    postNutritionData = {
      [lang.value]: {
        [addRecipeNutrientType.value]:
          addRecipeNutrientData.value.length !== 0
            ? addRecipeNutrientData.value
            : "",
      },
    };
  } else if (
    (nutritionKey.value && nutritionKey.value?.['per100g']) ||
    nutritionKey.value.perServing
  ) {
    if (nutritionKey.value && nutritionKey.value?.['per100g'])
      per100g = per100gNutrientData.value;
    if (nutritionKey.value && nutritionKey.value?.['perServing'])
      perServing = perServingNutrientData.value;
    postNutritionData = {
      [lang.value]: {
        per100g: per100g,
        perServing: perServing,
      },
    };
  }

  hasSlugCheckConfirm.value = true;
  await checkSlug();
  hasSlugCheckConfirm.value = false;
  if (recipePrice.value == "0") {
    recipePrice.value = "0";
  }
  let payload;
  payload = {
    boostInSearch: isBoostInSearch.value,
    labels: selectedAllergens.value
      ? selectedAllergens.value.map((allergen) => allergen.key)
      : [],
    price: {
      currency:
        selectedCurrency.value.trim() || "usd",
      value: recipePrice.value >= 0 ? Number(recipePrice.value) : "",
    },
    isin: recipeIsin.value,
    provider: recipeData.value.provider || "cmsManual",
    title: {},
    subtitle: {},
    ingredients: {},
    tasks: {},
    slug: {},
    time: {
      total: getTotalTime(),
      prep: getRecipePreparationTime(),
      cook: getRecipeCookTime(),
    },
    externalId: recipeAttributionExternalId.value || "",
    description: {},
    nutrientTable: postNutritionData,
    media: {
      [lang.value]: {
        image: imageResponseUrl.value?.replace(/\?.*/, "") || recipeImage.value,
        video: [{
          url: videoResponseUrl.value?.replace(/\?.*/, "") || productVideo.value,
          size: videoDimension.value || ""
        }],
        externalVideoUrl: urlLinkUpdate.value || "",
        externalImageUrl: imageUrlLinkUpdate.value || null,
        thumbnailImageUrl: imageUrlThumbnailUpdate.value || null
      }
    },
    yield: {
      availableServings: availableServings.value || [],
      serving: Number(servings.value),
      raw: yieldData.value?.trim() || "",
      servingSize: nutritionServingSize.value.trim(),
      servingsPerContainer: nutritionServingSizePerContainer.value.trim(),
    },
    attribution: {
      [lang.value]: {
        author: {
          name: recipeAttributionAuthor.value?.trim() || "",
        },
        organization:
          recipeAttributionOrganization.value?.trim() || "",
      },
    },
    diets: selectedDiets.value
      ? selectedDiets.value.map((diet) => diet.key)
      : [],
    categories: {
      [lang.value]: selectedCategories.value
        ? selectedCategories.value.map((category) => category.isin)
        : [],
    },
    tags: {
      [lang.value]: selectedTags.value
        ? selectedTags.value.map((tag) => tag.isin)
        : [],
    },
    langs: availableLang.value.map((data) => data.language),
    countries: availableLang.value.map((data) => data.language.split("-")[1]),
  };

  if (selectedAllergens.value.length === 0) {
    delete payload.labels;
  }
  if (payload.externalId === '') {
    delete payload.externalId;
  }
  if (payload.slug == '') {
    delete payload.slug;
  }
  if (!isImagePresent.value) {
    delete payload.media[lang.value].image;
  }
  if (!isVideoPresent.value) {
    delete payload.media[lang.value].video;
  }
  if (!isLinkPresent.value) {
    delete payload.media[lang.value].externalVideoUrl;
  }
  if (!isImageLinkPresent.value) {
    delete payload.media[lang.value].externalImageUrl;
  }
  if (isImageLinkPresent.value) {
    delete payload.media[lang.value].image;
  }
  if (imageUrlThumbnailUpdate.value === '') {
    delete payload.media[lang.value].thumbnailImageUrl;
  }
  if (recipePrice.value === '') {
    delete payload.price;
  }
  if (
    payload.nutrientTable &&
    payload.nutrientTable[lang.value] &&
    (payload.nutrientTable[lang.value].perServing === '' ||
      payload.nutrientTable[lang.value].per100g === '')
  ) {
    delete payload.nutrientTable;
  }
  if (
    payload.attribution &&
    payload.attribution[lang.value] &&
    payload.attribution[lang.value].author &&
    payload.attribution[lang.value].organization === '' &&
    payload.attribution[lang.value].author.name === ''
  ) {
    delete payload.attribution;
  }
  if (
    payload.attribution &&
    payload.attribution[lang.value] &&
    payload.attribution[lang.value].author &&
    payload.attribution[lang.value].author.name === ''
  ) {
    delete payload.attribution[lang.value].author;
  }
  if (
    payload.attribution &&
    payload.attribution[lang.value] &&
    payload.attribution[lang.value].organization === ''
  ) {
    delete payload.attribution[lang.value].organization;
  }
  if (
    payload.time &&
    payload.time.prep &&
    payload.time.total &&
    payload.time.cook &&
    payload.time.prep === 'PT' &&
    payload.time.total === 'PT' &&
    payload.time.cook === 'PT'
  ) {
    delete payload.time;
  }
  if (payload.time && payload.time.total === 'PT') {
    delete payload.time.total;
  }
  if (payload.time && payload.time.prep === 'PT') {
    delete payload.time.prep;
  }
  if (payload.time && payload.time.cook === 'PT') {
    delete payload.time.cook;
  }
  if (payload.yield && payload.yield.raw === '') {
    delete payload.yield.raw;
  }
  if (payload.yield && payload.yield.servingSize === '') {
    delete payload.yield.servingSize;
  }
  if (payload.yield && payload.yield.servingsPerContainer === '') {
    delete payload.yield.servingsPerContainer;
  }
  if (payload.attribution && !payload.attribution[lang.value]) {
    delete payload.attribution;
  }

  payload = await setPayLoadWithVariantAsync(payload);

  if (payload && Object.keys(payload.description).length === 0) {
    delete payload.description;
  }
  if (payload && Object.keys(payload.subtitle).length === 0) {
    delete payload.subtitle;
  }

  recipeImageList.value.forEach((item) => {
    if (item?.isMainImage) {
      if (["aiGenerated", "manualUpload", "unknown"].includes(item.source)) {
        payload.media[lang.value].image = item.url;
        payload.media[lang.value].externalImageUrl = null;
      } else if (item.source === "externalLink") {
        payload.media[lang.value].image = null;
        payload.media[lang.value].externalImageUrl = item.url;
      }
    }
  });

  payload.media[lang.value].imageList = recipeImageList.value.map((item) => ({
    source: item.source,
    url: item.url,
  }));

  return RecipeService.saveRecipe(
    project.value,
    payload,
    recipeTxnId.value,
    store,
    $auth
  )
    .then(async (response) => {
      isPublishModalVisible.value = false;

      if (response) {
        isRecipeSuccess.value = response?.message ?? "";

        if ((isShowSaveButton.value || hasShowPublishButton.value) && isRecipeSuccess.value === "success") {
          await publishRecipe(recipeIsin);
        } else if ((!isShowSaveButton.value || !hasShowPublishButton.value) && isRecipeSuccess.value === "success") {
          await unPublishRecipe(recipeIsin);
        }

        await saveRecipeCampaigns();

        if (!route.query[QUERY_PARAM_KEY.ISIN]) {
          await recipeRecentActivityDataAsync();
        }

        isRecipeSaving.value = false;
        saveRouter();
        isShowPublishSection.value = true;
        isPublishModalVisible.value = false;
      } else {
        isShowPublishSection.value = false;
      }

      isCampaignModified.value = false;
    })
    .catch((e) => {
      checkRecipeEvent($keys.EVENT_KEY_NAMES.EDIT_RECIPE_ERROR);

      if (e.response?.data?.code === 409) {
        $eventBus.emit("error_for_409");
        router.push("/recipes");
      } else {
        isRecipeSaving.value = false;
        isSaveModalVisible.value = false;
        isConfirmScheduleRecipePopupVisible.value = false;
        isPublishModalVisible.value = false;
        errorMessage.value = e.response?.data?.message;
        isErrorOccuredModal.value = true;
      }
    });
}
const setRecipeVariantLanguageMatches = (value) => {
  recipeVariantLanguage.value = value;
  hasRecipeVariantLanguageResult.value = false;
  recipeVariantLanguageList.value.forEach((data, index) => {
    if (
      data &&
      recipeVariantLanguage.value &&
      data.language === recipeVariantLanguage.value.language
    ) {
      recipeVariantLanguageList.value.splice(index, 1);
      recipeVariantLanguageList.value.unshift(data);
    }
  });
};

const setRecipeVariantLanguageAsync = async () => {
  await unableToLeaveEmptyAsync();
  hasRecipeVariantLanguagePopup.value = false;
  if (isUnableToLeaveVariant.value) {
    return;
  }
  recipeVariantSelectedLanguage.value = recipeVariantLanguageList.value?.[0]?.language;
  recipeVariantLanguage.value = recipeVariantLanguageList.value?.[0];
  isCampaignModified.value = true;

  let langData = {};
  switch (recipeVariantSelectedLanguage.value) {
    case $keys.LANGUAGE.SPANISH:
      langData = {
        language: recipeVariantSelectedLanguage.value,
        default_check: recipeVariantSelectedLanguage.value === defaultLang.value,
        language_name: $keys.LANGUAGE.SPANISH_LANGUAGE,
        languageFlag: "/images/flags/spain-flag.png",
      };
      break;
    case $keys.LANGUAGE.FRENCH:
      langData = {
        language: recipeVariantSelectedLanguage.value,
        default_check: recipeVariantSelectedLanguage.value === defaultLang.value,
        language_name: $keys.LANGUAGE.FRENCH_LANGUAGE,
        languageFlag: "/images/flags/france-flag.png",
      };
      break;
    case $keys.LANGUAGE.ENGLISH:
      langData = {
        language: recipeVariantSelectedLanguage.value,
        default_check: recipeVariantSelectedLanguage.value === defaultLang.value,
        language_name: $keys.LANGUAGE.ENGLISH_LANGUAGE,
        languageFlag: "/images/flags/us-flag.png",
      };
      break;
    default:
      return;
  }

  availableLang.value.push(langData);

  recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter((data) => data.language !== recipeVariantSelectedLanguage.value);

  saveRemovedRecipeVariants.value = saveRemovedRecipeVariants.value.filter((data) => data.language !== recipeVariantSelectedLanguage.value);

  await addLanguageAsync(recipeVariantSelectedLanguage.value);
  closeSchedulePopupForVariant(recipeVariantSelectedLanguage.value);
};

const showRecipeVariantLanguageMatches = () => {
  hasRecipeVariantLanguageResult.value = !hasRecipeVariantLanguageResult.value;
};

const videoData = computed(() => {
  const media = step?.[recipeVariantSelectedLanguage.value]?.media?.video?.[0];
  return media || {};
});

const videoUrl = computed(() => videoData.value.url || '');

const isAllRecipeDataFilled = computed(() => !!(recipeIsin.value && recipeName.value?.trim() && servings.value));

onUnmounted(() => {
  document.removeEventListener("click", handleClickSelectLanguagePopup);
  document.removeEventListener("click", handleClickOutsideVariant);
  document.removeEventListener("click", handleClickOutsideCategories);
  document.removeEventListener("click", handleClickOutsideDiets);
  document.removeEventListener("click", handleClickOutsideAllergens);
  document.removeEventListener("click", handleClickOutsideTags);
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("input", handleTypeInput);
  document.removeEventListener("dragend", dragIngredient);
  triggerLoading($keys?.KEY_NAMES.CAMPAIGN_MODIFIED, false);
  $eventBus.off("savePreviewPage");
  $eventBus.off("cancelShoppableReview");
  $eventBus.off("publishPreviewPage");
  $eventBus.off($keys.KEY_NAMES.CAMPAIGN_MODIFIED);
  setRecipeStepFirstTime();
});
watchEffect(() => {
  categoryInfoIconVisibility();
  tagInfoIconVisibility();
  $eventBus.emit("campaignModified", isCampaignModified.value);
  if (recipePrice.value > 1000) {
    recipePrice.value = 1000;
    return recipePrice.value;
  }

  if (hour.value > 200) {
    hour.value = 200;
    return hour.value;
  }
  if (minute.value > 59) {
    minute.value = 59;
    return minute.value;
  }
  if (second.value > 59) {
    second.value = 59;
    return second.value;
  }
  if (prepHour.value > 99) {
    prepHour.value = 99;
    return prepHour.value;
  }
  if (prepMinute.value > 59) {
    prepMinute.value = 59;
    return prepMinute.value;
  }
  if (prepSecond.value > 59) {
    prepSecond.value = 59;
    return prepSecond.value;
  }
  if (cookHour.value > 99) {
    cookHour.value = 99;
    return cookHour.value;
  }
  if (cookMinute.value > 59) {
    cookMinute.value = 59;
    return cookMinute.value;
  }
  if (cookSecond.value > 59) {
    cookSecond.value = 59;
    return cookSecond.value;
  }

  if (servings.value > 100) {
    servings.value = 100;
    return servings.value;
  }
  const dragValue = drag?.value || false;
  if (dragValue) {
    toggleDropdownOff();
    isCampaignModified.value = dragValue;
  }
  $eventBus.emit("campaignModified", isCampaignModified.value);
  if (!isOnline.value) {
    closeModal();
  }
  if (recipeImageList.value?.length) {
    isImageIsMain.value = recipeImageList.value.some((data) => {
      return data.isMainImage === true;
    });
  }
});

onEscapeKeyPress(closeModal);

watch(isCampaignModified, (newValue) => {
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, newValue);
});


</script>
