import { useConfigStore } from "../stores/config.js";
import { useConfig } from "../composables/useConfig.js";

export default defineNuxtPlugin(async (nuxtApp) => {
  const { config: innerConfig } = useConfig();
  const store = useConfigStore(nuxtApp.$pinia);

  const { data, error } = await useAsyncData("initStore", async () => {
    const [config, sidebarNavs] = await Promise.all([
      await $fetch(
        `${innerConfig.value.BASE_PATH_WEB_BACKEND}${innerConfig.value.INNER_API_ENDPOINT_CONFIG_INIT}`,
        {
          baseURL: innerConfig.value.WEB_BACKEND_HOST,
        }
      ),
      await $fetch(
        `${innerConfig.value.BASE_PATH_WEB_BACKEND}${innerConfig.value.INNER_API_ENDPOINT_CONFIG_MENUS}`,
        {
          baseURL: innerConfig.value.WEB_BACKEND_HOST,
        }
      ),
    ]);
    return { config, sidebarNavs };
  });

  await store.setConfigAsync(data?.value?.config, data?.value?.sidebarNavs);

  // Sync config data from Pinia to Vuex store for compatibility
  const { $store } = nuxtApp;
  if ($store) {
    $store.dispatch('config/syncFromPinia');
  }
});
