import { categoryGroupModel } from "@/models/category-group.model";
import { KEYS } from '@/сonstants/keys';
import { useNuxtApp } from 'nuxt/app';
import { useConfigStore } from "../stores/config.js";

const state = () => ({
  categoryGroupList: {},
  categoryStatistics: [],
  categoryForCategoryGroupList: [],

  categoryGroupListForTable: [],
  pagination: {
    from: 0,
    size: 15,
    total: 0,
  },
});

const createMutation = (setter) => (state, payload) => {
  state[setter] = payload.data;
};

const mutations = {
  SET_CATEGORY_GROUP_LIST: createMutation('categoryGroupList'),
  SET_CATEGORY_STATISTICS: createMutation('categoryStatistics'),
  SET_CATEGORY_FOR_CATEGORY_GROUP_LIST: createMutation('categoryForCategoryGroupList'),
  SET_PAGINATION_TOTAL(state, payload) {
    state.pagination.total = payload.data.total;
  },
  SET_CATEGORY_GROUP_LIST_FOR_TABLE: createMutation('categoryGroupListForTable'),
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getCategoryGroupList: createGetter('categoryGroupList'),
  getCategoryStatistics: createGetter('categoryStatistics'),
  getCategoryForCategoryGroupList: createGetter('categoryForCategoryGroupList'),
  getPagination: createGetter('pagination'),
  getCategoryGroupListForTable: createGetter('categoryGroupListForTable'),
};

const actions = {
  async getCategoryGroupListAsync({ commit }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    const configStore = useConfigStore();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = configStore.getClientEndpoint(
        "icl",
        "getCategoryGroupList"
      );
      const baseURL = configStore.getClientConfig("icl").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { ...payload },
      });
      if (response) {
        commit("SET_CATEGORY_GROUP_LIST", response);
      }
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} getCategoryGroupListAsync:`, error);
    }
  },

  async getCategoryStatisticsAsync({ commit, rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "getCategoryStatistics"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { ...payload },
      });
      if (response) {
        commit("SET_CATEGORY_STATISTICS", response);
      }
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} getCategoryStatisticsAsync:`, error);
    }
  },

  async deleteCategoryGroupListAsync({ rootGetters }, { isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = `${rootGetters["config/getClientEndpoint"](
        "icl",
        "saveTag"
      )}/${isin}`;
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;

      const response = await $axios.delete(endpoint, {
        baseURL,
      });
      return response;
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} deleteCategoryGroupListAsync:`, error);
    }
  },

  async postCategoryOrCategoryGroupAsync({ rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "saveTag"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.post(endpoint, payload, {
        baseURL,
      });
      return response;
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} postCategoryOrCategoryGroupAsync:`, error);
    }
  },

  async patchCategoryAsync({ rootGetters }, { isin, payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!isin || !Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = `${rootGetters["config/getClientEndpoint"](
        "icl",
        "saveTag"
      )}/${isin}`;
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.patch(endpoint, payload, {
        baseURL,
      });
      return response;
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} patchCategoryAsync:`, error);
    }
  },

  async getCategoryForCategoryGroupListAsync({ commit, rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    const endpoint = rootGetters['config/getClientEndpoint']("icl", "saveTag");
    const baseURL = rootGetters["config/getClientConfig"]("icl").host;
    try {
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { ...payload },
      });

      if (response) {
        commit("SET_CATEGORY_FOR_CATEGORY_GROUP_LIST", response);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getCategoryForCategoryGroupList:", error)
    }
  },

  /**
   * @deprecated
   *
   * @param commit
   * @param rootGetters
   * @param state
   * @param params
   * @returns {Promise<void>}
   */
  async getCategoryGroupListForTableAsync({ commit, rootGetters, state }, { params }) {
    if (!params) {
      return;
    }

    params.includeCategories = true
    const endpoint = rootGetters['config/getClientEndpoint']("flite", "getCategoryGroupMasterData");
    const baseURL = rootGetters["config/getClientConfig"]("flite").host;
    try {

      /**
       * @type {AxiosResponse<CategoryGroupResponse>}
       */
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });

      /**
       * @type {CategoryGroupResponse}
       */
      const data = response.data;

      commit("SET_PAGINATION_TOTAL", {
        data: {
          total: data.total,
        },
      });

      commit("SET_CATEGORY_GROUP_LIST_FOR_TABLE", {
        data: data.results?.map(categoryGroupModel) || [],
      });
    } catch (error) {
      console.error(error);
    }
  },
  resetCategoryGroupList({ commit }) {
    commit("SET_CATEGORY_GROUP_LIST_FOR_TABLE", { data: [] });
    commit("SET_PAGINATION_TOTAL", { data: { total: 0 } });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
