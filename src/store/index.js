/**
 * @module store/index
 */
import { createStore } from 'vuex';
import articles from "./articles.js";
import recipe from './recipe';
import recipeDetails from './recipeDetails';
import recipePreview from './recipePreview';
import userData from './userData';
import overview from './overview';
import batchGenerator from './batchGenerator';
import ingredient from './ingredient';
import shoppableReview from './shoppableReview';
import editSearch from './editSearch';
import video from './video';
import recipeGeneration from './recipeGeneration';
import collection from './collection';
import categories from './categories';
import categoriesGroup from './categoriesGroup';
import organizations from './organizations';
import tagData from './tagData';
import isin from './isin';
import preSignedUrl from './preSignedUrl';
import banner from './banner';

export const strict = false;

/**
 * Actions for initializing Vuex store during Nuxt.js server-side initialization.
 * @type {Object}
 * @property {Function} nuxtServerInit - Action to initialize Vuex store during Nuxt.js server-side initialization.
 *   @property {Object} context - The Vuex action context.
 *   @property {Function} context.dispatch - The Vuex dispatch function to dispatch actions.
 *   @property {Function} context.commit - The Vuex commit function to commit mutations.
 *   @property {Object} context.req - The Nuxt.js request object from the server.
 */
export const actions = {
  async nuxtServerInit() {
    // Config initialization is now handled by Pinia plugin
    // No need to dispatch config actions here
  }
}

export default createStore({
  modules: {
    articles,
    recipe,
    recipeDetails,
    recipePreview,
    userData,
    overview,
    batchGenerator,
    organizations,
    tagData,
    ingredient,
    shoppableReview,
    editSearch,
    video,
    recipeGeneration,
    collection,
    categories,
    categoriesGroup,
    isin,
    preSignedUrl,
    banner
  },
  plugins: [
    // Config persistence is now handled by Pinia
  ],
  actions // Add the actions to the store
});
