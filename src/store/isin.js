import { useNuxtApp } from "nuxt/app";
import { useConfigStore } from "../stores/config.js";

const state = () => ({
  isin: [],
});

const mutations = {
  SET_ISIN(state, value) {
    state.isin = value;
  },
};

const getters = {
  getISIN(state) {
    return state.isin;
  },
};

const actions = {
  async getNewISINsAsync({ commit }, { lang, payload }) {
    if (!lang || !Object.keys(payload).length) {
      console.error("Missing required parameters");
      return;
    }
    try {
      const configStore = useConfigStore();
      const endpoint = configStore.getClientEndpoint(
        "flite",
        "getNewIsins"
      );
      const { $axios } = useNuxtApp();
      const baseURL = configStore.getClientConfig("flite").host;
      const response = await $axios.post(endpoint, null, {
        baseURL,
        params: { langs: lang, ...payload },
      });
      if (response.data) {
        commit("SET_ISIN", response.data);
      }
    } catch (error) {
      console.error(error);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
