import { KEYS } from "@/сonstants/keys.js";

const state = () => ({
  shoppableReviewConfig: {},
  productSuggestionIssue: [],
  ingredientPromotedProducts: [],
  ingredientReferenceProduct: [],
  ingredientProductMatches: [],
});

const createMutation = (setter) => (state, payload) => {
  state[setter] = payload.data;
};

const mutations = {
  SET_SHOPPABLE_REVIEW_CONFIG: createMutation("shoppableReviewConfig"),
  SET_PRODUCT_SUGGESTION_ISSUE: createMutation("productSuggestionIssue"),
  SET_INGREDIENT_PROMOTED_PRODUCTS: createMutation("ingredientPromotedProducts"),
  SET_INGREDIENT_REFERENCE_PRODUCT: createMutation("ingredientReferenceProduct"),
  SET_INGREDIENT_PRODUCT_MATCHES: createMutation("ingredientProductMatches"),
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getShoppableReviewConfig: createGetter("shoppableReviewConfig"),
  getProductSuggestionIssue: createGetter("productSuggestionIssue"),
  getIngredientPromotedProducts: createGetter("ingredientPromotedProducts"),
  getIngredientReferenceProduct: createGetter("ingredientReferenceProduct"),
  getIngredientProductMatches: createGetter("ingredientProductMatches")
};

import { useConfigStore } from "../stores/config.js";

const getEndpointAndBaseURL = (client, action) => {
  const configStore = useConfigStore();
  const endpoint = configStore.getClientEndpoint(client, action);
  const baseURL = configStore.getClientConfig(client).host;
  return { endpoint, baseURL };
};

const validateInput = (input) => {
  if (!Object.keys(input).length) {
    console.error(`${KEYS.KEY_NAMES.MISSING_REQUIRED_PARAMETERS}`);
    return false;
  }
  return true;
};

const handleError = (method, error) => {
  console.error(`${KEYS.KEY_NAMES.ERROR_IN} ${method}:`, error);
};

const validateParamsAndPayload = (params, payload) => {
  if (!Object.keys(params).length || !Object.keys(payload).length) {
    console.error(`${KEYS.KEY_NAMES.MISSING_REQUIRED_PARAMETERS}`);
    return false;
  }
  return true;
};

const actions = {
  async getShoppableReviewConfigDataAsync({ commit }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointAndBaseURL(
        "flite",
        "getShoppableReviewConfig"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      commit("SET_SHOPPABLE_REVIEW_CONFIG", response);
    } catch (error) {
      handleError("getShoppableReviewConfigDataAsync", error);
    }
  },

  async getIngredientReferenceMultipleProductDataAsync({ commit }, { params }) {
    if (!validateInput(params)) return;
    try {
      const { endpoint, baseURL } = getEndpointAndBaseURL(
        "flite",
        "getIngredientReferenceProduct"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      commit("SET_INGREDIENT_REFERENCE_PRODUCT", response);
    } catch (error) {
      handleError("getIngredientReferenceMultipleProductDataAsync", error);
    }
  },

  async postProductSuggestionIssueAsync({ commit }, { payload }) {
    if (!validateInput(payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointAndBaseURL(
        "icl",
        "postProductSuggestionIssue"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
      });
      commit("SET_PRODUCT_SUGGESTION_ISSUE", response);
    } catch (error) {
      handleError("postProductSuggestionIssueAsync", error);
    }
  },

  async getIngredientPromotedProductsAsync({ commit }, {params, payload }) {
    if (!validateParamsAndPayload(params, payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointAndBaseURL(
        "flite",
        "getIngredientPromotedProducts"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params
      });
      commit("SET_INGREDIENT_PROMOTED_PRODUCTS", response);
    } catch (error) {
      handleError("getIngredientPromotedProductsAsync", error);
    }
  },

  async getIngredientProductMatchesAsync({ commit }, { params, payload }) {
    if (!validateParamsAndPayload(params, payload)) return;
    try {
      const { endpoint, baseURL } = getEndpointAndBaseURL(
        "flite",
        "getIngredientProducts"
      );
      const { $axios } = useNuxtApp();
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params
      });
      commit("SET_INGREDIENT_PRODUCT_MATCHES", response);
    } catch (error) {
      handleError("getIngredientProductMatchesAsync", error);
    }
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
