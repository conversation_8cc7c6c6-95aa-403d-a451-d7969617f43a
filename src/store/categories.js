import { KEYS } from '@/сonstants/keys';
import { useNuxtApp } from 'nuxt/app';
import { useConfigStore } from "../stores/config.js";

const state = () => ({
  categoryData: [],
  categoryMasterData: [],
  editCategoryGroupList: [],
  promotedRecipesForCategories: [],
  categoryOperationStatus: "",
  categorySlug: "",
  categoryAssociations: [],
  masterListData: [],
  recipeForCategories: [],

  categoryListForTable: [],
  pagination: {
    from: 0,
    size: 15,
    total: 0,
  },
});

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};


const mutations = {
  SET_CATEGORY_DATA: createMutation('categoryData'),
  SET_CATEGORY_MASTER_DATA: createMutation('categoryMasterData'),
  SET_EDIT_CATEGORY_GROUP_LIST: createMutation('editCategoryGroupList'),
  SET_PROMOTED_RECIPES_FOR_CATEGORIES: createMutation('promotedRecipesForCategories'),
  SET_CATEGORY_OPERATION_STATUS: createMutation('categoryOperationStatus'),
  SET_CATEGORY_SLUG: createMutation('categorySlug'),
  SET_CATEGORY_ASSOCIATIONS: createMutation('categoryAssociations'),
  SET_MASTER_LIST_DATA: createMutation('masterListData'),
  SET_RECIPE_FOR_CATEGORIES: createMutation('recipeForCategories'),

  SET_CATEGORY_LIST_FOR_TABLE: createMutation('categoryListForTable'),
  SET_PAGINATION_TOTAL(state, payload) {
    state.pagination.total = payload.total;
  },
};

const actions = {
  async getCategoryDataAsync({ commit }, { lang, isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }

    try {
      const configStore = useConfigStore();
      let endpoint = configStore.getClientEndpoint(
        "flite",
        "getCategoryData"
      );
      const baseURL = configStore.getClientConfig("flite").host;
      const params = { lang, includeAlerts: true };
      endpoint = `${endpoint}/${isin}`;

      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });

      if (response) {
        commit("SET_CATEGORY_DATA", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getCategoryDataAsync:", error);
    }
  },

  async getCategoryListForTableAsync({ commit, state }, { params }) {
    if (!params) {
      return;
    }

    try {
      const configStore = useConfigStore();
      const endpoint = configStore.getClientEndpoint("flite", "getCategoryMasterData");
      const baseURL = configStore.getClientConfig("flite").host;
      const { $axios } = useNuxtApp();
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      const data = response?.data;
      const result = data?.results || [];
      const pagination = {
        total: data?.total
      };

      commit("SET_CATEGORY_LIST_FOR_TABLE", result);
      commit("SET_PAGINATION_TOTAL", pagination);
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getCategoryListForTableAsync:", error);
    }
  },

  async getCategoryMasterData({ commit, rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"]("flite", "getCategoryMasterData");
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { ...payload },
      });
      if (response) {
        commit("SET_CATEGORY_MASTER_DATA", response.data);
      }
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getCategoryMasterData:",
        error
      );
    }
  },

  async getEditCategoryGroupListAsync(
    { commit, rootGetters },
    { isin, sectionType }
  ) {
    const { $axios, $t } = useNuxtApp();
    if (!isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"]("icl", "saveTag");
      endpoint = `${endpoint}/${isin}`;
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const params = { type: sectionType };
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      if (response) {
        commit("SET_EDIT_CATEGORY_GROUP_LIST", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getEditCategoryGroupListAsync:", error)
    }
  },

  async getPromotedRecipesForCategoriesAsync(
    { commit, rootGetters },
    { lang, isin }
  ) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getCategoryCampaign"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang, includePromotedRecipeDetails: true };
      endpoint = endpoint.replace("{isin}", isin);

      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      if (response) {
        commit("SET_PROMOTED_RECIPES_FOR_CATEGORIES", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getPromotedRecipesForCategoriesAsync:", error)
    }
  },

  async postCategoryRecipeAsync({ rootGetters }, { payload }) {
    const { $axios } = useNuxtApp()
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "postAssociateOperation"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;

      const response = await $axios.post(endpoint, payload, {
        baseURL,
      });

      return response.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "postCategoryRecipeAsync:", error);
    }
  },

  async deleteCategoryList({ rootGetters }, { isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"]("icl", "saveTag");
      endpoint = `${endpoint}/${isin}`;
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      await $axios.delete(endpoint, {
        baseURL,
      });
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "deleteCategoryList:", error)
    }
  },

  async getOperationStatusAsync({ commit, rootGetters }, { operationId }) {
    const { $axios, $t } = useNuxtApp();
    if (!operationId) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "getOperationStatus"
      );
      endpoint = endpoint.replace("{opId}", operationId);
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.get(endpoint, {
        baseURL,
      });
      if (response) {
        commit("SET_CATEGORY_OPERATION_STATUS", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getOperationStatusAsync:", error)
    }
  },

  async saveRecipeCampaignDataAsync({ rootGetters }, { payload, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!payload || !lang) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "postCategoryCampaign"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang };

      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });

      return response.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "saveRecipeCampaignDataAsync:", error)
    }
  },

  async postCategoryOrCategoryGroupAsync({ rootGetters }, { payload, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!payload || !lang) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"]("icl", "saveTag");
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const params = { country: lang.split("-")[1] };
      params.lang = lang.split("-")[0];
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });

      return response.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "postCategoryOrCategoryGroupAsync:", error)
    }
  },

  async patchCategoryAsync({ rootGetters }, { payload, isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!payload || !isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"]("icl", "saveTag");
      endpoint = `${endpoint}/${isin}`;
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.patch(endpoint, payload, {
        baseURL,
      });

      return response.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "patchCategoryAsync:", error)
    }
  },

  async checkCategorySlugExistAsync({ commit, rootGetters }, { slug, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!slug || !lang) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "getCategorySlug"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const params = {
        slug,
        country: lang.split("-")[1],
        lang: lang.split("-")[0],
      };

      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });

      if (response) {
        commit("SET_CATEGORY_SLUG", response.data);
      }
    } catch (error) {
      commit("SET_CATEGORY_SLUG", KEYS.KEY_NAMES.SLUG_ALREADY_EXIST);
      console.error(KEYS.KEY_NAMES.ERROR_IN + "checkCategorySlugExistAsync:", error)
    }
  },

  async getCategoryAssociationsAsync(
    { commit, rootGetters },
    { lang, isin, from, size }
  ) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getCategoryAssociations"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = {
        lang,
        from,
        size,
      };

      endpoint = endpoint.replace("{isin}", isin);

      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });

      if (response) {
        commit("SET_CATEGORY_ASSOCIATIONS", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getCategoryAssociationsAsync:", error)
    }
  },

  async deleteLanguageVariantAsync({ rootGetters }, { lang, isin }) {
    const { $axios, $t } = useNuxtApp();
    if (!lang || !isin) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"]("icl", "saveTag");
      endpoint = `${endpoint}/${isin}`;
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const params = { langs: lang.join(",") };
      const response = await $axios.delete(endpoint, {
        baseURL,
        params,
      });

      return response.data;
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "deleteLanguageVariantAsync:", error)
    }
  },

  async getMasterListDataAsync({ commit, rootGetters }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if (!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"](
        "icl",
        "getCategoryGroupList"
      );
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { ...payload },
      });

      if (response) {
        commit("SET_MASTER_LIST_DATA", response.data);
      }
    } catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getMasterListDataAsync:", error)
    }
  },

  async getRecipeForCategoriesAsync({ rootGetters, commit }, { payload }) {
    const { $axios, $t } = useNuxtApp();
    if(!Object.keys(payload).length) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      let endpoint = rootGetters["config/getClientEndpoint"]("icl", "getRecipeList");
      const baseURL = rootGetters["config/getClientConfig"]("icl").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params: { ...payload },
      });
      commit("SET_RECIPE_FOR_CATEGORIES", response.data)
    }
    catch (error) {
      console.error(KEYS.KEY_NAMES.ERROR_IN + "getRecipeForCategoriesAsync:", error)
    }
  },

  resetCategoryList({ commit }) {
    commit("SET_CATEGORY_LIST_FOR_TABLE", []);
    commit("SET_PAGINATION_TOTAL", { total: 0 });
  },
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getCategoryData: createGetter('categoryData'),
  getCategoryMasterData: createGetter('categoryMasterData'),
  getEditCategoryGroupList: createGetter('editCategoryGroupList'),
  getPromotedRecipesForCategories: createGetter('promotedRecipesForCategories'),
  getOperationStatus: createGetter('categoryOperationStatus'),
  getCategorySlug: createGetter('categorySlug'),
  getCategoryAssociations: createGetter('categoryAssociations'),
  getMasterListData: createGetter('masterListData'),
  getRecipeForCategories: createGetter('recipeForCategories'),

  getCategoryListForTable: createGetter('categoryListForTable'),
  getPagination: createGetter('pagination'),
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
