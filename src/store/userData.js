import { STORAGE_KEY } from "../сonstants/storage-key";
import { compareStrings } from "../utils/compare-strings";
import { PROJECT_PERMISSIONS } from "../сonstants/project-permissions";
import {
    getItemFromLocalStorage,
    removeItemFromLocalStorage,
    setItemToLocalStorage
} from "../services/local-storage-service.js";
import { useConfig } from "../composables/useConfig.js";
import { useConfigStore } from "../stores/config.js";
import { useNuxtApp } from "nuxt/app";

const state = () => ({

    /**
     * Selected project.
     *
     * @property {string} id
     * @property {string} displayName
     * @property {Array} users - array of project users, can be empty
     *
     * @example
     * {
     *   id: "davidtestproject",
     *   displayName: "David's Test Project",
     *   users?: [{
     *     "id": "***",
     *     "permissions": ["manageContent", "manageProject", "manageUsers"],
     *     "profileColor": "#EDDC30",
     *   }],
     * }
     *
     * @default null
     */
    project: null,

    /**
     * Array of projects.
     *
     * @property {string} id
     * @property {string} displayName
     *
     * @example
     * [{
     *   id: "davidtestproject",
     *   displayName: "David's Test Project",
     * }]
     *
     * @default []
     */
    projectList: [],

    /**
     * Project languages.
     *
     * @property {string} defaultLang
     * @property {Array} availableLangs - array of available languages
     *
     * @example
     * {
     *   defaultLang: "en-US",
     *   availableLangs: ["en-US"],
     * }
     *
     * @default null
     */
    langs: null,

    /**
     * User project permissions.
     *
     * @property {string} id - project id
     * @property {Array} permissions - array of permissions
     *
     * @example
     * {
     *   projectId: "davidtestproject",
     *   permissions: ["manageContent", "manageProject", "manageUsers"],
     * }
     *
     * @default null
     */
    projectPermissions: null,


    /**
     * Array of users that connected to the selected project.
     *
     * @property {string} id
     * @property {string} email
     * @property {Array} permissions - array of permissions
     * @property {string} profileColor
     *
     * @example
     * [{
     *  "id": "***",
     *  "email": "<EMAIL>",
     *  "permissions": ["manageContent", "manageProject", "manageUsers"],
     *  "profileColor": "#FF568E"
     * }]
     *
     * @default []
     */
    projectUsers: [],
});

const mutations = {
    SET_DEFAULT_LANG(state, lang) {
        if (!state.langs) {
          state.langs = {};
        }
        state.langs.defaultLang = lang;
        localStorage.setItem(
          STORAGE_KEY.USER_DATA_LANGS,
          JSON.stringify({ defaultLang: lang })
        );
    },
    SET_PROJECT(state, project) {
        state.project = project
        setItemToLocalStorage(STORAGE_KEY.USER_DATA_PROJECT, {
            id: project.id,
            displayName: project.displayName
        });
    },
    SET_PROJECT_LIST(state, projectList) {
        state.projectList = projectList
    },
    SET_PROJECT_USERS(state, payload) {
        state.projectUsers = payload;
    },
    SET_LANGS(state, langs) {
        state.langs = langs
        setItemToLocalStorage(STORAGE_KEY.USER_DATA_LANGS, langs);
    },
    SET_PERMISSIONS(state, permissions) {
        state.projectPermissions = permissions;
    },
    CLEAR_ALL(state) {
        state.project = null;
        state.projectList = [];
        state.langs = null;
        state.projectPermissions = null;
        state.projectUsers = [];
        removeItemFromLocalStorage(STORAGE_KEY.USER_DATA_PROJECT);
        removeItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS);
    },
};

const actions = {
    setProject({ commit, state }, project) {
        commit('SET_PROJECT', project);
    },
    setProjectList({ commit, state }, projectList) {
        commit('SET_PROJECT_LIST', projectList);
    },
    setLangs({ commit, state }, langs) {
        commit('SET_LANGS', langs);
    },
    clear({ commit }) {
        commit('CLEAR_ALL');
    },
    updateLanguageOnProjectChange({ commit, state }) {
        const newLang = state.project?.id === "roche_main" ? "fr-FR" : "en-US";
        commit("SET_DEFAULT_LANG", newLang);
    },
    async postProjectAsync({ }, { payload }) {
        const { $axios } = useNuxtApp();
        const configStore = useConfigStore();
        const endpoint = configStore.getClientEndpoint("flite", "postProject");
        const baseURL = configStore.getClientConfig("flite").host;

        await $axios.post(endpoint, payload, {
            baseURL,
        }).catch();
    },

    async fetchProjectsAsync({ commit, state, rootGetters }, { isAdmin, isHotRefresh }) {
        const { $axios } = useNuxtApp();
        const { config } = useConfig();
        if (state.project && !isHotRefresh) {
            return;
        }

        const setUndefinedProject = () => {
            commit('SET_PROJECT', {
                id: undefined,
                displayName: undefined,
            });
        };

        try {
            const endpoint = rootGetters['config/getClientEndpoint']("flite", "getProjectsList");
            const baseUrl = rootGetters['config/getClientConfig']("flite").host;
            const response = await $axios.get(endpoint, {
                baseURL: baseUrl,
                headers: {
                    [config.value.HEADERS.X_INNIT_PROJECT_ID]: config.value.DEFAULT_PROJECT_ID,
                }
            });
            const processResponse = (response) => {
                if (response?.data?.length) {
                    const filteredResponse = isAdmin ? response?.data : response?.data.filter(project => project?.users?.length);
                    commit('SET_PROJECT_LIST', filteredResponse);

                    const cachedProject = getItemFromLocalStorage(STORAGE_KEY.USER_DATA_PROJECT);
                    const selectedProject = cachedProject
                        ? filteredResponse.find(({ id }) => compareStrings(id, cachedProject.id)) || filteredResponse[0]
                        : filteredResponse[0];
                    commit('SET_PROJECT', selectedProject);
                } else {
                    setUndefinedProject();
                }
            };
            processResponse(response);
        } catch (error) {
            console.warn("Cannot fetch project!", error);
            setUndefinedProject();
        }
    },

    async fetchLangsAsync({commit, state, rootGetters}, { isHotRefresh }) {
        const { $axios } = useNuxtApp();
        if (state.langs && !isHotRefresh) {
            return;
        }

        if (!state.project?.id) {
            return;
        }

        try {
            const endpoint = rootGetters['config/getClientEndpoint']("flite", "getLangConfig");
            const baseUrl = rootGetters['config/getClientConfig']("flite").host;
            const response = await $axios.get(endpoint, {
                baseURL: baseUrl,
            });

            if (response) {
                commit('SET_LANGS', response.data);
            }
        } catch (error) {}
    },

    async fetchUserPermissionsAsync({ commit, state, rootGetters }, { isHotRefresh }) {
        if (state.projectPermissions && !isHotRefresh) {
            return;
        }

        try {
            const endpoint = rootGetters['config/getClientEndpoint']("flite", "postControl");
            const baseUrl = rootGetters['config/getClientConfig']("flite").host;
            const { $axios, $auth } = useNuxtApp();
            const response = await $axios?.get(endpoint, {
                baseURL: baseUrl,
                params: {
                    userId: $auth?.user?.value?.sub
                },
            });
            if (response?.data?.projects?.length) {
                commit('SET_PERMISSIONS', {
                    projectId: response.data.projects[0].projectId,
                    permissions: response.data.projects[0].permissions
                });
            }
        } catch (error) {
            return Promise.reject(error);
        }
    },

    async updateProjectAsync({ commit, state, rootGetters, dispatch }, { id, displayName, isAdmin }) {
        if (!id || !displayName) {
            return;
        }

        const endpoint = rootGetters['config/getClientEndpoint']("flite", "updateProject");
        const baseURL = rootGetters['config/getClientConfig']("flite").host;
        const { $axios } = useNuxtApp();
        const { config } = useConfig();

        const response = await $axios.patch(endpoint, { displayName }, {
            baseURL,
            headers: {
                [config.value.HEADERS.X_INNIT_PROJECT_ID]: id,
            }
        }).catch();

        if (response?.data?.id) {
            await dispatch("fetchProjectsAsync", { isAdmin, isHotRefresh: true });
        }
    },

    async removeProjectAsync({ commit, state, rootGetters, dispatch }, { id,isAdmin }) {
        if (!id) {
            return;
        }

        const endpoint = rootGetters['config/getClientEndpoint']("flite", "deleteProject");
        const baseURL = rootGetters['config/getClientConfig']("flite").host;
        const { $axios } = useNuxtApp();
        const { config } = useConfig();

        const response = await $axios.delete(endpoint, {
            baseURL,
            headers: {
                [config.value.HEADERS.X_INNIT_PROJECT_ID]: id,
            }
        });

        if (response?.status === 200) {
            await dispatch("fetchProjectsAsync", { isAdmin, isHotRefresh: true });
            return true;
        }

        return false;
    },

    async getAllUsersDataAsync({ commit, rootGetters }) {
        const endpoint = rootGetters['config/getClientEndpoint']("flite", "getProjectDetails");
        const baseURL = rootGetters['config/getClientConfig']("flite").host;

        try {
            const { $axios } = useNuxtApp();
            const response = await $axios.get(endpoint, {
                baseURL,
                timeout: 120000
            });

            if (response?.data?.users) {
                commit("SET_PROJECT_USERS", response.data.users)
            }
        } catch (err) {
            console.error(err);
        }
    },

    async updateProjectUserPermissionsAsync({ commit, state, rootGetters, dispatch}, payload) {
        const endpoint = rootGetters['config/getClientEndpoint']("flite", "postControl");
        const baseURL = rootGetters['config/getClientConfig']("flite").host;

        try {
            const { $axios } = useNuxtApp();
            const response = await $axios.put(endpoint, payload, {
                baseURL,
                timeout: 120000
            });

            return response;
        } catch (err) {
            console.error(err);
            return err;
        }
    },

    async removeProjectUserPermissionsAsync({ commit, state, rootGetters, dispatch}, { params }) {
        const endpoint = rootGetters['config/getClientEndpoint']("flite", "postControl");
        const baseURL = rootGetters['config/getClientConfig']("flite").host;

        try {
            const { $axios } = useNuxtApp();
            const response = await $axios.delete(endpoint, {
                params,
                baseURL,
                timeout: 120000
            });

            return response;
        } catch (err) {
            console.error(err);
            return err;
        }
    }
};

const getters = {
    getProject: (state) => {
        return state.project;
    },
    getProjectList: (state) => {
        return state.projectList;
    },
    getProjectById: (state) => (id) => {
        return state.projectList?.find(project => project.id === id);
    },
    getDefaultLang: (state) => {
        return state?.langs?.defaultLang ||
          getItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS)?.defaultLang ||
          (state?.project?.id === "roche_main" ? "fr-FR" : "en-US");
    },
    getAvailableLangs: (state) => {
        return state?.langs?.availableLangs ||
          getItemFromLocalStorage(STORAGE_KEY.USER_DATA_LANGS)?.availableLangs ||
          null;
    },
    getProjectPermissions: (state) => {
        return state.projectPermissions?.permissions;
    },
    getProjectUsers: (state) => {
        return state.projectUsers;
    },
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
}
