import { useConfigStore } from '../stores/config.js';

// Bridge Vuex config store to Pinia config store
// This allows Vuex stores to access Pinia config store methods via rootGetters

const state = () => ({
  config: null,
  features: null,
  sidebarNavs: null,
  isLoaded: false
});

const getters = {
  getClientEndpoint: (state) => (service, endpoint) => {
    if (!state.config) {
      console.warn(`[Config Bridge] Config not loaded yet for ${service}/${endpoint}`);
      return '';
    }
    try {
      const template = state.config?.clients[service]?.endpoints[endpoint];
      if (!template) {
        console.warn(`[Config Bridge] Missing endpoint ${service}/${endpoint}`);
        return '';
      }
      // Simple URL formatting - replace placeholders if needed
      return template;
    } catch (error) {
      console.error(`[Config Bridge] Error getting endpoint ${service}/${endpoint}:`, error);
      return '';
    }
  },

  getClientConfig: (state) => (service) => {
    if (!state.config) {
      console.warn(`[Config Bridge] Config not loaded yet for ${service}`);
      return { host: '' };
    }
    try {
      return state.config?.clients[service] || { host: '' };
    } catch (error) {
      console.error(`[Config Bridge] Error getting config for ${service}:`, error);
      return { host: '' };
    }
  },

  getConfig: (state) => {
    return state.config;
  },

  getFeatures: (state) => {
    return state.features;
  },

  getSidebarNavs: (state) => {
    return state.sidebarNavs;
  }
};

const mutations = {
  SET_CONFIG(state, config) {
    state.config = config;
  },

  SET_FEATURES(state, features) {
    state.features = features;
  },

  SET_SIDEBAR_NAVS(state, sidebarNavs) {
    state.sidebarNavs = sidebarNavs;
  },

  SET_LOADED(state, isLoaded) {
    state.isLoaded = isLoaded;
  }
};

const actions = {
  // Sync data from Pinia store to Vuex store
  syncFromPinia({ commit }) {
    try {
      const configStore = useConfigStore();

      if (configStore.config) {
        commit('SET_CONFIG', configStore.config);
      }

      if (configStore.features) {
        commit('SET_FEATURES', configStore.features);
      }

      if (configStore.sidebarNavs) {
        commit('SET_SIDEBAR_NAVS', configStore.sidebarNavs);
      }

      commit('SET_LOADED', true);
      console.log('[Config Bridge] Synced data from Pinia to Vuex');
    } catch (error) {
      console.error('[Config Bridge] Error syncing from Pinia:', error);
    }
  },

  // Bridge actions to Pinia store if needed
  async fetchFeaturesAsync({ commit }, payload) {
    const configStore = useConfigStore();
    const result = await configStore.fetchFeaturesAsync(payload);

    // Sync the updated features back to Vuex
    if (configStore.features) {
      commit('SET_FEATURES', configStore.features);
    }

    return result;
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};