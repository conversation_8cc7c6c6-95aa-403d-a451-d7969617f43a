import { useConfigStore } from '../stores/config.js';

// Bridge Vuex config store to Pinia config store
// This allows Vuex stores to access Pinia config store methods via rootGetters

const state = () => ({
  // This is just a bridge - actual state is in Pinia store
});

const getters = {
  getClientEndpoint: () => (service, endpoint) => {
    const configStore = useConfigStore();
    return configStore.getClientEndpoint(service, endpoint);
  },

  getClientConfig: () => (service) => {
    const configStore = useConfigStore();
    return configStore.getClientConfig(service);
  },

  getConfig: () => {
    const configStore = useConfigStore();
    return configStore.config;
  },

  getFeatures: () => {
    const configStore = useConfigStore();
    return configStore.features;
  },

  getSidebarNavs: () => {
    const configStore = useConfigStore();
    return configStore.sidebarNavs;
  }
};

const mutations = {
  // No mutations needed - this is just a bridge
};

const actions = {
  // Bridge actions to Pinia store if needed
  async fetchFeaturesAsync({ commit }, payload) {
    const configStore = useConfigStore();
    return await configStore.fetchFeaturesAsync(payload);
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};