import { KEYS } from '@/сonstants/keys';
import { useNuxtApp } from "nuxt/app";
import { useConfigStore } from "../stores/config.js";

const state = () => ({
  collections: [],
  editCollectionData: [],
  collectionTagData: [],
});

const createMutation = (property) => {
  return (state, value) => {
    state[property] = value;
  };
};

const mutations = {
  SET_COLLECTIONS: createMutation("collections"),
  SET_EDIT_COLLECTIONS: createMutation("editCollectionData"),
  SET_COLLECTION_TAG_DATA: createMutation("collectionTagData"),
};

const actions = {
  async getCollectionDataAsync({ commit }, { lang, from, size }) {
    try {
      const { $axios } = useNuxtApp();
      const configStore = useConfigStore();
      const endpoint = configStore.getClientEndpoint(
        "module",
        "getCollectionList"
      );
      const baseURL = configStore.getClientConfig("module").host;
      const params = { lang: lang, from: from, size: size };
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });


      if (response) {
        commit("SET_COLLECTIONS", response.data);
      }
    } catch (error) {
      console.error(`${KEYS.KEY_NAMES.ERROR_IN} getCollectionDataAsync:`, error);
    }
  },
  async patchCollectionDataAsync(
    { rootGetters },
    { uuid, payload, onSuccess }
  ) {
    const { $axios, $t } = useNuxtApp();
    if (!uuid || !payload) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = `${rootGetters["config/getClientEndpoint"](
        "module",
        "getCollectionList"
      )}/${uuid}`;
      const baseURL = rootGetters["config/getClientConfig"]("module").host;
      const response = await $axios.patch(endpoint, payload, {
        baseURL,
      });
      if (response) {
        onSuccess();
      }
    } catch (error) {
      console.error(
        `${KEYS.KEY_NAMES.ERROR_IN} patchCollectionDataAsync:`,
        error
      );
    }
  },
  async postCollectionDataAsync({ rootGetters }, { payload, onSuccess, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!payload) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = rootGetters["config/getClientEndpoint"](
        "module",
        "getCollectionList"
      );
      const params = { lang: lang };
      const baseURL = rootGetters["config/getClientConfig"]("module").host;
      const response = await $axios.post(endpoint, payload, {
        baseURL,
        params,
      });
      if (response) {
        onSuccess();
      }
    } catch (error) {
      console.error(
        `${KEYS.KEY_NAMES.ERROR_IN} postCollectionDataAsync:`,
        error
      );
    }
  },
  async getEditCollectionDataAsync({ commit, rootGetters }, { uuid, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!uuid) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = `${rootGetters["config/getClientEndpoint"](
        "module",
        "getCollectionList"
      )}/${uuid}`;
      const params = { lang: lang };
      const baseURL = rootGetters["config/getClientConfig"]("module").host;
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });
      if (response) {
        commit("SET_EDIT_COLLECTIONS", response);
      }
    } catch (error) {
      console.error(error);
    }
  },
  async deleteCollectionDataAsync({ rootGetters }, { uuid, onDeleteSuccess }) {
    const { $axios, $t } = useNuxtApp();
    if (!uuid) {
      console.error($t('COMMON.MISSING_REQUIRED_PARAMETERS'));
      return;
    }
    try {
      const endpoint = `${rootGetters["config/getClientEndpoint"](
        "module",
        "getCollectionList"
      )}/${uuid}`;
      const baseURL = rootGetters["config/getClientConfig"]("module").host;
      const response = await $axios.delete(endpoint, {
        baseURL,
      });
      if (response) {
        onDeleteSuccess();
      }
    } catch (error) {
      console.error(
        `${KEYS.KEY_NAMES.ERROR_IN} deleteCollectionDataAsync:`,
        error
      );
    }
  },
  async getMultipleTagDataAsync({ commit, rootGetters }, { isins, lang }) {
    try {
      const { $axios } = useNuxtApp();
      const endpoint = rootGetters["config/getClientEndpoint"](
        "flite",
        "getTagMultiData"
      );
      const baseURL = rootGetters["config/getClientConfig"]("flite").host;
      const params = { lang: lang, isins: isins };
      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });

      if (response?.data) {
        commit("SET_COLLECTION_TAG_DATA", response.data);
      }
    } catch (error) {
      console.error(
        `${KEYS.KEY_NAMES.ERROR_IN} getMultipleTagDataAsync:`,
        error
      );
    }
  },
};

const createGetter = (property) => (state) => state[property];

const getters = {
  getCollectionData: createGetter("collections"),
  getEditCollectionData: createGetter("editCollectionData"),
  getCollectionTagData: createGetter("collectionTagData"),
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
