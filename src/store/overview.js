import { KEYS } from '@/сonstants/keys';
import { useNuxtApp } from "nuxt/app";
import { useConfigStore } from "../stores/config.js";
const state = () => ({
  recentActivityResponse: null,
});

const mutations = {
  SET_RECENT_ACTIVITY_RESPONSE(state, data) {
    state.recentActivityResponse = data;
  },
};

const actions = {
  async getRecentActivityAsync({ commit }, payload) {
    const { $axios, $t } = useNuxtApp();
    if (!payload?.lang || payload.from == null || payload.size == null) {
      console.error($t("COMMON.MISSING_REQUIRED_PARAMETERS"));
      return;
    }
    const configStore = useConfigStore();
    const endpoint = configStore.getClientEndpoint(
      "flite",
      "recentActivity"
    );
    const baseURL = configStore.getClientConfig("flite").host;
    try {
      const response = await $axios.get(endpoint, {
        baseURL,
        params: payload,
      });
      commit("SET_RECENT_ACTIVITY_RESPONSE", response);
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getRecentActivityAsync:",
        error
      );
    }
  },
  async deleteRecentActivityAsync({ rootGetters }, { uuid, lang }) {
    const { $axios, $t } = useNuxtApp();
    if (!uuid || !lang) {
      console.error($t("COMMON.MISSING_REQUIRED_PARAMETERS"));
      return;
    }
    const baseUrl = rootGetters["config/getClientConfig"]("flite").host;
    const clientEndpoint = rootGetters["config/getClientEndpoint"](
      "flite",
      "recentActivity"
    );
    const endpoint = `${clientEndpoint}/${uuid}?lang=${lang}`;
    try {
      await $axios.delete(endpoint, {
        baseURL: baseUrl,
      });
    } catch (error) {
      console.error(
        KEYS.KEY_NAMES.ERROR_IN + "getRecentActivityAsync:",
        error
      );
    }
  },
};

const getters = {
  getRecentActivityResponse(state) {
    return state.recentActivityResponse;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
