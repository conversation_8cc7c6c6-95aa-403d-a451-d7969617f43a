import axios from 'axios';
import { useNuxtApp } from '#app';
import { useInnitAuthStore } from "../stores/innit-auth.js";

let FSSClient;
let FMPClient;
let ICLClient;
let fliteClient;
let FCSClient;
let moduleClient;

const { $auth } = useNuxtApp();

function responseInterceptor(client, auth) {
    if (!client || !auth) return;

    client.interceptors.response.use(
        (response) => response,
        async (error) => {
            const code = parseInt(error?.response?.status);

            // Refresh token logic
            if (code === 401) {
                // Handle 401 errors with logout or refresh logic as needed
                return Promise.reject(error);
            } else {
                return Promise.reject(error);
            }
        }
    );
}

async function getAccessToken() {
    return useInnitAuthStore().authorizationToken.value;
}

async function createClient(client, clientConfig, auth) {
    const token = await getAccessToken();
    if (!client) {
        client = axios.create({
            baseURL: clientConfig.host,
            withCredentials: false,
            headers: {
                "Authorization": token ? `${token}` : undefined,
            },
            timeout: 20000
        });
        responseInterceptor(client, auth);
    }
    return client;
}

export async function getFSSClient(store, auth) {
    const clientConfig = store.getters['config/getClientConfig']("fss");
    FSSClient = await createClient(FSSClient, clientConfig, auth);
    return FSSClient;
}

export async function getFMPClient(store, auth) {
    const clientConfig = store.getters['config/getClientConfig']("fmp");
    FMPClient = await createClient(FMPClient, clientConfig, auth);
    return FMPClient;
}

import { useConfigStore } from "../stores/config.js";

export async function getICLClient(store, auth) {
    const configStore = useConfigStore();
    const clientConfig = configStore.getClientConfig("icl");
    ICLClient = await createClient(ICLClient, clientConfig, auth);
    return ICLClient;
}

export async function getFliteClient(store) {
    const configStore = useConfigStore();
    const clientConfig = configStore.getClientConfig("flite");
    fliteClient = await createClient(fliteClient, clientConfig);
    return fliteClient;
}

export async function getModuleClient(store, auth) {
    const configStore = useConfigStore();
    const clientConfig = configStore.getClientConfig("module");
    moduleClient = await createClient(moduleClient, clientConfig, auth);
    return moduleClient;
}

export async function getFCSService(store, auth) {
    const configStore = useConfigStore();
    const clientConfig = configStore.getClientConfig("fcs");
    FCSClient = await createClient(FCSClient, clientConfig, auth);
    return FCSClient;
}

export async function getAuthorizationHeader() {
    const token = await getAccessToken();
    return { "Authorization": token ? `${token}` : undefined };
}
